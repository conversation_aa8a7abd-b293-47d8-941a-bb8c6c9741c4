FROM golang:1.22.6 AS builder

COPY . /src
WORKDIR /src



#RUN GOPROXY=http://192.168.233.28:36944 && make build
#RUN export GOPROXY=http://goproxy.fxeyeinterface.com:8081 &&  make build
# RUN export GOPROXY=https://goproxy.io &&  make build
RUN  make build

FROM debian:stable-slim

ARG WIKI
RUN echo "${WIKI}"

COPY --from=builder /src/bin/${WIKI} /app/app
COPY --from=builder /src/configs/config.yaml /app/data/conf/config.yaml
COPY --from=builder /src/configs/config-dev.yaml /app/data/conf/config-dev.yaml
COPY --from=builder /src/configs/config-pro.yaml /app/data/conf/config-pro.yaml
COPY --from=builder /src/data /app/data
WORKDIR /app

EXPOSE 8080
EXPOSE 9090
VOLUME /data/conf


#CMD ["./app", "-conf", "/data/conf"]
CMD ["./app"]
