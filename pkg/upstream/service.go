package upstream

import (
	"context"
	"fmt"
)

const (
	TransToken  = "2mNpJU4tND"
	TransPrefix = "Wikiglobal"
	TransSuffix = "Translation"
)

func (c *Client) GetTraders(ctx context.Context, req *GetTraderRequest) (*GetTraderReply, error) {
	resp := &GetTraderReply{}
	_, err := c.client.R().
		SetContext(ctx).
		SetBody(req).
		SetResult(resp).
		SetHeaders(headerFromContext(ctx)).
		Post(fmt.Sprintf("%s/wikicore/getMultiple/v2", c.conf.TraderDomain))
	if err != nil {
		return nil, err
	}
	return resp, nil
}
