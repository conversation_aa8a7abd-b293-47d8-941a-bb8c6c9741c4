package upstream

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"strings"
)

const (
	// 交易商API的端点
	Address2GeoEndpoint = "/content_get_trader_or_service_name"
	// 默认的API域名（如果配置中没有设置的话）
	DefaultAddress2GeoDomain = "http://api-address2geo.fxeyeinterface.com:80"

	// fx企业搜索API的端点
	FxEnterpriseSearchEndpoint = "/api/posts/bitgrade/fxenterprisesearch"
)

// GetTraderOrServiceName 调用交易商API，提取交易商或服务名称信息
// 这个API主要用于从给定的文本内容中识别和提取交易商或服务提供商的名称
func (c *Client) GetTraderOrServiceName(ctx context.Context, req *Address2GeoRequest) (*Address2GeoResponse, error) {
	// 参数验证
	if req == nil {
		return nil, fmt.Errorf("请求参数不能为空")
	}
	if req.Content == "" {
		return nil, fmt.Errorf("内容参数不能为空")
	}
	if req.LlmName == "" {
		req.LlmName = "hunyuan" // 使用默认的LLM模型
	}

	// 确定API域名
	domain := c.conf.Address2GeoDomain
	if domain == "" {
		domain = DefaultAddress2GeoDomain
	}

	// 先尝试以字符串形式获取响应
	var responseBody string
	restResp, err := c.client.R().
		SetContext(ctx).
		SetBody(req).
		SetHeaders(headerFromContext(ctx)).
		Post(fmt.Sprintf("%s%s", domain, Address2GeoEndpoint))
	
	if err != nil {
		return nil, fmt.Errorf("API请求失败: %w", err)
	}

	responseBody = string(restResp.Body())

	// 记录API响应内容以便调试
	log.Printf("Address2Geo API响应: %s", responseBody)

	// 准备返回结构
	resp := &Address2GeoResponse{
		Code:    200,
		Message: "成功",
		Data:    make(map[string]interface{}),
	}

	// 尝试解析JSON响应
	var jsonResp map[string]interface{}
	if err := json.Unmarshal(restResp.Body(), &jsonResp); err == nil {
		// 如果是JSON格式，尝试解析为我们的结构
		if code, ok := jsonResp["code"]; ok {
			if c, ok := code.(float64); ok {
				resp.Code = int(c)
			}
		}
		if message, ok := jsonResp["message"]; ok {
			if m, ok := message.(string); ok {
				resp.Message = m
			}
		}
		if data, ok := jsonResp["data"]; ok {
			if d, ok := data.(map[string]interface{}); ok {
				resp.Data = d
			}
		}
	} else {
		// 如果不是JSON格式，说明是纯文本响应，进行简单的文本分析
		resp.Data["raw_response"] = responseBody
		
		// 尝试从响应中提取可能的交易商名称
		// 这里使用简单的文本分析，根据实际API响应格式进行调整
		if responseBody != "" && !strings.Contains(responseBody, "error") {
			// 假设响应是逗号分隔的交易商名称
			names := strings.Split(responseBody, ",")
			var traders []string
			for _, name := range names {
				// 清理字符串：去除空格和引号
				trimmed := strings.TrimSpace(name)
				// 移除可能的双引号和转义引号
				trimmed = strings.Trim(trimmed, `"`)
				trimmed = strings.ReplaceAll(trimmed, `\"`, ``)
				
				if trimmed != "" {
					traders = append(traders, trimmed)
				}
			}
			if len(traders) > 0 {
				resp.Data["traders"] = traders
			}
		}
	}

	return resp, nil
}

// ExtractTraderInfo 从API响应中提取交易商相关信息的便利方法
// 这个方法封装了API调用并提供更方便的接口来提取特定信息
func (c *Client) ExtractTraderInfo(ctx context.Context, content string, llmName string) (map[string]interface{}, error) {
	req := &Address2GeoRequest{
		Content: content,
		LlmName: llmName,
	}

	resp, err := c.GetTraderOrServiceName(ctx, req)
	if err != nil {
		return nil, fmt.Errorf("提取交易商信息失败: %w", err)
	}

	return resp.Data, nil
}
