package upstream

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
)

const (
	// 交易商API的端点
	Address2GeoEndpoint = "/content_get_trader_or_service_name"
	// wikibitsearch搜索API的端点和默认域名
	WikiCoreSearchEndpoint = "/wikicore/search"
)

// GetTraderOrServiceName 调用交易商API，提取交易商或服务名称信息
// 这个API主要用于从给定的文本内容中识别和提取交易商或服务提供商的名称
func (c *Client) GetTraderOrServiceName(ctx context.Context, req *Address2GeoRequest) (*Address2GeoResponse, error) {
	// 参数验证
	if req == nil {
		return nil, fmt.Errorf("请求参数不能为空")
	}
	if req.Content == "" {
		return nil, fmt.Errorf("内容参数不能为空")
	}
	if req.LlmName == "" {
		req.LlmName = "hunyuan" // 使用默认的LLM模型
	}

	// 确定API域名
	domain := c.conf.Address2GeoDomain

	// 发起API请求
	restResp, err := c.client.R().
		SetContext(ctx).
		SetBody(req).
		SetHeaders(headerFromContext(ctx)).
		Post(fmt.Sprintf("%s%s", domain, Address2GeoEndpoint))

	if err != nil {
		return nil, fmt.Errorf("API请求失败: %w", err)
	}

	// 记录API响应内容以便调试
	responseBody := string(restResp.Body())
	log.Printf("Address2Geo API响应: %s", responseBody)

	// 解析新的响应格式: {"data":"ABC","status_code":200,"err_msg":""}
	var resp Address2GeoResponse
	if err := json.Unmarshal(restResp.Body(), &resp); err != nil {
		return nil, fmt.Errorf("解析API响应失败: %w", err)
	}

	return &resp, nil
}

// ExtractTraderInfo 从API响应中提取交易商相关信息的便利方法
// 这个方法封装了API调用并提供更方便的接口来提取特定信息
func (c *Client) ExtractTraderInfo(ctx context.Context, content string, llmName string) (string, error) {
	req := &Address2GeoRequest{
		Content: content,
		LlmName: llmName,
	}

	resp, err := c.GetTraderOrServiceName(ctx, req)
	if err != nil {
		return "", fmt.Errorf("提取交易商信息失败: %w", err)
	}

	// 检查响应状态
	if err := resp.Check(); err != nil {
		return "", err
	}

	return resp.Data, nil
}

// WikiCoreSearch 调用wikibitsearch搜索API，根据关键词搜索交易商信息
// 这个API用于根据关键词搜索交易商的详细信息，包括code、logo、评分等
func (c *Client) WikiCoreSearch(ctx context.Context, req *WikiCoreSearchRequest) (*WikiCoreSearchResponse, error) {
	// 参数验证
	if req == nil {
		return nil, fmt.Errorf("请求参数不能为空")
	}
	if req.Content == "" {
		return nil, fmt.Errorf("搜索内容不能为空")
	}

	// 设置默认值
	if req.Project == 0 {
		req.Project = 1 // wikifx搜索
	}
	if req.Type == 0 {
		req.Type = 1 // 交易商
	}
	if req.PageIndex == 0 {
		req.PageIndex = 1
	}
	if req.PageSize == 0 {
		req.PageSize = 20
	}
	if req.LanguageCode == "" {
		req.LanguageCode = "zh-CN"
	}
	if req.CountryCode == "" {
		req.CountryCode = "156"
	}
	domain := c.conf.WikiCoreSearchDomain
	// 构建请求URL
	url := fmt.Sprintf("%s%s", domain, WikiCoreSearchEndpoint)

	// 设置请求头
	headers := headerFromContext(ctx)
	headers["accept"] = "application/json"
	headers["LanguageCode"] = req.LanguageCode
	headers["CountryCode"] = req.CountryCode

	// 发送GET请求
	restResp, err := c.client.R().
		SetContext(ctx).
		SetHeaders(headers).
		SetQueryParams(map[string]string{
			"content":      req.Content,
			"project":      fmt.Sprintf("%d", req.Project),
			"type":         fmt.Sprintf("%d", req.Type),
			"pageIndex":    fmt.Sprintf("%d", req.PageIndex),
			"pageSize":     fmt.Sprintf("%d", req.PageSize),
			"languageCode": req.LanguageCode,
			"countryCode":  req.CountryCode,
		}).
		SetResult(&WikiCoreSearchResponse{}).
		Get(url)

	if err != nil {
		return nil, fmt.Errorf("wikibitsearch搜索API请求失败: %w", err)
	}

	// 记录API响应内容以便调试
	log.Printf("WikiCoreSearch API响应: %s", string(restResp.Body()))

	// 获取响应结果
	resp, ok := restResp.Result().(*WikiCoreSearchResponse)
	if !ok {
		return nil, fmt.Errorf("响应格式解析失败")
	}

	return resp, nil
}
