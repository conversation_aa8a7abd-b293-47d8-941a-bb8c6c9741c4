package upstream

import (
	"context"
	"testing"
)

func TestFxEnterpriseSearch(t *testing.T) {
	// 创建测试配置
	config := &Config{
		// 使用Mock地址进行测试
	}
	
	// 创建客户端
	client, err := NewClient(config)
	if err != nil {
		t.Fatalf("创建客户端失败: %v", err)
	}
	
	// 测试用例
	testCases := []struct {
		name    string
		request *FxEnterpriseSearchRequest
		wantErr bool
	}{
		{
			name: "正常搜索交易商",
			request: &FxEnterpriseSearchRequest{
				Keyword: "FXTM",
				Type:    2, // 交易商
			},
			wantErr: false,
		},
		{
			name: "搜索服务商",
			request: &FxEnterpriseSearchRequest{
				Keyword: "服务商测试",
				Type:    1, // 服务商
			},
			wantErr: false,
		},
		{
			name: "搜索交易商和服务商",
			request: &FxEnterpriseSearchRequest{
				Keyword: "测试关键词",
				Type:    0, // 交易商服务商
			},
			wantErr: false,
		},
		{
			name: "空关键词应该失败",
			request: &FxEnterpriseSearchRequest{
				Keyword: "",
				Type:    2,
			},
			wantErr: true,
		},
		{
			name: "无效type应该失败",
			request: &FxEnterpriseSearchRequest{
				Keyword: "测试",
				Type:    5, // 无效的type
			},
			wantErr: true,
		},
	}
	
	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			ctx := context.Background()
			resp, err := client.FxEnterpriseSearch(ctx, tc.request)
			
			if tc.wantErr {
				if err == nil {
					t.Errorf("期望出现错误，但没有错误")
				}
				return
			}
			
			if err != nil {
				t.Errorf("不期望出现错误，但出现了错误: %v", err)
				return
			}
			
			if resp == nil {
				t.Errorf("响应不应该为空")
				return
			}
			
			t.Logf("搜索结果: StatusCode=%d, ResultCount=%d", resp.StatusCode, len(resp.Result))
			
			// 如果有结果，打印第一个结果的详细信息
			if len(resp.Result) > 0 {
				first := resp.Result[0]
				t.Logf("第一个结果: Code=%s, ShowName=%s, Logo=%s", 
					first.Code, first.ShowName, first.Logo)
			}
		})
	}
}

func TestFxEnterpriseSearchRequest_Validation(t *testing.T) {
	config := &Config{}
	client, err := NewClient(config)
	if err != nil {
		t.Fatalf("创建客户端失败: %v", err)
	}
	
	ctx := context.Background()
	
	// 测试nil请求
	_, err = client.FxEnterpriseSearch(ctx, nil)
	if err == nil {
		t.Error("nil请求应该返回错误")
	}
	
	// 测试空关键词
	_, err = client.FxEnterpriseSearch(ctx, &FxEnterpriseSearchRequest{
		Keyword: "",
		Type:    2,
	})
	if err == nil {
		t.Error("空关键词应该返回错误")
	}
	
	// 测试无效type
	_, err = client.FxEnterpriseSearch(ctx, &FxEnterpriseSearchRequest{
		Keyword: "test",
		Type:    -1,
	})
	if err == nil {
		t.Error("无效type应该返回错误")
	}
}
