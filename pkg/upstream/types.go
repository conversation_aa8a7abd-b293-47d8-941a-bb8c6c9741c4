package upstream

import "fmt"

// CheckResponse 接口，用于检查响应状态
type CheckResponse interface {
	Check() error
}

// Address2GeoRequest 交易商API请求结构
type Address2GeoRequest struct {
	Content string `json:"content"`  // 需要分析的内容
	LlmName string `json:"llm_name"` // 使用的LLM模型名称
}

// Address2GeoResponse 交易商API响应结构
type Address2GeoResponse struct {
	Code    int                    `json:"code"`    // 响应状态码
	Message string                 `json:"message"` // 响应消息
	Data    map[string]interface{} `json:"data"`    // 响应数据
}

// Check 实现CheckResponse接口
func (r *Address2GeoResponse) Check() error {
	if r.Code != 0 && r.Code != 200 {
		return fmt.Errorf("API请求失败，状态码: %d, 消息: %s", r.Code, r.Message)
	}
	return nil
}

