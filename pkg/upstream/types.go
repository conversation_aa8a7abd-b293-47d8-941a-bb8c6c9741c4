package upstream

import "fmt"

// CheckResponse 接口，用于检查响应状态
type CheckResponse interface {
	Check() error
}

// Address2GeoRequest 交易商API请求结构
type Address2GeoRequest struct {
	Content string `json:"content"`  // 需要分析的内容
	LlmName string `json:"llm_name"` // 使用的LLM模型名称
}

// Address2GeoResponse 交易商API响应结构
// 匹配新的 API 响应格式: {"data":"ABC","status_code":200,"err_msg":""}
type Address2GeoResponse struct {
	Data       string `json:"data"`        // 响应数据，现在是字符串类型
	StatusCode int    `json:"status_code"` // 响应状态码
	ErrMsg     string `json:"err_msg"`     // 错误消息
}

// Check 实现CheckResponse接口
func (r *Address2GeoResponse) Check() error {
	if r.StatusCode != 200 {
		errMsg := r.ErrMsg
		if errMsg == "" {
			errMsg = "未知错误"
		}
		return fmt.Errorf("API请求失败，状态码: %d, 错误信息: %s", r.StatusCode, errMsg)
	}
	return nil
}

// WikiCoreSearchRequest wikibitsearch搜索API请求结构
type WikiCoreSearchRequest struct {
	Content      string `json:"content"`      // 搜索条件/关键词
	Project      int32  `json:"project"`      // 项目类型，使用1(wikifx搜索)
	Type         int32  `json:"type"`         // 搜索类型，使用1(交易商)
	PageIndex    int32  `json:"pageIndex"`    // 页码，默认1
	PageSize     int32  `json:"pageSize"`     // 页大小，默认20
	LanguageCode string `json:"languageCode"` // 语言，默认"zh-CN"
	CountryCode  string `json:"countryCode"`  // 国家，默认"156"
}

// WikiCoreSearchItem 搜索结果项
type WikiCoreSearchItem struct {
	Code             string  `json:"code"`
	ShowName         string  `json:"showName"`
	Logo             string  `json:"logo"`
	Score            float64 `json:"score"`
	LocalShortName   string  `json:"localShortName"`
	LocalFullName    string  `json:"localFullName"`
	EnglishShortName string  `json:"englishShortName"`
	EnglishFullName  string  `json:"englishFullName"`
	Annotation       string  `json:"annotation"`
	Color            string  `json:"color"`
	Project          int32   `json:"project"`
	Type             int32   `json:"type"`
	KbScore          string  `json:"kbscore"`
}

// WikiCoreSearchResult 搜索结果
type WikiCoreSearchResult struct {
	Items      []WikiCoreSearchItem `json:"items"`
	TotalCount int32                `json:"totalCount"`
	PageIndex  int32                `json:"pageIndex"`
	PageSize   int32                `json:"pageSize"`
}

// WikiCoreSearchResponse wikibitsearch搜索API响应结构
type WikiCoreSearchResponse struct {
	StatusCode int32                `json:"statusCode"`
	Result     WikiCoreSearchResult `json:"result"`
	Code       string               `json:"code"`
	Succeed    bool                 `json:"succeed"`
	Message    string               `json:"message"`
	Extra      interface{}          `json:"extra"`
	Timestamp  int64                `json:"timestamp"`
}

// Check 实现CheckResponse接口
func (r *WikiCoreSearchResponse) Check() error {
	if !r.Succeed {
		return fmt.Errorf("wikibitsearch搜索API请求失败，消息: %s", r.Message)
	}
	return nil
}

// FxEnterpriseSearchRequest fx企业搜索API请求结构
type FxEnterpriseSearchRequest struct {
	Keyword string `json:"keyword"` // 关键词
	Type    int32  `json:"type"`    // 0 交易商服务商 1服务商 2交易商
}

// FxEnterpriseSearchLabel 标签信息
type FxEnterpriseSearchLabel struct {
	Type int32                         `json:"type"`
	Data []FxEnterpriseSearchLabelData `json:"data"`
}

// FxEnterpriseSearchLabelData 标签数据
type FxEnterpriseSearchLabelData struct {
	LabelName string `json:"labelName"`
}
