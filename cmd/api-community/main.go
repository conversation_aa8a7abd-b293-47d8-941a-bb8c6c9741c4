package main

import (
	"api-community/internal/conf"
	"api-community/internal/tool/config"
	"api-community/internal/tool/env"
	"encoding/json"
	"flag"
	"os"
	//iwgrp1 "api-community/internal/tool/grpc"
	"api-community/internal/tool/ilog"
	"github.com/go-kratos/kratos/v2"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/transport/grpc"
	"github.com/go-kratos/kratos/v2/transport/http"
	_ "go.uber.org/automaxprocs"
)

var (
	Name     = env.GetServiceName()
	Version  string
	flagconf string
	id, _    = os.Hostname()
)

func init() {
	//flag.StringVar(&flagconf, "conf", "../../configs", "config path, eg: -conf config.yaml")
	//
	//	ENVIRONMENT := os.Getenv("ENVIRONMENT")
	//	if ENVIRONMENT == "local" {
	//		flag.StringVar(&flagconf, "conf", "../../configs/config.yaml", "config path, eg: -conf config.yaml")
	//	} else if ENVIRONMENT == "SG" || ENVIRONMENT == "SH" {
	//		flag.StringVar(&flagconf, "conf", "../../configs/config-pro.yaml", "config path, eg: -conf config.yaml")
	//	} else {
	//		flag.StringVar(&flagconf, "conf", "../../configs/config-dev.yaml", "config path, eg: -conf config.yaml")
	//	}
	//	fmt.Println(flagconf)

	ENVIRONMENT := os.Getenv("ENVIRONMENT")
	if ENVIRONMENT == "" || ENVIRONMENT == "local" {
		flagconf = "./configs/config.yaml"
	} else if ENVIRONMENT == "SG" || ENVIRONMENT == "SH" {
		flagconf = "./data/conf/config-pro.yaml"
	} else {
		flagconf = "./data/conf/config-dev.yaml"
	}
}

func newApp(logger log.Logger, gs *grpc.Server, hs *http.Server) *kratos.App {
	opts := []kratos.Option{
		kratos.ID(id),
		kratos.Name(Name),
		kratos.Version(Version),
		kratos.Metadata(map[string]string{}),
		kratos.Logger(logger),
		kratos.Server(
			gs,
			hs,
		),
	}

	//_, ok := os.LookupEnv("KUBERNETES_SERVICE_HOST")
	//if ok {
	//	clientSet, err := k8s.NewClient()
	//	if err != nil {
	//		panic(err)
	//	}
	//
	//	opts = append(opts, kratos.Registrar(registry.NewRegistry(clientSet, logger)))
	//}

	return kratos.New(opts...)
}

func main() {
	flag.Parse()
	logger, closer := ilog.NewLogger(id, Name)
	defer closer.Close()
	var bc conf.Bootstrap
	_, err := config.LoadConfig(&bc, config.WithFilePath(flagconf))
	if err != nil {
		panic(err)
	}

	bcStr, _ := json.Marshal(&bc)
	_ = logger.Log(log.LevelInfo, "config:", string(bcStr))
	app, cleanup, err := wireApp(bc.Server, bc.Data, bc.Business, logger)
	if err != nil {
		panic(err)
	}
	defer cleanup()

	if err = app.Run(); err != nil {
		panic(err)
	}
}
