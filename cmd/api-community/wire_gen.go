// Code generated by Wire. DO NOT EDIT.

//go:generate go run github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package main

import (
	"api-community/api/common"
	"api-community/internal/conf"
	"api-community/internal/dao"
	"api-community/internal/server"
	"api-community/internal/service"
	"github.com/go-kratos/kratos/v2"
	"github.com/go-kratos/kratos/v2/log"
)

import (
	_ "go.uber.org/automaxprocs"
)

// Injectors from wire.go:

// wireApp init kratos application.
func wireApp(serverConfig *common.ServerConfig, dataConfig *common.DataConfig, business *conf.Business, logger log.Logger) (*kratos.App, func(), error) {
	db, cleanup, err := dao.NewMySQL(dataConfig, logger)
	if err != nil {
		return nil, nil, err
	}
	client, cleanup2, err := dao.NewRedis(dataConfig, logger)
	if err != nil {
		cleanup()
		return nil, nil, err
	}
	user := dao.NewUser(db, client)

	//conn := iwgrp1.DialInsecureWithShort(context.Background(), (business.GrpcUrl.Usercenter))
	//serviceService := service.NewWikiCommunityService(user, business, pb.NewUserCenterClient(conn))
	serviceService := service.NewWikiCommunityService(user, business)
	grpcServer := server.NewGRPCServer(serverConfig, serviceService, logger)
	httpServer := server.NewHTTPServer(serverConfig, serviceService, logger)
	app := newApp(logger, grpcServer, httpServer)
	return app, func() {
		cleanup2()
		cleanup()
	}, nil
}
