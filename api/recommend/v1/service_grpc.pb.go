// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.25.3
// source: recommend/v1/service.proto

package v1

import (
	common "api-community/api/common"
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Service_Healthy_FullMethodName                 = "/api.recommend.v1.Service/Healthy"
	Service_Recommend_FullMethodName               = "/api.recommend.v1.Service/Recommend"
	Service_RecommendV2_FullMethodName             = "/api.recommend.v1.Service/RecommendV2"
	Service_FindCommerceByCategory_FullMethodName  = "/api.recommend.v1.Service/FindCommerceByCategory"
	Service_FindHotAndNew_FullMethodName           = "/api.recommend.v1.Service/FindHotAndNew"
	Service_FindHotAndNewV2_FullMethodName         = "/api.recommend.v1.Service/FindHotAndNewV2"
	Service_FindFollowPublish_FullMethodName       = "/api.recommend.v1.Service/FindFollowPublish"
	Service_TraderHome_FullMethodName              = "/api.recommend.v1.Service/TraderHome"
	Service_TraderPostCount_FullMethodName         = "/api.recommend.v1.Service/TraderPostCount"
	Service_Search_FullMethodName                  = "/api.recommend.v1.Service/Search"
	Service_FindSearchTitle_FullMethodName         = "/api.recommend.v1.Service/FindSearchTitle"
	Service_InterestedUser_FullMethodName          = "/api.recommend.v1.Service/InterestedUser"
	Service_FindHotContent_FullMethodName          = "/api.recommend.v1.Service/FindHotContent"
	Service_RecommendUser_FullMethodName           = "/api.recommend.v1.Service/RecommendUser"
	Service_SkyLineActivity_FullMethodName         = "/api.recommend.v1.Service/SkyLineActivity"
	Service_YearlyReport_FullMethodName            = "/api.recommend.v1.Service/YearlyReport"
	Service_RecommendFeedback_FullMethodName       = "/api.recommend.v1.Service/RecommendFeedback"
	Service_HotContentRanking_FullMethodName       = "/api.recommend.v1.Service/HotContentRanking"
	Service_RankingScope_FullMethodName            = "/api.recommend.v1.Service/RankingScope"
	Service_CreatorRanking_FullMethodName          = "/api.recommend.v1.Service/CreatorRanking"
	Service_CreatorRankingNotice_FullMethodName    = "/api.recommend.v1.Service/CreatorRankingNotice"
	Service_SearchTitleAutoComplete_FullMethodName = "/api.recommend.v1.Service/SearchTitleAutoComplete"
	Service_RecommendTest_FullMethodName           = "/api.recommend.v1.Service/RecommendTest"
	Service_ContentSimilarity_FullMethodName       = "/api.recommend.v1.Service/ContentSimilarity"
	Service_UserContent_FullMethodName             = "/api.recommend.v1.Service/UserContent"
	Service_UserBehavior_FullMethodName            = "/api.recommend.v1.Service/UserBehavior"
	Service_UserOriginBehavior_FullMethodName      = "/api.recommend.v1.Service/UserOriginBehavior"
	Service_UserVector_FullMethodName              = "/api.recommend.v1.Service/UserVector"
	Service_UserRealVector_FullMethodName          = "/api.recommend.v1.Service/UserRealVector"
	Service_ResetUserBehavior_FullMethodName       = "/api.recommend.v1.Service/ResetUserBehavior"
)

// ServiceClient is the client API for Service service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ServiceClient interface {
	// 健康检查
	Healthy(ctx context.Context, in *common.EmptyRequest, opts ...grpc.CallOption) (*common.HealthyReply, error)
	// 推荐
	Recommend(ctx context.Context, in *RecommendRequest, opts ...grpc.CallOption) (*RecommendReply, error)
	// 推荐v2版本
	RecommendV2(ctx context.Context, in *RecommendV2Request, opts ...grpc.CallOption) (*RecommendReply, error)
	// 商业按照分类获取推荐
	FindCommerceByCategory(ctx context.Context, in *FindCommerceByCategoryRequest, opts ...grpc.CallOption) (*FindCommerceByCategoryReply, error)
	// 热门和最新
	FindHotAndNew(ctx context.Context, in *FindHotAndNewRequest, opts ...grpc.CallOption) (*FindHotAndNewReply, error)
	// 热门和最新
	FindHotAndNewV2(ctx context.Context, in *FindHotAndNewRequest, opts ...grpc.CallOption) (*FindHotAndNewV2Reply, error)
	// 关注对象内容列表
	FindFollowPublish(ctx context.Context, in *FindFollowPublishRequest, opts ...grpc.CallOption) (*FindFollowPublishReply, error)
	// 交易商主页内容
	TraderHome(ctx context.Context, in *TraderHomeRequest, opts ...grpc.CallOption) (*TraderHomeReply, error)
	// 交易商帖子总数量
	TraderPostCount(ctx context.Context, in *TraderPostCountRequest, opts ...grpc.CallOption) (*TraderPostCountReply, error)
	// 内容搜索接口
	Search(ctx context.Context, in *SearchRequest, opts ...grpc.CallOption) (*SearchReply, error)
	// 查找用户搜索标题内容
	FindSearchTitle(ctx context.Context, in *FindSearchTitleRequest, opts ...grpc.CallOption) (*FindSearchTitleReply, error)
	// 感兴趣的用户
	InterestedUser(ctx context.Context, in *InterestedUserRequest, opts ...grpc.CallOption) (*InterestedUserReply, error)
	// 热门内容
	FindHotContent(ctx context.Context, in *FindHotContentRequest, opts ...grpc.CallOption) (*FindHotContentReply, error)
	// 推荐用户
	RecommendUser(ctx context.Context, in *RecommendUserRequest, opts ...grpc.CallOption) (*RecommendUserReply, error)
	// 获取排行
	SkyLineActivity(ctx context.Context, in *ActivityRequest, opts ...grpc.CallOption) (*ActivityReply, error)
	// 年度报告
	YearlyReport(ctx context.Context, in *YearlyReportRequest, opts ...grpc.CallOption) (*YearlyReportReply, error)
	// 推荐用户反馈
	RecommendFeedback(ctx context.Context, in *RecommendFeedbackRequest, opts ...grpc.CallOption) (*RecommendFeedbackResponse, error)
	// 热帖排行
	HotContentRanking(ctx context.Context, in *HotContentRankingRequest, opts ...grpc.CallOption) (*HotContentRankingResponse, error)
	// 排行榜范围
	RankingScope(ctx context.Context, in *RankingScopeRequest, opts ...grpc.CallOption) (*RankingScopeResponse, error)
	// 用户排行
	CreatorRanking(ctx context.Context, in *CreatorRankingRequest, opts ...grpc.CallOption) (*CreatorRankingResponse, error)
	// 用户排行通知
	CreatorRankingNotice(ctx context.Context, in *CreatorRankingNoticeRequest, opts ...grpc.CallOption) (*CreatorRankingNoticeResponse, error)
	// 文章标题搜索自动补全
	SearchTitleAutoComplete(ctx context.Context, in *SearchTitleAutoCompleteRequest, opts ...grpc.CallOption) (*SearchTitleAutoCompleteReply, error)
	// ------------------------- 下面接口内部自测使用 -----------------------------------------
	RecommendTest(ctx context.Context, in *RecommendTestRequest, opts ...grpc.CallOption) (*RecommendTestReply, error)
	ContentSimilarity(ctx context.Context, in *ContentSimilarityRequest, opts ...grpc.CallOption) (*ContentSimilarityReply, error)
	UserContent(ctx context.Context, in *UserContentRequest, opts ...grpc.CallOption) (*RecommendTestReply, error)
	UserBehavior(ctx context.Context, in *UserBehaviorRequest, opts ...grpc.CallOption) (*UserBehaviorReply, error)
	UserOriginBehavior(ctx context.Context, in *UserOriginBehaviorRequest, opts ...grpc.CallOption) (*UserOriginBehaviorReply, error)
	UserVector(ctx context.Context, in *UserVectorRequest, opts ...grpc.CallOption) (*UserVectorReply, error)
	UserRealVector(ctx context.Context, in *UserVectorRequest, opts ...grpc.CallOption) (*UserVectorReply, error)
	ResetUserBehavior(ctx context.Context, in *ResetUserBehaviorRequest, opts ...grpc.CallOption) (*ResetUserBehaviorReply, error)
}

type serviceClient struct {
	cc grpc.ClientConnInterface
}

func NewServiceClient(cc grpc.ClientConnInterface) ServiceClient {
	return &serviceClient{cc}
}

func (c *serviceClient) Healthy(ctx context.Context, in *common.EmptyRequest, opts ...grpc.CallOption) (*common.HealthyReply, error) {
	out := new(common.HealthyReply)
	err := c.cc.Invoke(ctx, Service_Healthy_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) Recommend(ctx context.Context, in *RecommendRequest, opts ...grpc.CallOption) (*RecommendReply, error) {
	out := new(RecommendReply)
	err := c.cc.Invoke(ctx, Service_Recommend_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) RecommendV2(ctx context.Context, in *RecommendV2Request, opts ...grpc.CallOption) (*RecommendReply, error) {
	out := new(RecommendReply)
	err := c.cc.Invoke(ctx, Service_RecommendV2_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) FindCommerceByCategory(ctx context.Context, in *FindCommerceByCategoryRequest, opts ...grpc.CallOption) (*FindCommerceByCategoryReply, error) {
	out := new(FindCommerceByCategoryReply)
	err := c.cc.Invoke(ctx, Service_FindCommerceByCategory_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) FindHotAndNew(ctx context.Context, in *FindHotAndNewRequest, opts ...grpc.CallOption) (*FindHotAndNewReply, error) {
	out := new(FindHotAndNewReply)
	err := c.cc.Invoke(ctx, Service_FindHotAndNew_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) FindHotAndNewV2(ctx context.Context, in *FindHotAndNewRequest, opts ...grpc.CallOption) (*FindHotAndNewV2Reply, error) {
	out := new(FindHotAndNewV2Reply)
	err := c.cc.Invoke(ctx, Service_FindHotAndNewV2_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) FindFollowPublish(ctx context.Context, in *FindFollowPublishRequest, opts ...grpc.CallOption) (*FindFollowPublishReply, error) {
	out := new(FindFollowPublishReply)
	err := c.cc.Invoke(ctx, Service_FindFollowPublish_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) TraderHome(ctx context.Context, in *TraderHomeRequest, opts ...grpc.CallOption) (*TraderHomeReply, error) {
	out := new(TraderHomeReply)
	err := c.cc.Invoke(ctx, Service_TraderHome_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) TraderPostCount(ctx context.Context, in *TraderPostCountRequest, opts ...grpc.CallOption) (*TraderPostCountReply, error) {
	out := new(TraderPostCountReply)
	err := c.cc.Invoke(ctx, Service_TraderPostCount_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) Search(ctx context.Context, in *SearchRequest, opts ...grpc.CallOption) (*SearchReply, error) {
	out := new(SearchReply)
	err := c.cc.Invoke(ctx, Service_Search_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) FindSearchTitle(ctx context.Context, in *FindSearchTitleRequest, opts ...grpc.CallOption) (*FindSearchTitleReply, error) {
	out := new(FindSearchTitleReply)
	err := c.cc.Invoke(ctx, Service_FindSearchTitle_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) InterestedUser(ctx context.Context, in *InterestedUserRequest, opts ...grpc.CallOption) (*InterestedUserReply, error) {
	out := new(InterestedUserReply)
	err := c.cc.Invoke(ctx, Service_InterestedUser_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) FindHotContent(ctx context.Context, in *FindHotContentRequest, opts ...grpc.CallOption) (*FindHotContentReply, error) {
	out := new(FindHotContentReply)
	err := c.cc.Invoke(ctx, Service_FindHotContent_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) RecommendUser(ctx context.Context, in *RecommendUserRequest, opts ...grpc.CallOption) (*RecommendUserReply, error) {
	out := new(RecommendUserReply)
	err := c.cc.Invoke(ctx, Service_RecommendUser_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) SkyLineActivity(ctx context.Context, in *ActivityRequest, opts ...grpc.CallOption) (*ActivityReply, error) {
	out := new(ActivityReply)
	err := c.cc.Invoke(ctx, Service_SkyLineActivity_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) YearlyReport(ctx context.Context, in *YearlyReportRequest, opts ...grpc.CallOption) (*YearlyReportReply, error) {
	out := new(YearlyReportReply)
	err := c.cc.Invoke(ctx, Service_YearlyReport_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) RecommendFeedback(ctx context.Context, in *RecommendFeedbackRequest, opts ...grpc.CallOption) (*RecommendFeedbackResponse, error) {
	out := new(RecommendFeedbackResponse)
	err := c.cc.Invoke(ctx, Service_RecommendFeedback_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) HotContentRanking(ctx context.Context, in *HotContentRankingRequest, opts ...grpc.CallOption) (*HotContentRankingResponse, error) {
	out := new(HotContentRankingResponse)
	err := c.cc.Invoke(ctx, Service_HotContentRanking_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) RankingScope(ctx context.Context, in *RankingScopeRequest, opts ...grpc.CallOption) (*RankingScopeResponse, error) {
	out := new(RankingScopeResponse)
	err := c.cc.Invoke(ctx, Service_RankingScope_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) CreatorRanking(ctx context.Context, in *CreatorRankingRequest, opts ...grpc.CallOption) (*CreatorRankingResponse, error) {
	out := new(CreatorRankingResponse)
	err := c.cc.Invoke(ctx, Service_CreatorRanking_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) CreatorRankingNotice(ctx context.Context, in *CreatorRankingNoticeRequest, opts ...grpc.CallOption) (*CreatorRankingNoticeResponse, error) {
	out := new(CreatorRankingNoticeResponse)
	err := c.cc.Invoke(ctx, Service_CreatorRankingNotice_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) SearchTitleAutoComplete(ctx context.Context, in *SearchTitleAutoCompleteRequest, opts ...grpc.CallOption) (*SearchTitleAutoCompleteReply, error) {
	out := new(SearchTitleAutoCompleteReply)
	err := c.cc.Invoke(ctx, Service_SearchTitleAutoComplete_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) RecommendTest(ctx context.Context, in *RecommendTestRequest, opts ...grpc.CallOption) (*RecommendTestReply, error) {
	out := new(RecommendTestReply)
	err := c.cc.Invoke(ctx, Service_RecommendTest_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) ContentSimilarity(ctx context.Context, in *ContentSimilarityRequest, opts ...grpc.CallOption) (*ContentSimilarityReply, error) {
	out := new(ContentSimilarityReply)
	err := c.cc.Invoke(ctx, Service_ContentSimilarity_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) UserContent(ctx context.Context, in *UserContentRequest, opts ...grpc.CallOption) (*RecommendTestReply, error) {
	out := new(RecommendTestReply)
	err := c.cc.Invoke(ctx, Service_UserContent_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) UserBehavior(ctx context.Context, in *UserBehaviorRequest, opts ...grpc.CallOption) (*UserBehaviorReply, error) {
	out := new(UserBehaviorReply)
	err := c.cc.Invoke(ctx, Service_UserBehavior_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) UserOriginBehavior(ctx context.Context, in *UserOriginBehaviorRequest, opts ...grpc.CallOption) (*UserOriginBehaviorReply, error) {
	out := new(UserOriginBehaviorReply)
	err := c.cc.Invoke(ctx, Service_UserOriginBehavior_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) UserVector(ctx context.Context, in *UserVectorRequest, opts ...grpc.CallOption) (*UserVectorReply, error) {
	out := new(UserVectorReply)
	err := c.cc.Invoke(ctx, Service_UserVector_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) UserRealVector(ctx context.Context, in *UserVectorRequest, opts ...grpc.CallOption) (*UserVectorReply, error) {
	out := new(UserVectorReply)
	err := c.cc.Invoke(ctx, Service_UserRealVector_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) ResetUserBehavior(ctx context.Context, in *ResetUserBehaviorRequest, opts ...grpc.CallOption) (*ResetUserBehaviorReply, error) {
	out := new(ResetUserBehaviorReply)
	err := c.cc.Invoke(ctx, Service_ResetUserBehavior_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ServiceServer is the server API for Service service.
// All implementations must embed UnimplementedServiceServer
// for forward compatibility
type ServiceServer interface {
	// 健康检查
	Healthy(context.Context, *common.EmptyRequest) (*common.HealthyReply, error)
	// 推荐
	Recommend(context.Context, *RecommendRequest) (*RecommendReply, error)
	// 推荐v2版本
	RecommendV2(context.Context, *RecommendV2Request) (*RecommendReply, error)
	// 商业按照分类获取推荐
	FindCommerceByCategory(context.Context, *FindCommerceByCategoryRequest) (*FindCommerceByCategoryReply, error)
	// 热门和最新
	FindHotAndNew(context.Context, *FindHotAndNewRequest) (*FindHotAndNewReply, error)
	// 热门和最新
	FindHotAndNewV2(context.Context, *FindHotAndNewRequest) (*FindHotAndNewV2Reply, error)
	// 关注对象内容列表
	FindFollowPublish(context.Context, *FindFollowPublishRequest) (*FindFollowPublishReply, error)
	// 交易商主页内容
	TraderHome(context.Context, *TraderHomeRequest) (*TraderHomeReply, error)
	// 交易商帖子总数量
	TraderPostCount(context.Context, *TraderPostCountRequest) (*TraderPostCountReply, error)
	// 内容搜索接口
	Search(context.Context, *SearchRequest) (*SearchReply, error)
	// 查找用户搜索标题内容
	FindSearchTitle(context.Context, *FindSearchTitleRequest) (*FindSearchTitleReply, error)
	// 感兴趣的用户
	InterestedUser(context.Context, *InterestedUserRequest) (*InterestedUserReply, error)
	// 热门内容
	FindHotContent(context.Context, *FindHotContentRequest) (*FindHotContentReply, error)
	// 推荐用户
	RecommendUser(context.Context, *RecommendUserRequest) (*RecommendUserReply, error)
	// 获取排行
	SkyLineActivity(context.Context, *ActivityRequest) (*ActivityReply, error)
	// 年度报告
	YearlyReport(context.Context, *YearlyReportRequest) (*YearlyReportReply, error)
	// 推荐用户反馈
	RecommendFeedback(context.Context, *RecommendFeedbackRequest) (*RecommendFeedbackResponse, error)
	// 热帖排行
	HotContentRanking(context.Context, *HotContentRankingRequest) (*HotContentRankingResponse, error)
	// 排行榜范围
	RankingScope(context.Context, *RankingScopeRequest) (*RankingScopeResponse, error)
	// 用户排行
	CreatorRanking(context.Context, *CreatorRankingRequest) (*CreatorRankingResponse, error)
	// 用户排行通知
	CreatorRankingNotice(context.Context, *CreatorRankingNoticeRequest) (*CreatorRankingNoticeResponse, error)
	// 文章标题搜索自动补全
	SearchTitleAutoComplete(context.Context, *SearchTitleAutoCompleteRequest) (*SearchTitleAutoCompleteReply, error)
	// ------------------------- 下面接口内部自测使用 -----------------------------------------
	RecommendTest(context.Context, *RecommendTestRequest) (*RecommendTestReply, error)
	ContentSimilarity(context.Context, *ContentSimilarityRequest) (*ContentSimilarityReply, error)
	UserContent(context.Context, *UserContentRequest) (*RecommendTestReply, error)
	UserBehavior(context.Context, *UserBehaviorRequest) (*UserBehaviorReply, error)
	UserOriginBehavior(context.Context, *UserOriginBehaviorRequest) (*UserOriginBehaviorReply, error)
	UserVector(context.Context, *UserVectorRequest) (*UserVectorReply, error)
	UserRealVector(context.Context, *UserVectorRequest) (*UserVectorReply, error)
	ResetUserBehavior(context.Context, *ResetUserBehaviorRequest) (*ResetUserBehaviorReply, error)
	mustEmbedUnimplementedServiceServer()
}

// UnimplementedServiceServer must be embedded to have forward compatible implementations.
type UnimplementedServiceServer struct {
}

func (UnimplementedServiceServer) Healthy(context.Context, *common.EmptyRequest) (*common.HealthyReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Healthy not implemented")
}
func (UnimplementedServiceServer) Recommend(context.Context, *RecommendRequest) (*RecommendReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Recommend not implemented")
}
func (UnimplementedServiceServer) RecommendV2(context.Context, *RecommendV2Request) (*RecommendReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RecommendV2 not implemented")
}
func (UnimplementedServiceServer) FindCommerceByCategory(context.Context, *FindCommerceByCategoryRequest) (*FindCommerceByCategoryReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindCommerceByCategory not implemented")
}
func (UnimplementedServiceServer) FindHotAndNew(context.Context, *FindHotAndNewRequest) (*FindHotAndNewReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindHotAndNew not implemented")
}
func (UnimplementedServiceServer) FindHotAndNewV2(context.Context, *FindHotAndNewRequest) (*FindHotAndNewV2Reply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindHotAndNewV2 not implemented")
}
func (UnimplementedServiceServer) FindFollowPublish(context.Context, *FindFollowPublishRequest) (*FindFollowPublishReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindFollowPublish not implemented")
}
func (UnimplementedServiceServer) TraderHome(context.Context, *TraderHomeRequest) (*TraderHomeReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TraderHome not implemented")
}
func (UnimplementedServiceServer) TraderPostCount(context.Context, *TraderPostCountRequest) (*TraderPostCountReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TraderPostCount not implemented")
}
func (UnimplementedServiceServer) Search(context.Context, *SearchRequest) (*SearchReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Search not implemented")
}
func (UnimplementedServiceServer) FindSearchTitle(context.Context, *FindSearchTitleRequest) (*FindSearchTitleReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindSearchTitle not implemented")
}
func (UnimplementedServiceServer) InterestedUser(context.Context, *InterestedUserRequest) (*InterestedUserReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method InterestedUser not implemented")
}
func (UnimplementedServiceServer) FindHotContent(context.Context, *FindHotContentRequest) (*FindHotContentReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindHotContent not implemented")
}
func (UnimplementedServiceServer) RecommendUser(context.Context, *RecommendUserRequest) (*RecommendUserReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RecommendUser not implemented")
}
func (UnimplementedServiceServer) SkyLineActivity(context.Context, *ActivityRequest) (*ActivityReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SkyLineActivity not implemented")
}
func (UnimplementedServiceServer) YearlyReport(context.Context, *YearlyReportRequest) (*YearlyReportReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method YearlyReport not implemented")
}
func (UnimplementedServiceServer) RecommendFeedback(context.Context, *RecommendFeedbackRequest) (*RecommendFeedbackResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RecommendFeedback not implemented")
}
func (UnimplementedServiceServer) HotContentRanking(context.Context, *HotContentRankingRequest) (*HotContentRankingResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method HotContentRanking not implemented")
}
func (UnimplementedServiceServer) RankingScope(context.Context, *RankingScopeRequest) (*RankingScopeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RankingScope not implemented")
}
func (UnimplementedServiceServer) CreatorRanking(context.Context, *CreatorRankingRequest) (*CreatorRankingResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreatorRanking not implemented")
}
func (UnimplementedServiceServer) CreatorRankingNotice(context.Context, *CreatorRankingNoticeRequest) (*CreatorRankingNoticeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreatorRankingNotice not implemented")
}
func (UnimplementedServiceServer) SearchTitleAutoComplete(context.Context, *SearchTitleAutoCompleteRequest) (*SearchTitleAutoCompleteReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchTitleAutoComplete not implemented")
}
func (UnimplementedServiceServer) RecommendTest(context.Context, *RecommendTestRequest) (*RecommendTestReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RecommendTest not implemented")
}
func (UnimplementedServiceServer) ContentSimilarity(context.Context, *ContentSimilarityRequest) (*ContentSimilarityReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ContentSimilarity not implemented")
}
func (UnimplementedServiceServer) UserContent(context.Context, *UserContentRequest) (*RecommendTestReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UserContent not implemented")
}
func (UnimplementedServiceServer) UserBehavior(context.Context, *UserBehaviorRequest) (*UserBehaviorReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UserBehavior not implemented")
}
func (UnimplementedServiceServer) UserOriginBehavior(context.Context, *UserOriginBehaviorRequest) (*UserOriginBehaviorReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UserOriginBehavior not implemented")
}
func (UnimplementedServiceServer) UserVector(context.Context, *UserVectorRequest) (*UserVectorReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UserVector not implemented")
}
func (UnimplementedServiceServer) UserRealVector(context.Context, *UserVectorRequest) (*UserVectorReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UserRealVector not implemented")
}
func (UnimplementedServiceServer) ResetUserBehavior(context.Context, *ResetUserBehaviorRequest) (*ResetUserBehaviorReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ResetUserBehavior not implemented")
}
func (UnimplementedServiceServer) mustEmbedUnimplementedServiceServer() {}

// UnsafeServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ServiceServer will
// result in compilation errors.
type UnsafeServiceServer interface {
	mustEmbedUnimplementedServiceServer()
}

func RegisterServiceServer(s grpc.ServiceRegistrar, srv ServiceServer) {
	s.RegisterService(&Service_ServiceDesc, srv)
}

func _Service_Healthy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(common.EmptyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).Healthy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_Healthy_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).Healthy(ctx, req.(*common.EmptyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_Recommend_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RecommendRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).Recommend(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_Recommend_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).Recommend(ctx, req.(*RecommendRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_RecommendV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RecommendV2Request)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).RecommendV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_RecommendV2_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).RecommendV2(ctx, req.(*RecommendV2Request))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_FindCommerceByCategory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindCommerceByCategoryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).FindCommerceByCategory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_FindCommerceByCategory_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).FindCommerceByCategory(ctx, req.(*FindCommerceByCategoryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_FindHotAndNew_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindHotAndNewRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).FindHotAndNew(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_FindHotAndNew_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).FindHotAndNew(ctx, req.(*FindHotAndNewRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_FindHotAndNewV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindHotAndNewRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).FindHotAndNewV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_FindHotAndNewV2_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).FindHotAndNewV2(ctx, req.(*FindHotAndNewRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_FindFollowPublish_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindFollowPublishRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).FindFollowPublish(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_FindFollowPublish_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).FindFollowPublish(ctx, req.(*FindFollowPublishRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_TraderHome_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TraderHomeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).TraderHome(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_TraderHome_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).TraderHome(ctx, req.(*TraderHomeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_TraderPostCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TraderPostCountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).TraderPostCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_TraderPostCount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).TraderPostCount(ctx, req.(*TraderPostCountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_Search_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).Search(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_Search_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).Search(ctx, req.(*SearchRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_FindSearchTitle_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindSearchTitleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).FindSearchTitle(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_FindSearchTitle_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).FindSearchTitle(ctx, req.(*FindSearchTitleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_InterestedUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InterestedUserRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).InterestedUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_InterestedUser_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).InterestedUser(ctx, req.(*InterestedUserRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_FindHotContent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindHotContentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).FindHotContent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_FindHotContent_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).FindHotContent(ctx, req.(*FindHotContentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_RecommendUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RecommendUserRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).RecommendUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_RecommendUser_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).RecommendUser(ctx, req.(*RecommendUserRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_SkyLineActivity_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ActivityRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).SkyLineActivity(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_SkyLineActivity_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).SkyLineActivity(ctx, req.(*ActivityRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_YearlyReport_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(YearlyReportRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).YearlyReport(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_YearlyReport_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).YearlyReport(ctx, req.(*YearlyReportRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_RecommendFeedback_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RecommendFeedbackRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).RecommendFeedback(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_RecommendFeedback_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).RecommendFeedback(ctx, req.(*RecommendFeedbackRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_HotContentRanking_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HotContentRankingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).HotContentRanking(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_HotContentRanking_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).HotContentRanking(ctx, req.(*HotContentRankingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_RankingScope_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RankingScopeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).RankingScope(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_RankingScope_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).RankingScope(ctx, req.(*RankingScopeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_CreatorRanking_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreatorRankingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).CreatorRanking(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_CreatorRanking_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).CreatorRanking(ctx, req.(*CreatorRankingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_CreatorRankingNotice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreatorRankingNoticeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).CreatorRankingNotice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_CreatorRankingNotice_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).CreatorRankingNotice(ctx, req.(*CreatorRankingNoticeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_SearchTitleAutoComplete_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchTitleAutoCompleteRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).SearchTitleAutoComplete(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_SearchTitleAutoComplete_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).SearchTitleAutoComplete(ctx, req.(*SearchTitleAutoCompleteRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_RecommendTest_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RecommendTestRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).RecommendTest(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_RecommendTest_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).RecommendTest(ctx, req.(*RecommendTestRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_ContentSimilarity_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ContentSimilarityRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).ContentSimilarity(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_ContentSimilarity_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).ContentSimilarity(ctx, req.(*ContentSimilarityRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_UserContent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserContentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).UserContent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_UserContent_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).UserContent(ctx, req.(*UserContentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_UserBehavior_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserBehaviorRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).UserBehavior(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_UserBehavior_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).UserBehavior(ctx, req.(*UserBehaviorRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_UserOriginBehavior_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserOriginBehaviorRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).UserOriginBehavior(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_UserOriginBehavior_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).UserOriginBehavior(ctx, req.(*UserOriginBehaviorRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_UserVector_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserVectorRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).UserVector(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_UserVector_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).UserVector(ctx, req.(*UserVectorRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_UserRealVector_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserVectorRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).UserRealVector(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_UserRealVector_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).UserRealVector(ctx, req.(*UserVectorRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_ResetUserBehavior_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ResetUserBehaviorRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).ResetUserBehavior(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_ResetUserBehavior_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).ResetUserBehavior(ctx, req.(*ResetUserBehaviorRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Service_ServiceDesc is the grpc.ServiceDesc for Service service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Service_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.recommend.v1.Service",
	HandlerType: (*ServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Healthy",
			Handler:    _Service_Healthy_Handler,
		},
		{
			MethodName: "Recommend",
			Handler:    _Service_Recommend_Handler,
		},
		{
			MethodName: "RecommendV2",
			Handler:    _Service_RecommendV2_Handler,
		},
		{
			MethodName: "FindCommerceByCategory",
			Handler:    _Service_FindCommerceByCategory_Handler,
		},
		{
			MethodName: "FindHotAndNew",
			Handler:    _Service_FindHotAndNew_Handler,
		},
		{
			MethodName: "FindHotAndNewV2",
			Handler:    _Service_FindHotAndNewV2_Handler,
		},
		{
			MethodName: "FindFollowPublish",
			Handler:    _Service_FindFollowPublish_Handler,
		},
		{
			MethodName: "TraderHome",
			Handler:    _Service_TraderHome_Handler,
		},
		{
			MethodName: "TraderPostCount",
			Handler:    _Service_TraderPostCount_Handler,
		},
		{
			MethodName: "Search",
			Handler:    _Service_Search_Handler,
		},
		{
			MethodName: "FindSearchTitle",
			Handler:    _Service_FindSearchTitle_Handler,
		},
		{
			MethodName: "InterestedUser",
			Handler:    _Service_InterestedUser_Handler,
		},
		{
			MethodName: "FindHotContent",
			Handler:    _Service_FindHotContent_Handler,
		},
		{
			MethodName: "RecommendUser",
			Handler:    _Service_RecommendUser_Handler,
		},
		{
			MethodName: "SkyLineActivity",
			Handler:    _Service_SkyLineActivity_Handler,
		},
		{
			MethodName: "YearlyReport",
			Handler:    _Service_YearlyReport_Handler,
		},
		{
			MethodName: "RecommendFeedback",
			Handler:    _Service_RecommendFeedback_Handler,
		},
		{
			MethodName: "HotContentRanking",
			Handler:    _Service_HotContentRanking_Handler,
		},
		{
			MethodName: "RankingScope",
			Handler:    _Service_RankingScope_Handler,
		},
		{
			MethodName: "CreatorRanking",
			Handler:    _Service_CreatorRanking_Handler,
		},
		{
			MethodName: "CreatorRankingNotice",
			Handler:    _Service_CreatorRankingNotice_Handler,
		},
		{
			MethodName: "SearchTitleAutoComplete",
			Handler:    _Service_SearchTitleAutoComplete_Handler,
		},
		{
			MethodName: "RecommendTest",
			Handler:    _Service_RecommendTest_Handler,
		},
		{
			MethodName: "ContentSimilarity",
			Handler:    _Service_ContentSimilarity_Handler,
		},
		{
			MethodName: "UserContent",
			Handler:    _Service_UserContent_Handler,
		},
		{
			MethodName: "UserBehavior",
			Handler:    _Service_UserBehavior_Handler,
		},
		{
			MethodName: "UserOriginBehavior",
			Handler:    _Service_UserOriginBehavior_Handler,
		},
		{
			MethodName: "UserVector",
			Handler:    _Service_UserVector_Handler,
		},
		{
			MethodName: "UserRealVector",
			Handler:    _Service_UserRealVector_Handler,
		},
		{
			MethodName: "ResetUserBehavior",
			Handler:    _Service_ResetUserBehavior_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "recommend/v1/service.proto",
}
