// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.4
// - protoc             v4.25.3
// source: user_center/v1/usercenter.proto

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationUserCenterCreateExpoPreUser = "/api.user_center.v1.UserCenter/CreateExpoPreUser"
const OperationUserCenterGetBasicUserInfo = "/api.user_center.v1.UserCenter/GetBasicUserInfo"
const OperationUserCenterGetEnterpriseUserIdsByCode = "/api.user_center.v1.UserCenter/GetEnterpriseUserIdsByCode"
const OperationUserCenterGetOfficialNumberTypeById = "/api.user_center.v1.UserCenter/GetOfficialNumberTypeById"
const OperationUserCenterGetUserAttentionsPageList = "/api.user_center.v1.UserCenter/GetUserAttentionsPageList"
const OperationUserCenterGetUserByPhoneNumber = "/api.user_center.v1.UserCenter/GetUserByPhoneNumber"
const OperationUserCenterGetUserEnterpriseCode = "/api.user_center.v1.UserCenter/GetUserEnterpriseCode"
const OperationUserCenterGetUserFansNoFollowUserIds = "/api.user_center.v1.UserCenter/GetUserFansNoFollowUserIds"
const OperationUserCenterGetUserFollow = "/api.user_center.v1.UserCenter/GetUserFollow"
const OperationUserCenterGetUserFollowAndFansCount = "/api.user_center.v1.UserCenter/GetUserFollowAndFansCount"
const OperationUserCenterGetUserUserIdByWikiNumber = "/api.user_center.v1.UserCenter/GetUserUserIdByWikiNumber"
const OperationUserCenterGetUserWikiNumbers = "/api.user_center.v1.UserCenter/GetUserWikiNumbers"
const OperationUserCenterGetUsersFansCount = "/api.user_center.v1.UserCenter/GetUsersFansCount"
const OperationUserCenterGetUsersInfo = "/api.user_center.v1.UserCenter/GetUsersInfo"

type UserCenterHTTPServer interface {
	// CreateExpoPreUser创建展会预注册
	CreateExpoPreUser(context.Context, *CreateExpoPreUserRequest) (*CreateExpoPreUserReply, error)
	GetBasicUserInfo(context.Context, *GetUserWikiNumbersRequest) (*GetBasicUserInfoReply, error)
	// GetEnterpriseUserIdsByCode根据企业code获取 企业员工和管理员userid
	GetEnterpriseUserIdsByCode(context.Context, *GetEnterpriseUserIdsByCodeRequest) (*GetEnterpriseUserIdsByCodeReply, error)
	// GetOfficialNumberTypeById 根据id获取企业类型
	GetOfficialNumberTypeById(context.Context, *GetOfficialNumberTypeByIdRequest) (*GetOfficialNumberTypeByIdReply, error)
	GetUserAttentionsPageList(context.Context, *GetUserAttentionsPageListRequest) (*GetUserAttentionsPageListReply, error)
	GetUserByPhoneNumber(context.Context, *GetUserByPhoneNumberRequest) (*GetUserByPhoneNumberReply, error)
	// GetUserEnterpriseCode获取用户企业代码
	GetUserEnterpriseCode(context.Context, *GetUserEnterpriseCodeRequest) (*GetUserEnterpriseCodeReply, error)
	// GetUserFansNoFollowUserIds获取用户粉丝并且没有相互关注的
	GetUserFansNoFollowUserIds(context.Context, *GetUserFansNoFollowRequest) (*GetUserFansNoFollowReply, error)
	// GetUserFollow 获取用户关注对象
	GetUserFollow(context.Context, *GetUserFollowRequest) (*GetUserFollowReply, error)
	// GetUserFollowAndFansCount获取用户粉丝和关注数量
	GetUserFollowAndFansCount(context.Context, *GetUserFollowAndFansCountRequest) (*GetUserFollowAndFansCountReply, error)
	GetUserUserIdByWikiNumber(context.Context, *GetUserWikiNumbersRequest) (*GetUserWikiNumbersReply, error)
	GetUserWikiNumbers(context.Context, *GetUserWikiNumbersRequest) (*GetUserWikiNumbersReply, error)
	// GetUsersFansCount获取用户粉丝数量
	GetUsersFansCount(context.Context, *GetUsersFansCountRequest) (*GetUsersFansCountReply, error)
	GetUsersInfo(context.Context, *GetUsersRequest) (*GetUsersReply, error)
}

func RegisterUserCenterHTTPServer(s *http.Server, srv UserCenterHTTPServer) {
	r := s.Route("/")
	r.POST("/v1/app/usercenter/getbusinesscardlist", _UserCenter_GetUsersInfo0_HTTP_Handler(srv))
	r.POST("/v1/app/usercenter/getenterpriseuseridsbycode", _UserCenter_GetEnterpriseUserIdsByCode0_HTTP_Handler(srv))
	r.GET("/v1/app/usercenter/getofficialnumbertypebyid", _UserCenter_GetOfficialNumberTypeById0_HTTP_Handler(srv))
	r.GET("/v1/app/usercenter/getuserfollow", _UserCenter_GetUserFollow0_HTTP_Handler(srv))
	r.GET("/v1/app/usercenter/getuserfansnofollowuserids", _UserCenter_GetUserFansNoFollowUserIds0_HTTP_Handler(srv))
	r.POST("/v1/app/usercenter/getusersfanscount", _UserCenter_GetUsersFansCount0_HTTP_Handler(srv))
	r.GET("/v1/app/usercenter/getuserattentionspagelist", _UserCenter_GetUserAttentionsPageList0_HTTP_Handler(srv))
	r.POST("/v1/app/usercenter/getuserwikinumbers", _UserCenter_GetUserWikiNumbers0_HTTP_Handler(srv))
	r.POST("/v1/app/usercenter/getuseruseridbywikifxnumber", _UserCenter_GetUserUserIdByWikiNumber0_HTTP_Handler(srv))
	r.POST("/v1/app/usercenter/getbasicuserinfo", _UserCenter_GetBasicUserInfo0_HTTP_Handler(srv))
	r.POST("/v1/app/usercenter/getuserbyphonenumber", _UserCenter_GetUserByPhoneNumber0_HTTP_Handler(srv))
	r.POST("/v1/app/usercenter/createexpopreuser", _UserCenter_CreateExpoPreUser0_HTTP_Handler(srv))
	r.GET("/v1/app/usercenter/getuserfollowandfanscount", _UserCenter_GetUserFollowAndFansCount0_HTTP_Handler(srv))
	r.GET("/v1/app/usercenter/getuserenterprisecode", _UserCenter_GetUserEnterpriseCode0_HTTP_Handler(srv))
}

func _UserCenter_GetUsersInfo0_HTTP_Handler(srv UserCenterHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetUsersRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUserCenterGetUsersInfo)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetUsersInfo(ctx, req.(*GetUsersRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetUsersReply)
		return ctx.Result(200, reply)
	}
}

func _UserCenter_GetEnterpriseUserIdsByCode0_HTTP_Handler(srv UserCenterHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetEnterpriseUserIdsByCodeRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUserCenterGetEnterpriseUserIdsByCode)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetEnterpriseUserIdsByCode(ctx, req.(*GetEnterpriseUserIdsByCodeRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetEnterpriseUserIdsByCodeReply)
		return ctx.Result(200, reply)
	}
}

func _UserCenter_GetOfficialNumberTypeById0_HTTP_Handler(srv UserCenterHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetOfficialNumberTypeByIdRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUserCenterGetOfficialNumberTypeById)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetOfficialNumberTypeById(ctx, req.(*GetOfficialNumberTypeByIdRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetOfficialNumberTypeByIdReply)
		return ctx.Result(200, reply)
	}
}

func _UserCenter_GetUserFollow0_HTTP_Handler(srv UserCenterHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetUserFollowRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUserCenterGetUserFollow)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetUserFollow(ctx, req.(*GetUserFollowRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetUserFollowReply)
		return ctx.Result(200, reply)
	}
}

func _UserCenter_GetUserFansNoFollowUserIds0_HTTP_Handler(srv UserCenterHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetUserFansNoFollowRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUserCenterGetUserFansNoFollowUserIds)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetUserFansNoFollowUserIds(ctx, req.(*GetUserFansNoFollowRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetUserFansNoFollowReply)
		return ctx.Result(200, reply)
	}
}

func _UserCenter_GetUsersFansCount0_HTTP_Handler(srv UserCenterHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetUsersFansCountRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUserCenterGetUsersFansCount)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetUsersFansCount(ctx, req.(*GetUsersFansCountRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetUsersFansCountReply)
		return ctx.Result(200, reply)
	}
}

func _UserCenter_GetUserAttentionsPageList0_HTTP_Handler(srv UserCenterHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetUserAttentionsPageListRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUserCenterGetUserAttentionsPageList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetUserAttentionsPageList(ctx, req.(*GetUserAttentionsPageListRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetUserAttentionsPageListReply)
		return ctx.Result(200, reply)
	}
}

func _UserCenter_GetUserWikiNumbers0_HTTP_Handler(srv UserCenterHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetUserWikiNumbersRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUserCenterGetUserWikiNumbers)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetUserWikiNumbers(ctx, req.(*GetUserWikiNumbersRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetUserWikiNumbersReply)
		return ctx.Result(200, reply)
	}
}

func _UserCenter_GetUserUserIdByWikiNumber0_HTTP_Handler(srv UserCenterHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetUserWikiNumbersRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUserCenterGetUserUserIdByWikiNumber)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetUserUserIdByWikiNumber(ctx, req.(*GetUserWikiNumbersRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetUserWikiNumbersReply)
		return ctx.Result(200, reply)
	}
}

func _UserCenter_GetBasicUserInfo0_HTTP_Handler(srv UserCenterHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetUserWikiNumbersRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUserCenterGetBasicUserInfo)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetBasicUserInfo(ctx, req.(*GetUserWikiNumbersRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetBasicUserInfoReply)
		return ctx.Result(200, reply)
	}
}

func _UserCenter_GetUserByPhoneNumber0_HTTP_Handler(srv UserCenterHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetUserByPhoneNumberRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUserCenterGetUserByPhoneNumber)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetUserByPhoneNumber(ctx, req.(*GetUserByPhoneNumberRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetUserByPhoneNumberReply)
		return ctx.Result(200, reply)
	}
}

func _UserCenter_CreateExpoPreUser0_HTTP_Handler(srv UserCenterHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateExpoPreUserRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUserCenterCreateExpoPreUser)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateExpoPreUser(ctx, req.(*CreateExpoPreUserRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CreateExpoPreUserReply)
		return ctx.Result(200, reply)
	}
}

func _UserCenter_GetUserFollowAndFansCount0_HTTP_Handler(srv UserCenterHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetUserFollowAndFansCountRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUserCenterGetUserFollowAndFansCount)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetUserFollowAndFansCount(ctx, req.(*GetUserFollowAndFansCountRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetUserFollowAndFansCountReply)
		return ctx.Result(200, reply)
	}
}

func _UserCenter_GetUserEnterpriseCode0_HTTP_Handler(srv UserCenterHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetUserEnterpriseCodeRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUserCenterGetUserEnterpriseCode)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetUserEnterpriseCode(ctx, req.(*GetUserEnterpriseCodeRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetUserEnterpriseCodeReply)
		return ctx.Result(200, reply)
	}
}

type UserCenterHTTPClient interface {
	CreateExpoPreUser(ctx context.Context, req *CreateExpoPreUserRequest, opts ...http.CallOption) (rsp *CreateExpoPreUserReply, err error)
	GetBasicUserInfo(ctx context.Context, req *GetUserWikiNumbersRequest, opts ...http.CallOption) (rsp *GetBasicUserInfoReply, err error)
	GetEnterpriseUserIdsByCode(ctx context.Context, req *GetEnterpriseUserIdsByCodeRequest, opts ...http.CallOption) (rsp *GetEnterpriseUserIdsByCodeReply, err error)
	GetOfficialNumberTypeById(ctx context.Context, req *GetOfficialNumberTypeByIdRequest, opts ...http.CallOption) (rsp *GetOfficialNumberTypeByIdReply, err error)
	GetUserAttentionsPageList(ctx context.Context, req *GetUserAttentionsPageListRequest, opts ...http.CallOption) (rsp *GetUserAttentionsPageListReply, err error)
	GetUserByPhoneNumber(ctx context.Context, req *GetUserByPhoneNumberRequest, opts ...http.CallOption) (rsp *GetUserByPhoneNumberReply, err error)
	GetUserEnterpriseCode(ctx context.Context, req *GetUserEnterpriseCodeRequest, opts ...http.CallOption) (rsp *GetUserEnterpriseCodeReply, err error)
	GetUserFansNoFollowUserIds(ctx context.Context, req *GetUserFansNoFollowRequest, opts ...http.CallOption) (rsp *GetUserFansNoFollowReply, err error)
	GetUserFollow(ctx context.Context, req *GetUserFollowRequest, opts ...http.CallOption) (rsp *GetUserFollowReply, err error)
	GetUserFollowAndFansCount(ctx context.Context, req *GetUserFollowAndFansCountRequest, opts ...http.CallOption) (rsp *GetUserFollowAndFansCountReply, err error)
	GetUserUserIdByWikiNumber(ctx context.Context, req *GetUserWikiNumbersRequest, opts ...http.CallOption) (rsp *GetUserWikiNumbersReply, err error)
	GetUserWikiNumbers(ctx context.Context, req *GetUserWikiNumbersRequest, opts ...http.CallOption) (rsp *GetUserWikiNumbersReply, err error)
	GetUsersFansCount(ctx context.Context, req *GetUsersFansCountRequest, opts ...http.CallOption) (rsp *GetUsersFansCountReply, err error)
	GetUsersInfo(ctx context.Context, req *GetUsersRequest, opts ...http.CallOption) (rsp *GetUsersReply, err error)
}

type UserCenterHTTPClientImpl struct {
	cc *http.Client
}

func NewUserCenterHTTPClient(client *http.Client) UserCenterHTTPClient {
	return &UserCenterHTTPClientImpl{client}
}

func (c *UserCenterHTTPClientImpl) CreateExpoPreUser(ctx context.Context, in *CreateExpoPreUserRequest, opts ...http.CallOption) (*CreateExpoPreUserReply, error) {
	var out CreateExpoPreUserReply
	pattern := "/v1/app/usercenter/createexpopreuser"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationUserCenterCreateExpoPreUser))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *UserCenterHTTPClientImpl) GetBasicUserInfo(ctx context.Context, in *GetUserWikiNumbersRequest, opts ...http.CallOption) (*GetBasicUserInfoReply, error) {
	var out GetBasicUserInfoReply
	pattern := "/v1/app/usercenter/getbasicuserinfo"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationUserCenterGetBasicUserInfo))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *UserCenterHTTPClientImpl) GetEnterpriseUserIdsByCode(ctx context.Context, in *GetEnterpriseUserIdsByCodeRequest, opts ...http.CallOption) (*GetEnterpriseUserIdsByCodeReply, error) {
	var out GetEnterpriseUserIdsByCodeReply
	pattern := "/v1/app/usercenter/getenterpriseuseridsbycode"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationUserCenterGetEnterpriseUserIdsByCode))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *UserCenterHTTPClientImpl) GetOfficialNumberTypeById(ctx context.Context, in *GetOfficialNumberTypeByIdRequest, opts ...http.CallOption) (*GetOfficialNumberTypeByIdReply, error) {
	var out GetOfficialNumberTypeByIdReply
	pattern := "/v1/app/usercenter/getofficialnumbertypebyid"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationUserCenterGetOfficialNumberTypeById))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *UserCenterHTTPClientImpl) GetUserAttentionsPageList(ctx context.Context, in *GetUserAttentionsPageListRequest, opts ...http.CallOption) (*GetUserAttentionsPageListReply, error) {
	var out GetUserAttentionsPageListReply
	pattern := "/v1/app/usercenter/getuserattentionspagelist"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationUserCenterGetUserAttentionsPageList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *UserCenterHTTPClientImpl) GetUserByPhoneNumber(ctx context.Context, in *GetUserByPhoneNumberRequest, opts ...http.CallOption) (*GetUserByPhoneNumberReply, error) {
	var out GetUserByPhoneNumberReply
	pattern := "/v1/app/usercenter/getuserbyphonenumber"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationUserCenterGetUserByPhoneNumber))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *UserCenterHTTPClientImpl) GetUserEnterpriseCode(ctx context.Context, in *GetUserEnterpriseCodeRequest, opts ...http.CallOption) (*GetUserEnterpriseCodeReply, error) {
	var out GetUserEnterpriseCodeReply
	pattern := "/v1/app/usercenter/getuserenterprisecode"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationUserCenterGetUserEnterpriseCode))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *UserCenterHTTPClientImpl) GetUserFansNoFollowUserIds(ctx context.Context, in *GetUserFansNoFollowRequest, opts ...http.CallOption) (*GetUserFansNoFollowReply, error) {
	var out GetUserFansNoFollowReply
	pattern := "/v1/app/usercenter/getuserfansnofollowuserids"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationUserCenterGetUserFansNoFollowUserIds))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *UserCenterHTTPClientImpl) GetUserFollow(ctx context.Context, in *GetUserFollowRequest, opts ...http.CallOption) (*GetUserFollowReply, error) {
	var out GetUserFollowReply
	pattern := "/v1/app/usercenter/getuserfollow"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationUserCenterGetUserFollow))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *UserCenterHTTPClientImpl) GetUserFollowAndFansCount(ctx context.Context, in *GetUserFollowAndFansCountRequest, opts ...http.CallOption) (*GetUserFollowAndFansCountReply, error) {
	var out GetUserFollowAndFansCountReply
	pattern := "/v1/app/usercenter/getuserfollowandfanscount"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationUserCenterGetUserFollowAndFansCount))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *UserCenterHTTPClientImpl) GetUserUserIdByWikiNumber(ctx context.Context, in *GetUserWikiNumbersRequest, opts ...http.CallOption) (*GetUserWikiNumbersReply, error) {
	var out GetUserWikiNumbersReply
	pattern := "/v1/app/usercenter/getuseruseridbywikifxnumber"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationUserCenterGetUserUserIdByWikiNumber))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *UserCenterHTTPClientImpl) GetUserWikiNumbers(ctx context.Context, in *GetUserWikiNumbersRequest, opts ...http.CallOption) (*GetUserWikiNumbersReply, error) {
	var out GetUserWikiNumbersReply
	pattern := "/v1/app/usercenter/getuserwikinumbers"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationUserCenterGetUserWikiNumbers))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *UserCenterHTTPClientImpl) GetUsersFansCount(ctx context.Context, in *GetUsersFansCountRequest, opts ...http.CallOption) (*GetUsersFansCountReply, error) {
	var out GetUsersFansCountReply
	pattern := "/v1/app/usercenter/getusersfanscount"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationUserCenterGetUsersFansCount))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *UserCenterHTTPClientImpl) GetUsersInfo(ctx context.Context, in *GetUsersRequest, opts ...http.CallOption) (*GetUsersReply, error) {
	var out GetUsersReply
	pattern := "/v1/app/usercenter/getbusinesscardlist"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationUserCenterGetUsersInfo))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
