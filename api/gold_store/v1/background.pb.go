// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.25.3
// source: gold_store/v1/background.proto

package v1

import (
	common "api-community/api/common"
	_ "github.com/grpc-ecosystem/grpc-gateway/v2/protoc-gen-openapiv2/options"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// =========================== 标签 ===========================
type LabelCategory int32

const (
	LabelCategory_LABEL_CATEGORY_IMAGE LabelCategory = 0 // 图片
	LabelCategory_LABEL_CATEGORY_TEXT  LabelCategory = 1 // 文本
)

// Enum value maps for LabelCategory.
var (
	LabelCategory_name = map[int32]string{
		0: "LABEL_CATEGORY_IMAGE",
		1: "LABEL_CATEGORY_TEXT",
	}
	LabelCategory_value = map[string]int32{
		"LABEL_CATEGORY_IMAGE": 0,
		"LABEL_CATEGORY_TEXT":  1,
	}
)

func (x LabelCategory) Enum() *LabelCategory {
	p := new(LabelCategory)
	*p = x
	return p
}

func (x LabelCategory) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LabelCategory) Descriptor() protoreflect.EnumDescriptor {
	return file_gold_store_v1_background_proto_enumTypes[0].Descriptor()
}

func (LabelCategory) Type() protoreflect.EnumType {
	return &file_gold_store_v1_background_proto_enumTypes[0]
}

func (x LabelCategory) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LabelCategory.Descriptor instead.
func (LabelCategory) EnumDescriptor() ([]byte, []int) {
	return file_gold_store_v1_background_proto_rawDescGZIP(), []int{0}
}

// =========================== 订单 ===========================
type OrderDeliverRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OrderNo     string `protobuf:"bytes,1,opt,name=order_no,json=orderNo,proto3" json:"order_no"`
	TrackingNo  string `protobuf:"bytes,2,opt,name=tracking_no,json=trackingNo,proto3" json:"tracking_no"`
	CarrierName string `protobuf:"bytes,3,opt,name=carrier_name,json=carrierName,proto3" json:"carrier_name"`
}

func (x *OrderDeliverRequest) Reset() {
	*x = OrderDeliverRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_background_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OrderDeliverRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OrderDeliverRequest) ProtoMessage() {}

func (x *OrderDeliverRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_background_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OrderDeliverRequest.ProtoReflect.Descriptor instead.
func (*OrderDeliverRequest) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_background_proto_rawDescGZIP(), []int{0}
}

func (x *OrderDeliverRequest) GetOrderNo() string {
	if x != nil {
		return x.OrderNo
	}
	return ""
}

func (x *OrderDeliverRequest) GetTrackingNo() string {
	if x != nil {
		return x.TrackingNo
	}
	return ""
}

func (x *OrderDeliverRequest) GetCarrierName() string {
	if x != nil {
		return x.CarrierName
	}
	return ""
}

type AdminOrderListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OrderNo       string        `protobuf:"bytes,1,opt,name=order_no,json=orderNo,proto3" json:"order_no"`
	GoodsName     string        `protobuf:"bytes,2,opt,name=goods_name,json=goodsName,proto3" json:"goods_name"`
	PaymentMethod PaymentMethod `protobuf:"varint,3,opt,name=payment_method,json=paymentMethod,proto3,enum=api.gold_store.v1.PaymentMethod" json:"payment_method"`
	Status        OrderStatus   `protobuf:"varint,4,opt,name=status,json=status,proto3,enum=api.gold_store.v1.OrderStatus" json:"status"`
	Size          int32         `protobuf:"varint,5,opt,name=size,json=size,proto3" json:"size"`
	Page          int32         `protobuf:"varint,6,opt,name=page,json=page,proto3" json:"page"`
}

func (x *AdminOrderListRequest) Reset() {
	*x = AdminOrderListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_background_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AdminOrderListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdminOrderListRequest) ProtoMessage() {}

func (x *AdminOrderListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_background_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdminOrderListRequest.ProtoReflect.Descriptor instead.
func (*AdminOrderListRequest) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_background_proto_rawDescGZIP(), []int{1}
}

func (x *AdminOrderListRequest) GetOrderNo() string {
	if x != nil {
		return x.OrderNo
	}
	return ""
}

func (x *AdminOrderListRequest) GetGoodsName() string {
	if x != nil {
		return x.GoodsName
	}
	return ""
}

func (x *AdminOrderListRequest) GetPaymentMethod() PaymentMethod {
	if x != nil {
		return x.PaymentMethod
	}
	return PaymentMethod_GOLD
}

func (x *AdminOrderListRequest) GetStatus() OrderStatus {
	if x != nil {
		return x.Status
	}
	return OrderStatus_UNKNOWN
}

func (x *AdminOrderListRequest) GetSize() int32 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *AdminOrderListRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

type AdminOrder struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OrderNo       string        `protobuf:"bytes,1,opt,name=order_no,json=orderNo,proto3" json:"order_no"`
	GoodsId       string        `protobuf:"bytes,2,opt,name=goods_id,json=goodsId,proto3" json:"goods_id"`
	UserId        string        `protobuf:"bytes,3,opt,name=user_id,json=userId,proto3" json:"user_id"`
	GoodsName     string        `protobuf:"bytes,4,opt,name=goods_name,json=goodsName,proto3" json:"goods_name"`
	SkuId         string        `protobuf:"bytes,5,opt,name=sku_id,json=skuId,proto3" json:"sku_id"`
	SpecDesc      string        `protobuf:"bytes,6,opt,name=spec_desc,json=specDesc,proto3" json:"spec_desc"`
	Price         float32       `protobuf:"fixed32,7,opt,name=price,json=price,proto3" json:"price"`
	Quantity      int32         `protobuf:"varint,8,opt,name=quantity,json=quantity,proto3" json:"quantity"`
	Address       *Address      `protobuf:"bytes,9,opt,name=address,json=address,proto3" json:"address"`
	TotalAmount   float32       `protobuf:"fixed32,10,opt,name=total_amount,json=totalAmount,proto3" json:"total_amount"`
	PaymentTotal  float32       `protobuf:"fixed32,20,opt,name=payment_total,json=paymentTotal,proto3" json:"payment_total"`
	ShippingFee   float32       `protobuf:"fixed32,21,opt,name=shipping_fee,json=shippingFee,proto3" json:"shipping_fee"`
	PaymentMethod PaymentMethod `protobuf:"varint,22,opt,name=payment_method,json=paymentMethod,proto3,enum=api.gold_store.v1.PaymentMethod" json:"payment_method"`
	Status        OrderStatus   `protobuf:"varint,23,opt,name=status,json=status,proto3,enum=api.gold_store.v1.OrderStatus" json:"status"`
	CreatedAt     int64         `protobuf:"varint,24,opt,name=created_at,json=createdAt,proto3" json:"created_at"`
	WikiNo        string        `protobuf:"bytes,25,opt,name=wiki_no,json=wikiNo,proto3" json:"wiki_no"`
}

func (x *AdminOrder) Reset() {
	*x = AdminOrder{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_background_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AdminOrder) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdminOrder) ProtoMessage() {}

func (x *AdminOrder) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_background_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdminOrder.ProtoReflect.Descriptor instead.
func (*AdminOrder) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_background_proto_rawDescGZIP(), []int{2}
}

func (x *AdminOrder) GetOrderNo() string {
	if x != nil {
		return x.OrderNo
	}
	return ""
}

func (x *AdminOrder) GetGoodsId() string {
	if x != nil {
		return x.GoodsId
	}
	return ""
}

func (x *AdminOrder) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *AdminOrder) GetGoodsName() string {
	if x != nil {
		return x.GoodsName
	}
	return ""
}

func (x *AdminOrder) GetSkuId() string {
	if x != nil {
		return x.SkuId
	}
	return ""
}

func (x *AdminOrder) GetSpecDesc() string {
	if x != nil {
		return x.SpecDesc
	}
	return ""
}

func (x *AdminOrder) GetPrice() float32 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *AdminOrder) GetQuantity() int32 {
	if x != nil {
		return x.Quantity
	}
	return 0
}

func (x *AdminOrder) GetAddress() *Address {
	if x != nil {
		return x.Address
	}
	return nil
}

func (x *AdminOrder) GetTotalAmount() float32 {
	if x != nil {
		return x.TotalAmount
	}
	return 0
}

func (x *AdminOrder) GetPaymentTotal() float32 {
	if x != nil {
		return x.PaymentTotal
	}
	return 0
}

func (x *AdminOrder) GetShippingFee() float32 {
	if x != nil {
		return x.ShippingFee
	}
	return 0
}

func (x *AdminOrder) GetPaymentMethod() PaymentMethod {
	if x != nil {
		return x.PaymentMethod
	}
	return PaymentMethod_GOLD
}

func (x *AdminOrder) GetStatus() OrderStatus {
	if x != nil {
		return x.Status
	}
	return OrderStatus_UNKNOWN
}

func (x *AdminOrder) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *AdminOrder) GetWikiNo() string {
	if x != nil {
		return x.WikiNo
	}
	return ""
}

type AdminOrderListReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Orders []*AdminOrder `protobuf:"bytes,1,rep,name=orders,json=orders,proto3" json:"orders"`
	Total  int32         `protobuf:"varint,2,opt,name=total,json=total,proto3" json:"total"`
}

func (x *AdminOrderListReply) Reset() {
	*x = AdminOrderListReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_background_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AdminOrderListReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdminOrderListReply) ProtoMessage() {}

func (x *AdminOrderListReply) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_background_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdminOrderListReply.ProtoReflect.Descriptor instead.
func (*AdminOrderListReply) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_background_proto_rawDescGZIP(), []int{3}
}

func (x *AdminOrderListReply) GetOrders() []*AdminOrder {
	if x != nil {
		return x.Orders
	}
	return nil
}

func (x *AdminOrderListReply) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

type AdminOrderDetailRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OrderNo string `protobuf:"bytes,1,opt,name=order_no,json=orderNo,proto3" json:"order_no"`
}

func (x *AdminOrderDetailRequest) Reset() {
	*x = AdminOrderDetailRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_background_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AdminOrderDetailRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdminOrderDetailRequest) ProtoMessage() {}

func (x *AdminOrderDetailRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_background_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdminOrderDetailRequest.ProtoReflect.Descriptor instead.
func (*AdminOrderDetailRequest) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_background_proto_rawDescGZIP(), []int{4}
}

func (x *AdminOrderDetailRequest) GetOrderNo() string {
	if x != nil {
		return x.OrderNo
	}
	return ""
}

type AdminOrderDetailReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OrderNo       string          `protobuf:"bytes,1,opt,name=order_no,json=orderNo,proto3" json:"order_no"`
	GoodsId       string          `protobuf:"bytes,2,opt,name=goods_id,json=goodsId,proto3" json:"goods_id"`
	UserId        string          `protobuf:"bytes,3,opt,name=user_id,json=userId,proto3" json:"user_id"`
	GoodsName     string          `protobuf:"bytes,4,opt,name=goods_name,json=goodsName,proto3" json:"goods_name"`
	SkuId         string          `protobuf:"bytes,5,opt,name=sku_id,json=skuId,proto3" json:"sku_id"`
	SpecDesc      string          `protobuf:"bytes,6,opt,name=spec_desc,json=specDesc,proto3" json:"spec_desc"`
	Price         float32         `protobuf:"fixed32,7,opt,name=price,json=price,proto3" json:"price"`
	Quantity      int32           `protobuf:"varint,8,opt,name=quantity,json=quantity,proto3" json:"quantity"`
	Address       *Address        `protobuf:"bytes,9,opt,name=address,json=address,proto3" json:"address"`
	TotalAmount   float32         `protobuf:"fixed32,10,opt,name=total_amount,json=totalAmount,proto3" json:"total_amount"`
	PaymentTotal  float32         `protobuf:"fixed32,20,opt,name=payment_total,json=paymentTotal,proto3" json:"payment_total"`
	ShippingFee   float32         `protobuf:"fixed32,21,opt,name=shipping_fee,json=shippingFee,proto3" json:"shipping_fee"`
	PaymentMethod PaymentMethod   `protobuf:"varint,22,opt,name=payment_method,json=paymentMethod,proto3,enum=api.gold_store.v1.PaymentMethod" json:"payment_method"`
	Status        OrderStatus     `protobuf:"varint,23,opt,name=status,json=status,proto3,enum=api.gold_store.v1.OrderStatus" json:"status"`
	CreatedAt     int64           `protobuf:"varint,24,opt,name=created_at,json=createdAt,proto3" json:"created_at"`
	WikiNo        string          `protobuf:"bytes,25,opt,name=wiki_no,json=wikiNo,proto3" json:"wiki_no"`
	PaidAt        int64           `protobuf:"varint,26,opt,name=paid_at,json=paidAt,proto3" json:"paid_at"`
	CarrierName   string          `protobuf:"bytes,27,opt,name=carrier_name,json=carrierName,proto3" json:"carrier_name"`
	CarrierIcon   string          `protobuf:"bytes,28,opt,name=carrier_icon,json=carrierIcon,proto3" json:"carrier_icon"`
	TrackingNo    string          `protobuf:"bytes,29,opt,name=tracking_no,json=trackingNo,proto3" json:"tracking_no"`
	Steps         []*LogisticStep `protobuf:"bytes,30,rep,name=steps,json=steps,proto3" json:"steps"`
}

func (x *AdminOrderDetailReply) Reset() {
	*x = AdminOrderDetailReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_background_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AdminOrderDetailReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdminOrderDetailReply) ProtoMessage() {}

func (x *AdminOrderDetailReply) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_background_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdminOrderDetailReply.ProtoReflect.Descriptor instead.
func (*AdminOrderDetailReply) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_background_proto_rawDescGZIP(), []int{5}
}

func (x *AdminOrderDetailReply) GetOrderNo() string {
	if x != nil {
		return x.OrderNo
	}
	return ""
}

func (x *AdminOrderDetailReply) GetGoodsId() string {
	if x != nil {
		return x.GoodsId
	}
	return ""
}

func (x *AdminOrderDetailReply) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *AdminOrderDetailReply) GetGoodsName() string {
	if x != nil {
		return x.GoodsName
	}
	return ""
}

func (x *AdminOrderDetailReply) GetSkuId() string {
	if x != nil {
		return x.SkuId
	}
	return ""
}

func (x *AdminOrderDetailReply) GetSpecDesc() string {
	if x != nil {
		return x.SpecDesc
	}
	return ""
}

func (x *AdminOrderDetailReply) GetPrice() float32 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *AdminOrderDetailReply) GetQuantity() int32 {
	if x != nil {
		return x.Quantity
	}
	return 0
}

func (x *AdminOrderDetailReply) GetAddress() *Address {
	if x != nil {
		return x.Address
	}
	return nil
}

func (x *AdminOrderDetailReply) GetTotalAmount() float32 {
	if x != nil {
		return x.TotalAmount
	}
	return 0
}

func (x *AdminOrderDetailReply) GetPaymentTotal() float32 {
	if x != nil {
		return x.PaymentTotal
	}
	return 0
}

func (x *AdminOrderDetailReply) GetShippingFee() float32 {
	if x != nil {
		return x.ShippingFee
	}
	return 0
}

func (x *AdminOrderDetailReply) GetPaymentMethod() PaymentMethod {
	if x != nil {
		return x.PaymentMethod
	}
	return PaymentMethod_GOLD
}

func (x *AdminOrderDetailReply) GetStatus() OrderStatus {
	if x != nil {
		return x.Status
	}
	return OrderStatus_UNKNOWN
}

func (x *AdminOrderDetailReply) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *AdminOrderDetailReply) GetWikiNo() string {
	if x != nil {
		return x.WikiNo
	}
	return ""
}

func (x *AdminOrderDetailReply) GetPaidAt() int64 {
	if x != nil {
		return x.PaidAt
	}
	return 0
}

func (x *AdminOrderDetailReply) GetCarrierName() string {
	if x != nil {
		return x.CarrierName
	}
	return ""
}

func (x *AdminOrderDetailReply) GetCarrierIcon() string {
	if x != nil {
		return x.CarrierIcon
	}
	return ""
}

func (x *AdminOrderDetailReply) GetTrackingNo() string {
	if x != nil {
		return x.TrackingNo
	}
	return ""
}

func (x *AdminOrderDetailReply) GetSteps() []*LogisticStep {
	if x != nil {
		return x.Steps
	}
	return nil
}

type Label struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id       string            `protobuf:"bytes,1,opt,name=id,json=id,proto3" json:"id"`
	Name     string            `protobuf:"bytes,2,opt,name=name,json=name,proto3" json:"name"`
	Category LabelCategory     `protobuf:"varint,3,opt,name=category,json=category,proto3,enum=api.gold_store.v1.LabelCategory" json:"category"`
	Values   map[string]string `protobuf:"bytes,4,rep,name=values,json=values,proto3" json:"values" protobuf_key:"bytes,1,opt,name=key,json=key,proto3" protobuf_val:"bytes,2,opt,name=value,json=value,proto3"`
}

func (x *Label) Reset() {
	*x = Label{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_background_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Label) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Label) ProtoMessage() {}

func (x *Label) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_background_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Label.ProtoReflect.Descriptor instead.
func (*Label) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_background_proto_rawDescGZIP(), []int{6}
}

func (x *Label) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Label) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Label) GetCategory() LabelCategory {
	if x != nil {
		return x.Category
	}
	return LabelCategory_LABEL_CATEGORY_IMAGE
}

func (x *Label) GetValues() map[string]string {
	if x != nil {
		return x.Values
	}
	return nil
}

type LabelListReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Items []*Label `protobuf:"bytes,1,rep,name=items,json=items,proto3" json:"items"`
}

func (x *LabelListReply) Reset() {
	*x = LabelListReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_background_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LabelListReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LabelListReply) ProtoMessage() {}

func (x *LabelListReply) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_background_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LabelListReply.ProtoReflect.Descriptor instead.
func (*LabelListReply) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_background_proto_rawDescGZIP(), []int{7}
}

func (x *LabelListReply) GetItems() []*Label {
	if x != nil {
		return x.Items
	}
	return nil
}

// =========================== 商品 ===========================
type GetStaticSpecRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Category GoodsCategory `protobuf:"varint,1,opt,name=category,json=category,proto3,enum=api.gold_store.v1.GoodsCategory" json:"category"`
}

func (x *GetStaticSpecRequest) Reset() {
	*x = GetStaticSpecRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_background_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetStaticSpecRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetStaticSpecRequest) ProtoMessage() {}

func (x *GetStaticSpecRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_background_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetStaticSpecRequest.ProtoReflect.Descriptor instead.
func (*GetStaticSpecRequest) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_background_proto_rawDescGZIP(), []int{8}
}

func (x *GetStaticSpecRequest) GetCategory() GoodsCategory {
	if x != nil {
		return x.Category
	}
	return GoodsCategory_GOODS_CATEGORY_PHYSICAL
}

type GoodsStaticSpecUnit struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id       string `protobuf:"bytes,1,opt,name=id,json=id,proto3" json:"id"`
	Name     string `protobuf:"bytes,2,opt,name=name,json=name,proto3" json:"name"`
	ShowName string `protobuf:"bytes,3,opt,name=show_name,json=showName,proto3" json:"show_name"`
	Key      string `protobuf:"bytes,4,opt,name=key,json=key,proto3" json:"key"`
}

func (x *GoodsStaticSpecUnit) Reset() {
	*x = GoodsStaticSpecUnit{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_background_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GoodsStaticSpecUnit) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GoodsStaticSpecUnit) ProtoMessage() {}

func (x *GoodsStaticSpecUnit) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_background_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GoodsStaticSpecUnit.ProtoReflect.Descriptor instead.
func (*GoodsStaticSpecUnit) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_background_proto_rawDescGZIP(), []int{9}
}

func (x *GoodsStaticSpecUnit) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *GoodsStaticSpecUnit) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GoodsStaticSpecUnit) GetShowName() string {
	if x != nil {
		return x.ShowName
	}
	return ""
}

func (x *GoodsStaticSpecUnit) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

type GoodsStaticSpecValue struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id        string            `protobuf:"bytes,1,opt,name=id,json=id,proto3" json:"id"`
	Name      string            `protobuf:"bytes,2,opt,name=name,json=name,proto3" json:"name"`
	Translate map[string]string `protobuf:"bytes,3,rep,name=translate,json=translate,proto3" json:"translate" protobuf_key:"bytes,1,opt,name=key,json=key,proto3" protobuf_val:"bytes,2,opt,name=value,json=value,proto3"`
}

func (x *GoodsStaticSpecValue) Reset() {
	*x = GoodsStaticSpecValue{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_background_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GoodsStaticSpecValue) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GoodsStaticSpecValue) ProtoMessage() {}

func (x *GoodsStaticSpecValue) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_background_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GoodsStaticSpecValue.ProtoReflect.Descriptor instead.
func (*GoodsStaticSpecValue) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_background_proto_rawDescGZIP(), []int{10}
}

func (x *GoodsStaticSpecValue) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *GoodsStaticSpecValue) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GoodsStaticSpecValue) GetTranslate() map[string]string {
	if x != nil {
		return x.Translate
	}
	return nil
}

type GoodsStaticSpec struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id        string                  `protobuf:"bytes,1,opt,name=id,json=id,proto3" json:"id"`
	Name      string                  `protobuf:"bytes,2,opt,name=name,json=name,proto3" json:"name"`
	Translate map[string]string       `protobuf:"bytes,3,rep,name=translate,json=translate,proto3" json:"translate" protobuf_key:"bytes,1,opt,name=key,json=key,proto3" protobuf_val:"bytes,2,opt,name=value,json=value,proto3"`
	Units     []*GoodsStaticSpecUnit  `protobuf:"bytes,4,rep,name=units,json=units,proto3" json:"units"`
	Values    []*GoodsStaticSpecValue `protobuf:"bytes,5,rep,name=values,json=values,proto3" json:"values"`
	Key       string                  `protobuf:"bytes,6,opt,name=key,json=key,proto3" json:"key"`
}

func (x *GoodsStaticSpec) Reset() {
	*x = GoodsStaticSpec{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_background_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GoodsStaticSpec) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GoodsStaticSpec) ProtoMessage() {}

func (x *GoodsStaticSpec) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_background_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GoodsStaticSpec.ProtoReflect.Descriptor instead.
func (*GoodsStaticSpec) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_background_proto_rawDescGZIP(), []int{11}
}

func (x *GoodsStaticSpec) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *GoodsStaticSpec) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GoodsStaticSpec) GetTranslate() map[string]string {
	if x != nil {
		return x.Translate
	}
	return nil
}

func (x *GoodsStaticSpec) GetUnits() []*GoodsStaticSpecUnit {
	if x != nil {
		return x.Units
	}
	return nil
}

func (x *GoodsStaticSpec) GetValues() []*GoodsStaticSpecValue {
	if x != nil {
		return x.Values
	}
	return nil
}

func (x *GoodsStaticSpec) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

type GoodsStaticSpecOfCategory struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Category GoodsCategory      `protobuf:"varint,1,opt,name=category,json=category,proto3,enum=api.gold_store.v1.GoodsCategory" json:"category"`
	Specs    []*GoodsStaticSpec `protobuf:"bytes,2,rep,name=specs,json=specs,proto3" json:"specs"`
}

func (x *GoodsStaticSpecOfCategory) Reset() {
	*x = GoodsStaticSpecOfCategory{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_background_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GoodsStaticSpecOfCategory) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GoodsStaticSpecOfCategory) ProtoMessage() {}

func (x *GoodsStaticSpecOfCategory) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_background_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GoodsStaticSpecOfCategory.ProtoReflect.Descriptor instead.
func (*GoodsStaticSpecOfCategory) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_background_proto_rawDescGZIP(), []int{12}
}

func (x *GoodsStaticSpecOfCategory) GetCategory() GoodsCategory {
	if x != nil {
		return x.Category
	}
	return GoodsCategory_GOODS_CATEGORY_PHYSICAL
}

func (x *GoodsStaticSpecOfCategory) GetSpecs() []*GoodsStaticSpec {
	if x != nil {
		return x.Specs
	}
	return nil
}

type GetStaticSpecReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Categories []*GoodsStaticSpecOfCategory `protobuf:"bytes,1,rep,name=categories,json=categories,proto3" json:"categories"`
}

func (x *GetStaticSpecReply) Reset() {
	*x = GetStaticSpecReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_background_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetStaticSpecReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetStaticSpecReply) ProtoMessage() {}

func (x *GetStaticSpecReply) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_background_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetStaticSpecReply.ProtoReflect.Descriptor instead.
func (*GetStaticSpecReply) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_background_proto_rawDescGZIP(), []int{13}
}

func (x *GetStaticSpecReply) GetCategories() []*GoodsStaticSpecOfCategory {
	if x != nil {
		return x.Categories
	}
	return nil
}

type GoodsSpecTranslate struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name   string            `protobuf:"bytes,2,opt,name=name,json=name,proto3" json:"name"`
	Values map[string]string `protobuf:"bytes,3,rep,name=values,json=values,proto3" json:"values" protobuf_key:"bytes,1,opt,name=key,json=key,proto3" protobuf_val:"bytes,2,opt,name=value,json=value,proto3"`
}

func (x *GoodsSpecTranslate) Reset() {
	*x = GoodsSpecTranslate{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_background_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GoodsSpecTranslate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GoodsSpecTranslate) ProtoMessage() {}

func (x *GoodsSpecTranslate) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_background_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GoodsSpecTranslate.ProtoReflect.Descriptor instead.
func (*GoodsSpecTranslate) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_background_proto_rawDescGZIP(), []int{14}
}

func (x *GoodsSpecTranslate) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GoodsSpecTranslate) GetValues() map[string]string {
	if x != nil {
		return x.Values
	}
	return nil
}

type GoodsTranslate struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name    string                         `protobuf:"bytes,1,opt,name=name,json=name,proto3" json:"name"`
	Details []*Image                       `protobuf:"bytes,2,rep,name=details,json=details,proto3" json:"details"`
	Specs   map[string]*GoodsSpecTranslate `protobuf:"bytes,3,rep,name=specs,json=specs,proto3" json:"specs" protobuf_key:"bytes,1,opt,name=key,json=key,proto3" protobuf_val:"bytes,2,opt,name=value,json=value,proto3"`
}

func (x *GoodsTranslate) Reset() {
	*x = GoodsTranslate{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_background_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GoodsTranslate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GoodsTranslate) ProtoMessage() {}

func (x *GoodsTranslate) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_background_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GoodsTranslate.ProtoReflect.Descriptor instead.
func (*GoodsTranslate) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_background_proto_rawDescGZIP(), []int{15}
}

func (x *GoodsTranslate) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GoodsTranslate) GetDetails() []*Image {
	if x != nil {
		return x.Details
	}
	return nil
}

func (x *GoodsTranslate) GetSpecs() map[string]*GoodsSpecTranslate {
	if x != nil {
		return x.Specs
	}
	return nil
}

type GoodsInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id            string                     `protobuf:"bytes,1,opt,name=id,json=id,proto3" json:"id"`
	Category      GoodsCategory              `protobuf:"varint,2,opt,name=category,json=category,proto3,enum=api.gold_store.v1.GoodsCategory" json:"category"`
	Name          string                     `protobuf:"bytes,3,opt,name=name,json=name,proto3" json:"name"`
	Title         string                     `protobuf:"bytes,4,opt,name=title,json=title,proto3" json:"title"`
	LabelId       []string                   `protobuf:"bytes,5,rep,name=label_id,json=labelId,proto3" json:"label_id"`
	Image         *Image                     `protobuf:"bytes,6,opt,name=image,json=image,proto3" json:"image"`
	Status        GoodsStatus                `protobuf:"varint,7,opt,name=status,json=status,proto3,enum=api.gold_store.v1.GoodsStatus" json:"status"`
	FreeShipping  bool                       `protobuf:"varint,8,opt,name=free_shipping,json=freeShipping,proto3" json:"free_shipping"`
	BasePrice     float32                    `protobuf:"fixed32,9,opt,name=base_price,json=basePrice,proto3" json:"base_price"`
	UseBasePrice  bool                       `protobuf:"varint,10,opt,name=use_base_price,json=useBasePrice,proto3" json:"use_base_price"`
	SelectedPrice float32                    `protobuf:"fixed32,11,opt,name=selected_price,json=selectedPrice,proto3" json:"selected_price"`
	Carousels     []*Image                   `protobuf:"bytes,16,rep,name=carousels,json=carousels,proto3" json:"carousels"`
	Details       []*Image                   `protobuf:"bytes,17,rep,name=details,json=details,proto3" json:"details"`
	Specs         []*GoodsSpec               `protobuf:"bytes,18,rep,name=specs,json=specs,proto3" json:"specs"`
	Skus          []*GoodsSku                `protobuf:"bytes,19,rep,name=skus,json=skus,proto3" json:"skus"`
	Translate     map[string]*GoodsTranslate `protobuf:"bytes,20,rep,name=translate,json=translate,proto3" json:"translate" protobuf_key:"bytes,1,opt,name=key,json=key,proto3" protobuf_val:"bytes,2,opt,name=value,json=value,proto3"`
	Description   string                     `protobuf:"bytes,21,opt,name=description,json=description,proto3" json:"description"`
}

func (x *GoodsInfo) Reset() {
	*x = GoodsInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_background_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GoodsInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GoodsInfo) ProtoMessage() {}

func (x *GoodsInfo) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_background_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GoodsInfo.ProtoReflect.Descriptor instead.
func (*GoodsInfo) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_background_proto_rawDescGZIP(), []int{16}
}

func (x *GoodsInfo) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *GoodsInfo) GetCategory() GoodsCategory {
	if x != nil {
		return x.Category
	}
	return GoodsCategory_GOODS_CATEGORY_PHYSICAL
}

func (x *GoodsInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GoodsInfo) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *GoodsInfo) GetLabelId() []string {
	if x != nil {
		return x.LabelId
	}
	return nil
}

func (x *GoodsInfo) GetImage() *Image {
	if x != nil {
		return x.Image
	}
	return nil
}

func (x *GoodsInfo) GetStatus() GoodsStatus {
	if x != nil {
		return x.Status
	}
	return GoodsStatus_GoodsStatusOff
}

func (x *GoodsInfo) GetFreeShipping() bool {
	if x != nil {
		return x.FreeShipping
	}
	return false
}

func (x *GoodsInfo) GetBasePrice() float32 {
	if x != nil {
		return x.BasePrice
	}
	return 0
}

func (x *GoodsInfo) GetUseBasePrice() bool {
	if x != nil {
		return x.UseBasePrice
	}
	return false
}

func (x *GoodsInfo) GetSelectedPrice() float32 {
	if x != nil {
		return x.SelectedPrice
	}
	return 0
}

func (x *GoodsInfo) GetCarousels() []*Image {
	if x != nil {
		return x.Carousels
	}
	return nil
}

func (x *GoodsInfo) GetDetails() []*Image {
	if x != nil {
		return x.Details
	}
	return nil
}

func (x *GoodsInfo) GetSpecs() []*GoodsSpec {
	if x != nil {
		return x.Specs
	}
	return nil
}

func (x *GoodsInfo) GetSkus() []*GoodsSku {
	if x != nil {
		return x.Skus
	}
	return nil
}

func (x *GoodsInfo) GetTranslate() map[string]*GoodsTranslate {
	if x != nil {
		return x.Translate
	}
	return nil
}

func (x *GoodsInfo) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

type AddGoodsReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,json=id,proto3" json:"id"`
}

func (x *AddGoodsReply) Reset() {
	*x = AddGoodsReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_background_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddGoodsReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddGoodsReply) ProtoMessage() {}

func (x *AddGoodsReply) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_background_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddGoodsReply.ProtoReflect.Descriptor instead.
func (*AddGoodsReply) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_background_proto_rawDescGZIP(), []int{17}
}

func (x *AddGoodsReply) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type DeleteGoodsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,json=id,proto3" json:"id"`
}

func (x *DeleteGoodsRequest) Reset() {
	*x = DeleteGoodsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_background_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteGoodsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteGoodsRequest) ProtoMessage() {}

func (x *DeleteGoodsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_background_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteGoodsRequest.ProtoReflect.Descriptor instead.
func (*DeleteGoodsRequest) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_background_proto_rawDescGZIP(), []int{18}
}

func (x *DeleteGoodsRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type GetGoodsInfoRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,json=id,proto3" json:"id"`
}

func (x *GetGoodsInfoRequest) Reset() {
	*x = GetGoodsInfoRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_background_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetGoodsInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetGoodsInfoRequest) ProtoMessage() {}

func (x *GetGoodsInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_background_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetGoodsInfoRequest.ProtoReflect.Descriptor instead.
func (*GetGoodsInfoRequest) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_background_proto_rawDescGZIP(), []int{19}
}

func (x *GetGoodsInfoRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type GetGoodsListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page    int32  `protobuf:"varint,1,opt,name=page,json=page,proto3" json:"page"`
	Size    int32  `protobuf:"varint,2,opt,name=size,json=size,proto3" json:"size"`
	Keyword string `protobuf:"bytes,3,opt,name=keyword,json=keyword,proto3" json:"keyword"`
	Status  int32  `protobuf:"varint,4,opt,name=status,json=status,proto3" json:"status"`
}

func (x *GetGoodsListRequest) Reset() {
	*x = GetGoodsListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_background_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetGoodsListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetGoodsListRequest) ProtoMessage() {}

func (x *GetGoodsListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_background_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetGoodsListRequest.ProtoReflect.Descriptor instead.
func (*GetGoodsListRequest) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_background_proto_rawDescGZIP(), []int{20}
}

func (x *GetGoodsListRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *GetGoodsListRequest) GetSize() int32 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *GetGoodsListRequest) GetKeyword() string {
	if x != nil {
		return x.Keyword
	}
	return ""
}

func (x *GetGoodsListRequest) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

type GetGoodsListReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Total int32        `protobuf:"varint,1,opt,name=total,json=total,proto3" json:"total"`
	Items []*GoodsInfo `protobuf:"bytes,2,rep,name=items,json=items,proto3" json:"items"`
}

func (x *GetGoodsListReply) Reset() {
	*x = GetGoodsListReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_background_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetGoodsListReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetGoodsListReply) ProtoMessage() {}

func (x *GetGoodsListReply) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_background_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetGoodsListReply.ProtoReflect.Descriptor instead.
func (*GetGoodsListReply) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_background_proto_rawDescGZIP(), []int{21}
}

func (x *GetGoodsListReply) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *GetGoodsListReply) GetItems() []*GoodsInfo {
	if x != nil {
		return x.Items
	}
	return nil
}

type GoodsStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id     string      `protobuf:"bytes,1,opt,name=id,json=id,proto3" json:"id"`
	Status GoodsStatus `protobuf:"varint,2,opt,name=status,json=status,proto3,enum=api.gold_store.v1.GoodsStatus" json:"status"`
}

func (x *GoodsStatusRequest) Reset() {
	*x = GoodsStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_background_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GoodsStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GoodsStatusRequest) ProtoMessage() {}

func (x *GoodsStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_background_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GoodsStatusRequest.ProtoReflect.Descriptor instead.
func (*GoodsStatusRequest) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_background_proto_rawDescGZIP(), []int{22}
}

func (x *GoodsStatusRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *GoodsStatusRequest) GetStatus() GoodsStatus {
	if x != nil {
		return x.Status
	}
	return GoodsStatus_GoodsStatusOff
}

type GiftCardSettingPageListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page int32 `protobuf:"varint,1,opt,name=page,json=page,proto3" json:"page"`
	Size int32 `protobuf:"varint,2,opt,name=size,json=size,proto3" json:"size"`
}

func (x *GiftCardSettingPageListRequest) Reset() {
	*x = GiftCardSettingPageListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_background_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GiftCardSettingPageListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GiftCardSettingPageListRequest) ProtoMessage() {}

func (x *GiftCardSettingPageListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_background_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GiftCardSettingPageListRequest.ProtoReflect.Descriptor instead.
func (*GiftCardSettingPageListRequest) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_background_proto_rawDescGZIP(), []int{23}
}

func (x *GiftCardSettingPageListRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *GiftCardSettingPageListRequest) GetSize() int32 {
	if x != nil {
		return x.Size
	}
	return 0
}

type GiftCardSettingPageListReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Items []*GiftCardSettingPageListReplyItem `protobuf:"bytes,1,rep,name=items,json=items,proto3" json:"items"`
	Total int32                               `protobuf:"varint,2,opt,name=total,json=total,proto3" json:"total"`
}

func (x *GiftCardSettingPageListReply) Reset() {
	*x = GiftCardSettingPageListReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_background_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GiftCardSettingPageListReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GiftCardSettingPageListReply) ProtoMessage() {}

func (x *GiftCardSettingPageListReply) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_background_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GiftCardSettingPageListReply.ProtoReflect.Descriptor instead.
func (*GiftCardSettingPageListReply) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_background_proto_rawDescGZIP(), []int{24}
}

func (x *GiftCardSettingPageListReply) GetItems() []*GiftCardSettingPageListReplyItem {
	if x != nil {
		return x.Items
	}
	return nil
}

func (x *GiftCardSettingPageListReply) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

type GiftCardSettingPageListReplyItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id               int32  `protobuf:"varint,1,opt,name=id,json=id,proto3" json:"id"`
	GoodsId          string `protobuf:"bytes,2,opt,name=goods_id,json=goodsId,proto3" json:"goods_id"`
	InDate           string `protobuf:"bytes,3,opt,name=in_date,json=inDate,proto3" json:"in_date"`
	InDateDays       string `protobuf:"bytes,4,opt,name=in_date_days,json=inDateDays,proto3" json:"in_date_days"`
	StartTime        string `protobuf:"bytes,5,opt,name=start_time,json=startTime,proto3" json:"start_time"`
	EndTime          string `protobuf:"bytes,6,opt,name=end_time,json=endTime,proto3" json:"end_time"`
	UseTimes         int32  `protobuf:"varint,7,opt,name=use_times,json=useTimes,proto3" json:"use_times"`
	UsedTimes        int32  `protobuf:"varint,8,opt,name=used_times,json=usedTimes,proto3" json:"used_times"`
	PromotionChannel string `protobuf:"bytes,9,opt,name=promotion_channel,json=promotionChannel,proto3" json:"promotion_channel"`
	Proposer         string `protobuf:"bytes,10,opt,name=proposer,json=proposer,proto3" json:"proposer"`
	CreatedUser      string `protobuf:"bytes,11,opt,name=created_user,json=createdUser,proto3" json:"created_user"`
	CreatedAt        string `protobuf:"bytes,12,opt,name=created_at,json=createdAt,proto3" json:"created_at"`
}

func (x *GiftCardSettingPageListReplyItem) Reset() {
	*x = GiftCardSettingPageListReplyItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_background_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GiftCardSettingPageListReplyItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GiftCardSettingPageListReplyItem) ProtoMessage() {}

func (x *GiftCardSettingPageListReplyItem) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_background_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GiftCardSettingPageListReplyItem.ProtoReflect.Descriptor instead.
func (*GiftCardSettingPageListReplyItem) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_background_proto_rawDescGZIP(), []int{25}
}

func (x *GiftCardSettingPageListReplyItem) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GiftCardSettingPageListReplyItem) GetGoodsId() string {
	if x != nil {
		return x.GoodsId
	}
	return ""
}

func (x *GiftCardSettingPageListReplyItem) GetInDate() string {
	if x != nil {
		return x.InDate
	}
	return ""
}

func (x *GiftCardSettingPageListReplyItem) GetInDateDays() string {
	if x != nil {
		return x.InDateDays
	}
	return ""
}

func (x *GiftCardSettingPageListReplyItem) GetStartTime() string {
	if x != nil {
		return x.StartTime
	}
	return ""
}

func (x *GiftCardSettingPageListReplyItem) GetEndTime() string {
	if x != nil {
		return x.EndTime
	}
	return ""
}

func (x *GiftCardSettingPageListReplyItem) GetUseTimes() int32 {
	if x != nil {
		return x.UseTimes
	}
	return 0
}

func (x *GiftCardSettingPageListReplyItem) GetUsedTimes() int32 {
	if x != nil {
		return x.UsedTimes
	}
	return 0
}

func (x *GiftCardSettingPageListReplyItem) GetPromotionChannel() string {
	if x != nil {
		return x.PromotionChannel
	}
	return ""
}

func (x *GiftCardSettingPageListReplyItem) GetProposer() string {
	if x != nil {
		return x.Proposer
	}
	return ""
}

func (x *GiftCardSettingPageListReplyItem) GetCreatedUser() string {
	if x != nil {
		return x.CreatedUser
	}
	return ""
}

func (x *GiftCardSettingPageListReplyItem) GetCreatedAt() string {
	if x != nil {
		return x.CreatedAt
	}
	return ""
}

type EditGiftCardSettingRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GoodsId          string `protobuf:"bytes,1,opt,name=goods_id,json=goodsId,proto3" json:"goods_id"`
	InDate           int32  `protobuf:"varint,2,opt,name=in_date,json=inDate,proto3" json:"in_date"`
	InDateDays       int32  `protobuf:"varint,3,opt,name=in_date_days,json=inDateDays,proto3" json:"in_date_days"`
	StartTime        string `protobuf:"bytes,4,opt,name=start_time,json=startTime,proto3" json:"start_time"`
	EndTime          string `protobuf:"bytes,5,opt,name=end_time,json=endTime,proto3" json:"end_time"`
	UseTimes         int32  `protobuf:"varint,6,opt,name=use_times,json=useTimes,proto3" json:"use_times"`
	PromotionChannel string `protobuf:"bytes,7,opt,name=promotion_channel,json=promotionChannel,proto3" json:"promotion_channel"`
	Proposer         string `protobuf:"bytes,8,opt,name=proposer,json=proposer,proto3" json:"proposer"`
	CreatedUser      string `protobuf:"bytes,9,opt,name=created_user,json=createdUser,proto3" json:"created_user"`
	Id               int32  `protobuf:"varint,10,opt,name=id,json=id,proto3" json:"id"`
	IsPinkage        int32  `protobuf:"varint,11,opt,name=is_pinkage,json=isPinkage,proto3" json:"is_pinkage"`
	Image            string `protobuf:"bytes,12,opt,name=image,json=image,proto3" json:"image"`
}

func (x *EditGiftCardSettingRequest) Reset() {
	*x = EditGiftCardSettingRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_background_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EditGiftCardSettingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EditGiftCardSettingRequest) ProtoMessage() {}

func (x *EditGiftCardSettingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_background_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EditGiftCardSettingRequest.ProtoReflect.Descriptor instead.
func (*EditGiftCardSettingRequest) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_background_proto_rawDescGZIP(), []int{26}
}

func (x *EditGiftCardSettingRequest) GetGoodsId() string {
	if x != nil {
		return x.GoodsId
	}
	return ""
}

func (x *EditGiftCardSettingRequest) GetInDate() int32 {
	if x != nil {
		return x.InDate
	}
	return 0
}

func (x *EditGiftCardSettingRequest) GetInDateDays() int32 {
	if x != nil {
		return x.InDateDays
	}
	return 0
}

func (x *EditGiftCardSettingRequest) GetStartTime() string {
	if x != nil {
		return x.StartTime
	}
	return ""
}

func (x *EditGiftCardSettingRequest) GetEndTime() string {
	if x != nil {
		return x.EndTime
	}
	return ""
}

func (x *EditGiftCardSettingRequest) GetUseTimes() int32 {
	if x != nil {
		return x.UseTimes
	}
	return 0
}

func (x *EditGiftCardSettingRequest) GetPromotionChannel() string {
	if x != nil {
		return x.PromotionChannel
	}
	return ""
}

func (x *EditGiftCardSettingRequest) GetProposer() string {
	if x != nil {
		return x.Proposer
	}
	return ""
}

func (x *EditGiftCardSettingRequest) GetCreatedUser() string {
	if x != nil {
		return x.CreatedUser
	}
	return ""
}

func (x *EditGiftCardSettingRequest) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *EditGiftCardSettingRequest) GetIsPinkage() int32 {
	if x != nil {
		return x.IsPinkage
	}
	return 0
}

func (x *EditGiftCardSettingRequest) GetImage() string {
	if x != nil {
		return x.Image
	}
	return ""
}

type EditGiftCardSettingReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *EditGiftCardSettingReply) Reset() {
	*x = EditGiftCardSettingReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_background_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EditGiftCardSettingReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EditGiftCardSettingReply) ProtoMessage() {}

func (x *EditGiftCardSettingReply) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_background_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EditGiftCardSettingReply.ProtoReflect.Descriptor instead.
func (*EditGiftCardSettingReply) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_background_proto_rawDescGZIP(), []int{27}
}

type DeleteGiftCardSettingRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int32 `protobuf:"varint,1,opt,name=id,json=id,proto3" json:"id"`
}

func (x *DeleteGiftCardSettingRequest) Reset() {
	*x = DeleteGiftCardSettingRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_background_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteGiftCardSettingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteGiftCardSettingRequest) ProtoMessage() {}

func (x *DeleteGiftCardSettingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_background_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteGiftCardSettingRequest.ProtoReflect.Descriptor instead.
func (*DeleteGiftCardSettingRequest) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_background_proto_rawDescGZIP(), []int{28}
}

func (x *DeleteGiftCardSettingRequest) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

type DeleteGiftCardSettingReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeleteGiftCardSettingReply) Reset() {
	*x = DeleteGiftCardSettingReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_background_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteGiftCardSettingReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteGiftCardSettingReply) ProtoMessage() {}

func (x *DeleteGiftCardSettingReply) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_background_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteGiftCardSettingReply.ProtoReflect.Descriptor instead.
func (*DeleteGiftCardSettingReply) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_background_proto_rawDescGZIP(), []int{29}
}

type GetSingleGiftCardSettingRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int32 `protobuf:"varint,1,opt,name=id,json=id,proto3" json:"id"`
}

func (x *GetSingleGiftCardSettingRequest) Reset() {
	*x = GetSingleGiftCardSettingRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_background_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSingleGiftCardSettingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSingleGiftCardSettingRequest) ProtoMessage() {}

func (x *GetSingleGiftCardSettingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_background_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSingleGiftCardSettingRequest.ProtoReflect.Descriptor instead.
func (*GetSingleGiftCardSettingRequest) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_background_proto_rawDescGZIP(), []int{30}
}

func (x *GetSingleGiftCardSettingRequest) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

type GetSingleGiftCardSettingReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GoodsId          string `protobuf:"bytes,1,opt,name=goods_id,json=goodsId,proto3" json:"goods_id"`
	InDate           int32  `protobuf:"varint,2,opt,name=in_date,json=inDate,proto3" json:"in_date"`
	InDateDays       int32  `protobuf:"varint,3,opt,name=in_date_days,json=inDateDays,proto3" json:"in_date_days"`
	StartTime        string `protobuf:"bytes,4,opt,name=start_time,json=startTime,proto3" json:"start_time"`
	EndTime          string `protobuf:"bytes,5,opt,name=end_time,json=endTime,proto3" json:"end_time"`
	UseTimes         int32  `protobuf:"varint,6,opt,name=use_times,json=useTimes,proto3" json:"use_times"`
	PromotionChannel string `protobuf:"bytes,7,opt,name=promotion_channel,json=promotionChannel,proto3" json:"promotion_channel"`
	Proposer         string `protobuf:"bytes,8,opt,name=proposer,json=proposer,proto3" json:"proposer"`
	CreatedUser      string `protobuf:"bytes,9,opt,name=created_user,json=createdUser,proto3" json:"created_user"`
	Id               int64  `protobuf:"varint,10,opt,name=id,json=id,proto3" json:"id"`
	GoodsName        string `protobuf:"bytes,11,opt,name=goods_name,json=goodsName,proto3" json:"goods_name"`
	IsPinkage        int32  `protobuf:"varint,12,opt,name=is_pinkage,json=isPinkage,proto3" json:"is_pinkage"`
	Image            string `protobuf:"bytes,13,opt,name=image,json=image,proto3" json:"image"`
}

func (x *GetSingleGiftCardSettingReply) Reset() {
	*x = GetSingleGiftCardSettingReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_background_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSingleGiftCardSettingReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSingleGiftCardSettingReply) ProtoMessage() {}

func (x *GetSingleGiftCardSettingReply) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_background_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSingleGiftCardSettingReply.ProtoReflect.Descriptor instead.
func (*GetSingleGiftCardSettingReply) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_background_proto_rawDescGZIP(), []int{31}
}

func (x *GetSingleGiftCardSettingReply) GetGoodsId() string {
	if x != nil {
		return x.GoodsId
	}
	return ""
}

func (x *GetSingleGiftCardSettingReply) GetInDate() int32 {
	if x != nil {
		return x.InDate
	}
	return 0
}

func (x *GetSingleGiftCardSettingReply) GetInDateDays() int32 {
	if x != nil {
		return x.InDateDays
	}
	return 0
}

func (x *GetSingleGiftCardSettingReply) GetStartTime() string {
	if x != nil {
		return x.StartTime
	}
	return ""
}

func (x *GetSingleGiftCardSettingReply) GetEndTime() string {
	if x != nil {
		return x.EndTime
	}
	return ""
}

func (x *GetSingleGiftCardSettingReply) GetUseTimes() int32 {
	if x != nil {
		return x.UseTimes
	}
	return 0
}

func (x *GetSingleGiftCardSettingReply) GetPromotionChannel() string {
	if x != nil {
		return x.PromotionChannel
	}
	return ""
}

func (x *GetSingleGiftCardSettingReply) GetProposer() string {
	if x != nil {
		return x.Proposer
	}
	return ""
}

func (x *GetSingleGiftCardSettingReply) GetCreatedUser() string {
	if x != nil {
		return x.CreatedUser
	}
	return ""
}

func (x *GetSingleGiftCardSettingReply) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GetSingleGiftCardSettingReply) GetGoodsName() string {
	if x != nil {
		return x.GoodsName
	}
	return ""
}

func (x *GetSingleGiftCardSettingReply) GetIsPinkage() int32 {
	if x != nil {
		return x.IsPinkage
	}
	return 0
}

func (x *GetSingleGiftCardSettingReply) GetImage() string {
	if x != nil {
		return x.Image
	}
	return ""
}

type GetSingleGiftCardSettingIsSendRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int32 `protobuf:"varint,1,opt,name=id,json=id,proto3" json:"id"`
}

func (x *GetSingleGiftCardSettingIsSendRequest) Reset() {
	*x = GetSingleGiftCardSettingIsSendRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_background_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSingleGiftCardSettingIsSendRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSingleGiftCardSettingIsSendRequest) ProtoMessage() {}

func (x *GetSingleGiftCardSettingIsSendRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_background_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSingleGiftCardSettingIsSendRequest.ProtoReflect.Descriptor instead.
func (*GetSingleGiftCardSettingIsSendRequest) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_background_proto_rawDescGZIP(), []int{32}
}

func (x *GetSingleGiftCardSettingIsSendRequest) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

type GetSingleGiftCardSettingIsSendReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IsUse bool `protobuf:"varint,1,opt,name=is_use,json=isUse,proto3" json:"is_use"`
}

func (x *GetSingleGiftCardSettingIsSendReply) Reset() {
	*x = GetSingleGiftCardSettingIsSendReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_background_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSingleGiftCardSettingIsSendReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSingleGiftCardSettingIsSendReply) ProtoMessage() {}

func (x *GetSingleGiftCardSettingIsSendReply) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_background_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSingleGiftCardSettingIsSendReply.ProtoReflect.Descriptor instead.
func (*GetSingleGiftCardSettingIsSendReply) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_background_proto_rawDescGZIP(), []int{33}
}

func (x *GetSingleGiftCardSettingIsSendReply) GetIsUse() bool {
	if x != nil {
		return x.IsUse
	}
	return false
}

type SendGiftCardRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserIds   []string `protobuf:"bytes,1,rep,name=user_ids,json=userIds,proto3" json:"user_ids"`
	SettingId int32    `protobuf:"varint,2,opt,name=setting_id,json=settingId,proto3" json:"setting_id"`
}

func (x *SendGiftCardRequest) Reset() {
	*x = SendGiftCardRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_background_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SendGiftCardRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendGiftCardRequest) ProtoMessage() {}

func (x *SendGiftCardRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_background_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendGiftCardRequest.ProtoReflect.Descriptor instead.
func (*SendGiftCardRequest) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_background_proto_rawDescGZIP(), []int{34}
}

func (x *SendGiftCardRequest) GetUserIds() []string {
	if x != nil {
		return x.UserIds
	}
	return nil
}

func (x *SendGiftCardRequest) GetSettingId() int32 {
	if x != nil {
		return x.SettingId
	}
	return 0
}

type SendGiftCardReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SendGiftCardReply) Reset() {
	*x = SendGiftCardReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_background_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SendGiftCardReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendGiftCardReply) ProtoMessage() {}

func (x *SendGiftCardReply) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_background_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendGiftCardReply.ProtoReflect.Descriptor instead.
func (*SendGiftCardReply) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_background_proto_rawDescGZIP(), []int{35}
}

type SendGiftCardRecordRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SettingId int32 `protobuf:"varint,1,opt,name=setting_id,json=settingId,proto3" json:"setting_id"`
	Page      int32 `protobuf:"varint,2,opt,name=page,json=page,proto3" json:"page"`
	Size      int32 `protobuf:"varint,3,opt,name=size,json=size,proto3" json:"size"`
}

func (x *SendGiftCardRecordRequest) Reset() {
	*x = SendGiftCardRecordRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_background_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SendGiftCardRecordRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendGiftCardRecordRequest) ProtoMessage() {}

func (x *SendGiftCardRecordRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_background_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendGiftCardRecordRequest.ProtoReflect.Descriptor instead.
func (*SendGiftCardRecordRequest) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_background_proto_rawDescGZIP(), []int{36}
}

func (x *SendGiftCardRecordRequest) GetSettingId() int32 {
	if x != nil {
		return x.SettingId
	}
	return 0
}

func (x *SendGiftCardRecordRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *SendGiftCardRecordRequest) GetSize() int32 {
	if x != nil {
		return x.Size
	}
	return 0
}

type SendGiftCardRecordReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Items []*SendGiftCardRecordItem `protobuf:"bytes,1,rep,name=items,json=items,proto3" json:"items"`
	Total int32                     `protobuf:"varint,2,opt,name=total,json=total,proto3" json:"total"`
}

func (x *SendGiftCardRecordReply) Reset() {
	*x = SendGiftCardRecordReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_background_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SendGiftCardRecordReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendGiftCardRecordReply) ProtoMessage() {}

func (x *SendGiftCardRecordReply) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_background_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendGiftCardRecordReply.ProtoReflect.Descriptor instead.
func (*SendGiftCardRecordReply) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_background_proto_rawDescGZIP(), []int{37}
}

func (x *SendGiftCardRecordReply) GetItems() []*SendGiftCardRecordItem {
	if x != nil {
		return x.Items
	}
	return nil
}

func (x *SendGiftCardRecordReply) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

type SendGiftCardRecordItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId      string `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id"`
	ReceiveTime string `protobuf:"bytes,2,opt,name=receive_time,json=receiveTime,proto3" json:"receive_time"`
	UsedTime    string `protobuf:"bytes,3,opt,name=used_time,json=usedTime,proto3" json:"used_time"`
	Status      int32  `protobuf:"varint,4,opt,name=status,json=status,proto3" json:"status"`
	ShowStatus  string `protobuf:"bytes,5,opt,name=show_status,json=showStatus,proto3" json:"show_status"`
	OrderId     string `protobuf:"bytes,6,opt,name=orderId,json=orderId,proto3" json:"orderId"`
}

func (x *SendGiftCardRecordItem) Reset() {
	*x = SendGiftCardRecordItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_background_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SendGiftCardRecordItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendGiftCardRecordItem) ProtoMessage() {}

func (x *SendGiftCardRecordItem) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_background_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendGiftCardRecordItem.ProtoReflect.Descriptor instead.
func (*SendGiftCardRecordItem) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_background_proto_rawDescGZIP(), []int{38}
}

func (x *SendGiftCardRecordItem) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *SendGiftCardRecordItem) GetReceiveTime() string {
	if x != nil {
		return x.ReceiveTime
	}
	return ""
}

func (x *SendGiftCardRecordItem) GetUsedTime() string {
	if x != nil {
		return x.UsedTime
	}
	return ""
}

func (x *SendGiftCardRecordItem) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *SendGiftCardRecordItem) GetShowStatus() string {
	if x != nil {
		return x.ShowStatus
	}
	return ""
}

func (x *SendGiftCardRecordItem) GetOrderId() string {
	if x != nil {
		return x.OrderId
	}
	return ""
}

var File_gold_store_v1_background_proto protoreflect.FileDescriptor

var file_gold_store_v1_background_proto_rawDesc = []byte{
	0x0a, 0x1e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2f, 0x76, 0x31, 0x2f,
	0x62, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x11, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65,
	0x2e, 0x76, 0x31, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f,
	0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x2d, 0x67, 0x65, 0x6e, 0x2d, 0x6f, 0x70,
	0x65, 0x6e, 0x61, 0x70, 0x69, 0x76, 0x32, 0x2f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f,
	0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x13, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1a, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f,
	0x72, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x18, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2f, 0x76,
	0x31, 0x2f, 0x74, 0x61, 0x73, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x18, 0x67, 0x6f,
	0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x69, 0x67, 0x6e,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xaa, 0x01, 0x0a, 0x13, 0x4f, 0x72, 0x64, 0x65, 0x72,
	0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x29,
	0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x6e, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x0e, 0x92, 0x41, 0x0b, 0x2a, 0x09, 0xe8, 0xae, 0xa2, 0xe5, 0x8d, 0x95, 0xe5, 0x8f, 0xb7,
	0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x4e, 0x6f, 0x12, 0x32, 0x0a, 0x0b, 0x74, 0x72, 0x61,
	0x63, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x6e, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11,
	0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe7, 0x89, 0xa9, 0xe6, 0xb5, 0x81, 0xe5, 0x8d, 0x95, 0xe5, 0x8f,
	0xb7, 0x52, 0x0a, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x4e, 0x6f, 0x12, 0x34, 0x0a,
	0x0c, 0x63, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0xbf, 0xab, 0xe9, 0x80, 0x92,
	0xe5, 0x85, 0xac, 0xe5, 0x8f, 0xb8, 0x52, 0x0b, 0x63, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x4e,
	0x61, 0x6d, 0x65, 0x22, 0x84, 0x05, 0x0a, 0x15, 0x41, 0x64, 0x6d, 0x69, 0x6e, 0x4f, 0x72, 0x64,
	0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x28, 0x0a,
	0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x6e, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe6, 0xa0, 0x87, 0xe7, 0xad, 0xbe, 0x49, 0x44, 0x52, 0x07,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x4e, 0x6f, 0x12, 0x30, 0x0a, 0x0a, 0x67, 0x6f, 0x6f, 0x64, 0x73,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e,
	0x2a, 0x0c, 0xe5, 0x95, 0x86, 0xe5, 0x93, 0x81, 0xe5, 0x90, 0x8d, 0xe7, 0xa7, 0xb0, 0x52, 0x09,
	0x67, 0x6f, 0x6f, 0x64, 0x73, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0xe8, 0x01, 0x0a, 0x0e, 0x70, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74,
	0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65,
	0x74, 0x68, 0x6f, 0x64, 0x42, 0x9e, 0x01, 0x92, 0x41, 0x9a, 0x01, 0x2a, 0x97, 0x01, 0xe6, 0x94,
	0xaf, 0xe4, 0xbb, 0x98, 0xe6, 0x96, 0xb9, 0xe5, 0xbc, 0x8f, 0x3a, 0x30, 0xef, 0xbc, 0x9a, 0xe9,
	0x87, 0x91, 0xe5, 0xb8, 0x81, 0xef, 0xbc, 0x9b, 0x31, 0xef, 0xbc, 0x9a, 0xe7, 0xa4, 0xbc, 0xe5,
	0x93, 0x81, 0xe5, 0x8d, 0xa1, 0xef, 0xbc, 0x9b, 0x32, 0xef, 0xbc, 0x9a, 0xe7, 0x94, 0xa8, 0xe6,
	0x88, 0xb7, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a, 0xa1, 0xef, 0xbc, 0x9b, 0x33, 0xef, 0xbc, 0x9a, 0xe6,
	0x94, 0xaf, 0xe4, 0xbb, 0x98, 0xe5, 0xae, 0x9d, 0xef, 0xbc, 0x9b, 0x34, 0xef, 0xbc, 0x9a, 0xe5,
	0xbe, 0xae, 0xe4, 0xbf, 0xa1, 0xef, 0xbc, 0x9b, 0x35, 0xef, 0xbc, 0x9a, 0xe8, 0x8b, 0xb9, 0xe6,
	0x9e, 0x9c, 0xe5, 0x86, 0x85, 0xe8, 0xb4, 0xad, 0xef, 0xbc, 0x9b, 0x36, 0xef, 0xbc, 0x9a, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0xe5, 0x86, 0x85, 0xe8, 0xb4, 0xad, 0xef, 0xbc, 0x9b, 0xe5, 0xa6,
	0x82, 0xe6, 0x9e, 0x9c, 0xe6, 0xb2, 0xa1, 0xe6, 0x9c, 0x89, 0xe7, 0xad, 0x9b, 0xe9, 0x80, 0x89,
	0xe4, 0xbc, 0xa0, 0x2d, 0x31, 0x52, 0x0d, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65,
	0x74, 0x68, 0x6f, 0x64, 0x12, 0xc6, 0x01, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64,
	0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x8d, 0x01, 0x92, 0x41, 0x89, 0x01, 0x2a, 0x86, 0x01, 0xe8,
	0xae, 0xa2, 0xe5, 0x8d, 0x95, 0xe7, 0x8a, 0xb6, 0xe6, 0x80, 0x81, 0x3a, 0x30, 0xef, 0xbc, 0x9a,
	0xe6, 0x9c, 0xaa, 0xe7, 0x9f, 0xa5, 0xe7, 0x8a, 0xb6, 0xe6, 0x80, 0x81, 0xef, 0xbc, 0x9b, 0x31,
	0xef, 0xbc, 0x9a, 0xe5, 0xbe, 0x85, 0xe6, 0x94, 0xaf, 0xe4, 0xbb, 0x98, 0xef, 0xbc, 0x9b, 0x32,
	0xef, 0xbc, 0x9a, 0xe5, 0xb7, 0xb2, 0xe6, 0x94, 0xaf, 0xe4, 0xbb, 0x98, 0xef, 0xbc, 0x9b, 0x33,
	0xef, 0xbc, 0x9a, 0xe5, 0xb7, 0xb2, 0xe5, 0x8f, 0x96, 0xe6, 0xb6, 0x88, 0xef, 0xbc, 0x9b, 0x34,
	0xef, 0xbc, 0x9a, 0xe5, 0xbe, 0x85, 0xe6, 0x94, 0xb6, 0xe8, 0xb4, 0xa7, 0xef, 0xbc, 0x9b, 0x35,
	0xef, 0xbc, 0x9a, 0xe5, 0xb7, 0xb2, 0xe5, 0xae, 0x8c, 0xe6, 0x88, 0x90, 0xef, 0xbc, 0x9b, 0xe5,
	0xa6, 0x82, 0xe6, 0x9e, 0x9c, 0xe6, 0xb2, 0xa1, 0xe6, 0x9c, 0x89, 0xe7, 0xad, 0x9b, 0xe9, 0x80,
	0x89, 0xe4, 0xbc, 0xa0, 0x30, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x30, 0x0a,
	0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x42, 0x1c, 0x92, 0x41, 0x19,
	0x2a, 0x17, 0xe5, 0x88, 0x86, 0xe9, 0xa1, 0xb5, 0xe5, 0xa4, 0xa7, 0xe5, 0xb0, 0x8f, 0xef, 0xbc,
	0x9b, 0xe9, 0xbb, 0x98, 0xe8, 0xae, 0xa4, 0x31, 0x30, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12,
	0x29, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x42, 0x15, 0x92,
	0x41, 0x12, 0x2a, 0x10, 0xe9, 0xa1, 0xb5, 0xe6, 0x95, 0xb0, 0xef, 0xbc, 0x9b, 0xe9, 0xbb, 0x98,
	0xe8, 0xae, 0xa4, 0x31, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x22, 0xad, 0x08, 0x0a, 0x0a, 0x41,
	0x64, 0x6d, 0x69, 0x6e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x28, 0x0a, 0x08, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x5f, 0x6e, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d, 0x92, 0x41, 0x0a,
	0x2a, 0x08, 0xe6, 0xa0, 0x87, 0xe7, 0xad, 0xbe, 0x49, 0x44, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x4e, 0x6f, 0x12, 0x28, 0x0a, 0x08, 0x67, 0x6f, 0x6f, 0x64, 0x73, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe5, 0x95, 0x86, 0xe5,
	0x93, 0x81, 0x49, 0x44, 0x52, 0x07, 0x67, 0x6f, 0x6f, 0x64, 0x73, 0x49, 0x64, 0x12, 0x26, 0x0a,
	0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d,
	0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0x49, 0x44, 0x52, 0x06, 0x75,
	0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x30, 0x0a, 0x0a, 0x67, 0x6f, 0x6f, 0x64, 0x73, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c,
	0xe5, 0x95, 0x86, 0xe5, 0x93, 0x81, 0xe5, 0x90, 0x8d, 0xe7, 0xa7, 0xb0, 0x52, 0x09, 0x67, 0x6f,
	0x6f, 0x64, 0x73, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x28, 0x0a, 0x06, 0x73, 0x6b, 0x75, 0x5f, 0x69,
	0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0x95,
	0x86, 0xe5, 0x93, 0x81, 0x53, 0x4b, 0x55, 0x20, 0x49, 0x44, 0x52, 0x05, 0x73, 0x6b, 0x75, 0x49,
	0x64, 0x12, 0x31, 0x0a, 0x09, 0x73, 0x70, 0x65, 0x63, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x14, 0x92, 0x41, 0x11, 0x2a, 0x0f, 0xe5, 0x95, 0x86, 0xe5, 0x93,
	0x81, 0x53, 0x4b, 0x55, 0xe6, 0x8f, 0x8f, 0xe8, 0xbf, 0xb0, 0x52, 0x08, 0x73, 0x70, 0x65, 0x63,
	0x44, 0x65, 0x73, 0x63, 0x12, 0x27, 0x0a, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x02, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0x95, 0x86, 0xe5, 0x93, 0x81,
	0xe4, 0xbb, 0xb7, 0xe6, 0xa0, 0xbc, 0x52, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x12, 0x2d, 0x0a,
	0x08, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x42,
	0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0x95, 0x86, 0xe5, 0x93, 0x81, 0xe6, 0x95, 0xb0, 0xe9,
	0x87, 0x8f, 0x52, 0x08, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x12, 0x47, 0x0a, 0x07,
	0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76,
	0x31, 0x2e, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c,
	0xe6, 0x94, 0xb6, 0xe8, 0xb4, 0xa7, 0xe5, 0x9c, 0xb0, 0xe5, 0x9d, 0x80, 0x52, 0x07, 0x61, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x34, 0x0a, 0x0c, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x61,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x02, 0x42, 0x11, 0x92, 0x41, 0x0e,
	0x2a, 0x0c, 0xe5, 0x95, 0x86, 0xe5, 0x93, 0x81, 0xe6, 0x80, 0xbb, 0xe4, 0xbb, 0xb7, 0x52, 0x0b,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x36, 0x0a, 0x0d, 0x70,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x14, 0x20, 0x01,
	0x28, 0x02, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe6, 0x94, 0xaf, 0xe4, 0xbb, 0x98, 0xe6,
	0x80, 0xbb, 0xe9, 0xa2, 0x9d, 0x52, 0x0c, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x6f,
	0x74, 0x61, 0x6c, 0x12, 0x2e, 0x0a, 0x0c, 0x73, 0x68, 0x69, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x5f,
	0x66, 0x65, 0x65, 0x18, 0x15, 0x20, 0x01, 0x28, 0x02, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06,
	0xe8, 0xbf, 0x90, 0xe8, 0xb4, 0xb9, 0x52, 0x0b, 0x73, 0x68, 0x69, 0x70, 0x70, 0x69, 0x6e, 0x67,
	0x46, 0x65, 0x65, 0x12, 0xcc, 0x01, 0x0a, 0x0e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f,
	0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x18, 0x16, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x20, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31,
	0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x42, 0x82,
	0x01, 0x92, 0x41, 0x7f, 0x2a, 0x7d, 0xe6, 0x94, 0xaf, 0xe4, 0xbb, 0x98, 0xe6, 0x96, 0xb9, 0xe5,
	0xbc, 0x8f, 0x3a, 0x30, 0xef, 0xbc, 0x9a, 0xe9, 0x87, 0x91, 0xe5, 0xb8, 0x81, 0xef, 0xbc, 0x9b,
	0x31, 0xef, 0xbc, 0x9a, 0xe7, 0xa4, 0xbc, 0xe5, 0x93, 0x81, 0xe5, 0x8d, 0xa1, 0xef, 0xbc, 0x9b,
	0x32, 0xef, 0xbc, 0x9a, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a, 0xa1,
	0xef, 0xbc, 0x9b, 0x33, 0xef, 0xbc, 0x9a, 0xe6, 0x94, 0xaf, 0xe4, 0xbb, 0x98, 0xe5, 0xae, 0x9d,
	0xef, 0xbc, 0x9b, 0x34, 0xef, 0xbc, 0x9a, 0xe5, 0xbe, 0xae, 0xe4, 0xbf, 0xa1, 0xef, 0xbc, 0x9b,
	0x35, 0xef, 0xbc, 0x9a, 0xe8, 0x8b, 0xb9, 0xe6, 0x9e, 0x9c, 0xe5, 0x86, 0x85, 0xe8, 0xb4, 0xad,
	0xef, 0xbc, 0x9b, 0x36, 0xef, 0xbc, 0x9a, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0xe5, 0x86, 0x85,
	0xe8, 0xb4, 0xad, 0x52, 0x0d, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68,
	0x6f, 0x64, 0x12, 0xaa, 0x01, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x17, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73,
	0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x42, 0x72, 0x92, 0x41, 0x6f, 0x2a, 0x6d, 0xe8, 0xae, 0xa2, 0xe5, 0x8d, 0x95,
	0xe7, 0x8a, 0xb6, 0xe6, 0x80, 0x81, 0x3a, 0x30, 0xef, 0xbc, 0x9a, 0xe6, 0x9c, 0xaa, 0xe7, 0x9f,
	0xa5, 0xe7, 0x8a, 0xb6, 0xe6, 0x80, 0x81, 0xef, 0xbc, 0x9b, 0x31, 0xef, 0xbc, 0x9a, 0xe5, 0xbe,
	0x85, 0xe6, 0x94, 0xaf, 0xe4, 0xbb, 0x98, 0xef, 0xbc, 0x9b, 0x32, 0xef, 0xbc, 0x9a, 0xe5, 0xb7,
	0xb2, 0xe6, 0x94, 0xaf, 0xe4, 0xbb, 0x98, 0xef, 0xbc, 0x9b, 0x33, 0xef, 0xbc, 0x9a, 0xe5, 0xb7,
	0xb2, 0xe5, 0x8f, 0x96, 0xe6, 0xb6, 0x88, 0xef, 0xbc, 0x9b, 0x34, 0xef, 0xbc, 0x9a, 0xe5, 0xbe,
	0x85, 0xe6, 0x94, 0xb6, 0xe8, 0xb4, 0xa7, 0xef, 0xbc, 0x9b, 0x35, 0xef, 0xbc, 0x9a, 0xe5, 0xb7,
	0xb2, 0xe5, 0xae, 0x8c, 0xe6, 0x88, 0x90, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x30, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x18, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0x88, 0x9b, 0xe5, 0xbb, 0xba,
	0xe6, 0x97, 0xb6, 0xe9, 0x97, 0xb4, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41,
	0x74, 0x12, 0x27, 0x0a, 0x07, 0x77, 0x69, 0x6b, 0x69, 0x5f, 0x6e, 0x6f, 0x18, 0x19, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x0e, 0x92, 0x41, 0x0b, 0x2a, 0x09, 0xe5, 0xa4, 0xa9, 0xe7, 0x9c, 0xbc, 0xe5,
	0x8f, 0xb7, 0x52, 0x06, 0x77, 0x69, 0x6b, 0x69, 0x4e, 0x6f, 0x22, 0x62, 0x0a, 0x13, 0x41, 0x64,
	0x6d, 0x69, 0x6e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6c,
	0x79, 0x12, 0x35, 0x0a, 0x06, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x1d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f,
	0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64, 0x6d, 0x69, 0x6e, 0x4f, 0x72, 0x64, 0x65, 0x72,
	0x52, 0x06, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61,
	0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x22, 0x43,
	0x0a, 0x17, 0x41, 0x64, 0x6d, 0x69, 0x6e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x28, 0x0a, 0x08, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x5f, 0x6e, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d, 0x92, 0x41, 0x0a,
	0x2a, 0x08, 0xe6, 0xa0, 0x87, 0xe7, 0xad, 0xbe, 0x49, 0x44, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x4e, 0x6f, 0x22, 0xda, 0x0a, 0x0a, 0x15, 0x41, 0x64, 0x6d, 0x69, 0x6e, 0x4f, 0x72, 0x64,
	0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x28, 0x0a,
	0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x6e, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe6, 0xa0, 0x87, 0xe7, 0xad, 0xbe, 0x49, 0x44, 0x52, 0x07,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x4e, 0x6f, 0x12, 0x28, 0x0a, 0x08, 0x67, 0x6f, 0x6f, 0x64, 0x73,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08,
	0xe5, 0x95, 0x86, 0xe5, 0x93, 0x81, 0x49, 0x44, 0x52, 0x07, 0x67, 0x6f, 0x6f, 0x64, 0x73, 0x49,
	0x64, 0x12, 0x26, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0x49,
	0x44, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x30, 0x0a, 0x0a, 0x67, 0x6f, 0x6f,
	0x64, 0x73, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92,
	0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0x95, 0x86, 0xe5, 0x93, 0x81, 0xe5, 0x90, 0x8d, 0xe7, 0xa7, 0xb0,
	0x52, 0x09, 0x67, 0x6f, 0x6f, 0x64, 0x73, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x28, 0x0a, 0x06, 0x73,
	0x6b, 0x75, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e,
	0x2a, 0x0c, 0xe5, 0x95, 0x86, 0xe5, 0x93, 0x81, 0x53, 0x4b, 0x55, 0x20, 0x49, 0x44, 0x52, 0x05,
	0x73, 0x6b, 0x75, 0x49, 0x64, 0x12, 0x31, 0x0a, 0x09, 0x73, 0x70, 0x65, 0x63, 0x5f, 0x64, 0x65,
	0x73, 0x63, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x14, 0x92, 0x41, 0x11, 0x2a, 0x0f, 0xe5,
	0x95, 0x86, 0xe5, 0x93, 0x81, 0x53, 0x4b, 0x55, 0xe6, 0x8f, 0x8f, 0xe8, 0xbf, 0xb0, 0x52, 0x08,
	0x73, 0x70, 0x65, 0x63, 0x44, 0x65, 0x73, 0x63, 0x12, 0x27, 0x0a, 0x05, 0x70, 0x72, 0x69, 0x63,
	0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x02, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0x95,
	0x86, 0xe5, 0x93, 0x81, 0xe4, 0xbb, 0xb7, 0xe6, 0xa0, 0xbc, 0x52, 0x05, 0x70, 0x72, 0x69, 0x63,
	0x65, 0x12, 0x2d, 0x0a, 0x08, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x05, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0x95, 0x86, 0xe5, 0x93, 0x81,
	0xe6, 0x95, 0xb0, 0xe9, 0x87, 0x8f, 0x52, 0x08, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79,
	0x12, 0x47, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f,
	0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x42, 0x11, 0x92,
	0x41, 0x0e, 0x2a, 0x0c, 0xe6, 0x94, 0xb6, 0xe8, 0xb4, 0xa7, 0xe5, 0x9c, 0xb0, 0xe5, 0x9d, 0x80,
	0x52, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x34, 0x0a, 0x0c, 0x74, 0x6f, 0x74,
	0x61, 0x6c, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x02, 0x42,
	0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0x95, 0x86, 0xe5, 0x93, 0x81, 0xe6, 0x80, 0xbb, 0xe4,
	0xbb, 0xb7, 0x52, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12,
	0x36, 0x0a, 0x0d, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x18, 0x14, 0x20, 0x01, 0x28, 0x02, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe6, 0x94, 0xaf,
	0xe4, 0xbb, 0x98, 0xe6, 0x80, 0xbb, 0xe9, 0xa2, 0x9d, 0x52, 0x0c, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x2e, 0x0a, 0x0c, 0x73, 0x68, 0x69, 0x70, 0x70,
	0x69, 0x6e, 0x67, 0x5f, 0x66, 0x65, 0x65, 0x18, 0x15, 0x20, 0x01, 0x28, 0x02, 0x42, 0x0b, 0x92,
	0x41, 0x08, 0x2a, 0x06, 0xe8, 0xbf, 0x90, 0xe8, 0xb4, 0xb9, 0x52, 0x0b, 0x73, 0x68, 0x69, 0x70,
	0x70, 0x69, 0x6e, 0x67, 0x46, 0x65, 0x65, 0x12, 0xcc, 0x01, 0x0a, 0x0e, 0x70, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x18, 0x16, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72,
	0x65, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68,
	0x6f, 0x64, 0x42, 0x82, 0x01, 0x92, 0x41, 0x7f, 0x2a, 0x7d, 0xe6, 0x94, 0xaf, 0xe4, 0xbb, 0x98,
	0xe6, 0x96, 0xb9, 0xe5, 0xbc, 0x8f, 0x3a, 0x30, 0xef, 0xbc, 0x9a, 0xe9, 0x87, 0x91, 0xe5, 0xb8,
	0x81, 0xef, 0xbc, 0x9b, 0x31, 0xef, 0xbc, 0x9a, 0xe7, 0xa4, 0xbc, 0xe5, 0x93, 0x81, 0xe5, 0x8d,
	0xa1, 0xef, 0xbc, 0x9b, 0x32, 0xef, 0xbc, 0x9a, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0xe4, 0xbb,
	0xbb, 0xe5, 0x8a, 0xa1, 0xef, 0xbc, 0x9b, 0x33, 0xef, 0xbc, 0x9a, 0xe6, 0x94, 0xaf, 0xe4, 0xbb,
	0x98, 0xe5, 0xae, 0x9d, 0xef, 0xbc, 0x9b, 0x34, 0xef, 0xbc, 0x9a, 0xe5, 0xbe, 0xae, 0xe4, 0xbf,
	0xa1, 0xef, 0xbc, 0x9b, 0x35, 0xef, 0xbc, 0x9a, 0xe8, 0x8b, 0xb9, 0xe6, 0x9e, 0x9c, 0xe5, 0x86,
	0x85, 0xe8, 0xb4, 0xad, 0xef, 0xbc, 0x9b, 0x36, 0xef, 0xbc, 0x9a, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0xe5, 0x86, 0x85, 0xe8, 0xb4, 0xad, 0x52, 0x0d, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x12, 0xaa, 0x01, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x17, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f,
	0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65,
	0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x72, 0x92, 0x41, 0x6f, 0x2a, 0x6d, 0xe8, 0xae,
	0xa2, 0xe5, 0x8d, 0x95, 0xe7, 0x8a, 0xb6, 0xe6, 0x80, 0x81, 0x3a, 0x30, 0xef, 0xbc, 0x9a, 0xe6,
	0x9c, 0xaa, 0xe7, 0x9f, 0xa5, 0xe7, 0x8a, 0xb6, 0xe6, 0x80, 0x81, 0xef, 0xbc, 0x9b, 0x31, 0xef,
	0xbc, 0x9a, 0xe5, 0xbe, 0x85, 0xe6, 0x94, 0xaf, 0xe4, 0xbb, 0x98, 0xef, 0xbc, 0x9b, 0x32, 0xef,
	0xbc, 0x9a, 0xe5, 0xb7, 0xb2, 0xe6, 0x94, 0xaf, 0xe4, 0xbb, 0x98, 0xef, 0xbc, 0x9b, 0x33, 0xef,
	0xbc, 0x9a, 0xe5, 0xb7, 0xb2, 0xe5, 0x8f, 0x96, 0xe6, 0xb6, 0x88, 0xef, 0xbc, 0x9b, 0x34, 0xef,
	0xbc, 0x9a, 0xe5, 0xbe, 0x85, 0xe6, 0x94, 0xb6, 0xe8, 0xb4, 0xa7, 0xef, 0xbc, 0x9b, 0x35, 0xef,
	0xbc, 0x9a, 0xe5, 0xb7, 0xb2, 0xe5, 0xae, 0x8c, 0xe6, 0x88, 0x90, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x30, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61,
	0x74, 0x18, 0x18, 0x20, 0x01, 0x28, 0x03, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0x88,
	0x9b, 0xe5, 0xbb, 0xba, 0xe6, 0x97, 0xb6, 0xe9, 0x97, 0xb4, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x27, 0x0a, 0x07, 0x77, 0x69, 0x6b, 0x69, 0x5f, 0x6e, 0x6f,
	0x18, 0x19, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0e, 0x92, 0x41, 0x0b, 0x2a, 0x09, 0xe5, 0xa4, 0xa9,
	0xe7, 0x9c, 0xbc, 0xe5, 0x8f, 0xb7, 0x52, 0x06, 0x77, 0x69, 0x6b, 0x69, 0x4e, 0x6f, 0x12, 0x2a,
	0x0a, 0x07, 0x70, 0x61, 0x69, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe6, 0x94, 0xaf, 0xe4, 0xbb, 0x98, 0xe6, 0x97, 0xb6, 0xe9,
	0x97, 0xb4, 0x52, 0x06, 0x70, 0x61, 0x69, 0x64, 0x41, 0x74, 0x12, 0x3a, 0x0a, 0x0c, 0x63, 0x61,
	0x72, 0x72, 0x69, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe7, 0x89, 0xa9, 0xe6, 0xb5, 0x81, 0xe5, 0x85, 0xac,
	0xe5, 0x8f, 0xb8, 0xe5, 0x90, 0x8d, 0xe7, 0xa7, 0xb0, 0x52, 0x0b, 0x63, 0x61, 0x72, 0x72, 0x69,
	0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x3a, 0x0a, 0x0c, 0x63, 0x61, 0x72, 0x72, 0x69, 0x65,
	0x72, 0x5f, 0x69, 0x63, 0x6f, 0x6e, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x09, 0x42, 0x17, 0x92, 0x41,
	0x14, 0x2a, 0x12, 0xe7, 0x89, 0xa9, 0xe6, 0xb5, 0x81, 0xe5, 0x85, 0xac, 0xe5, 0x8f, 0xb8, 0xe5,
	0x9b, 0xbe, 0xe6, 0xa0, 0x87, 0x52, 0x0b, 0x63, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x49, 0x63,
	0x6f, 0x6e, 0x12, 0x32, 0x0a, 0x0b, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x6e,
	0x6f, 0x18, 0x1d, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe7, 0x89,
	0xa9, 0xe6, 0xb5, 0x81, 0xe5, 0x8d, 0x95, 0xe5, 0x8f, 0xb7, 0x52, 0x0a, 0x74, 0x72, 0x61, 0x63,
	0x6b, 0x69, 0x6e, 0x67, 0x4e, 0x6f, 0x12, 0x48, 0x0a, 0x05, 0x73, 0x74, 0x65, 0x70, 0x73, 0x18,
	0x1e, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64,
	0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x67, 0x69, 0x73, 0x74,
	0x69, 0x63, 0x53, 0x74, 0x65, 0x70, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe7, 0x89, 0xa9,
	0xe6, 0xb5, 0x81, 0xe8, 0x8a, 0x82, 0xe7, 0x82, 0xb9, 0x52, 0x05, 0x73, 0x74, 0x65, 0x70, 0x73,
	0x22, 0xc1, 0x02, 0x0a, 0x05, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x12, 0x1d, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe6, 0xa0, 0x87,
	0xe7, 0xad, 0xbe, 0x49, 0x44, 0x52, 0x02, 0x69, 0x64, 0x12, 0x25, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe6, 0xa0,
	0x87, 0xe7, 0xad, 0xbe, 0xe5, 0x90, 0x8d, 0xe7, 0xa7, 0xb0, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x69, 0x0a, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74,
	0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x43, 0x61, 0x74, 0x65,
	0x67, 0x6f, 0x72, 0x79, 0x42, 0x2b, 0x92, 0x41, 0x28, 0x2a, 0x26, 0xe6, 0xa0, 0x87, 0xe7, 0xad,
	0xbe, 0xe7, 0xb1, 0xbb, 0xe5, 0x88, 0xab, 0xef, 0xbc, 0x9a, 0x30, 0xef, 0xbc, 0x9a, 0xe5, 0x9b,
	0xbe, 0xe7, 0x89, 0x87, 0xef, 0xbc, 0x9b, 0x31, 0xef, 0xbc, 0x9a, 0xe6, 0x96, 0x87, 0xe6, 0x9c,
	0xac, 0x52, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x4c, 0x0a, 0x06, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e,
	0x4c, 0x61, 0x62, 0x65, 0x6c, 0x2e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x42, 0x0e, 0x92, 0x41, 0x0b, 0x2a, 0x09, 0xe5, 0xa4, 0x9a, 0xe8, 0xaf, 0xad, 0xe8, 0xa8,
	0x80, 0x52, 0x06, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x1a, 0x39, 0x0a, 0x0b, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x3a, 0x02, 0x38, 0x01, 0x22, 0x40, 0x0a, 0x0e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x2e, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64,
	0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x52,
	0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x22, 0x8e, 0x01, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x53, 0x74,
	0x61, 0x74, 0x69, 0x63, 0x53, 0x70, 0x65, 0x63, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x76, 0x0a, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f,
	0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x6f, 0x6f, 0x64, 0x73, 0x43, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x79, 0x42, 0x38, 0x92, 0x41, 0x35, 0x2a, 0x33, 0xe5, 0x95, 0x86, 0xe5, 0x93, 0x81,
	0xe5, 0x88, 0x86, 0xe7, 0xb1, 0xbb, 0xef, 0xbc, 0x9a, 0x30, 0xef, 0xbc, 0x9a, 0xe5, 0xae, 0x9e,
	0xe7, 0x89, 0xa9, 0xe5, 0x95, 0x86, 0xe5, 0x93, 0x81, 0xef, 0xbc, 0x9b, 0x31, 0xef, 0xbc, 0x9a,
	0x56, 0x49, 0x50, 0xef, 0xbc, 0x9b, 0x32, 0xef, 0xbc, 0x9a, 0x56, 0x50, 0x53, 0x52, 0x08, 0x63,
	0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x22, 0xc5, 0x01, 0x0a, 0x13, 0x47, 0x6f, 0x6f, 0x64,
	0x73, 0x53, 0x74, 0x61, 0x74, 0x69, 0x63, 0x53, 0x70, 0x65, 0x63, 0x55, 0x6e, 0x69, 0x74, 0x12,
	0x23, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x13, 0x92, 0x41, 0x10,
	0x2a, 0x0e, 0xe8, 0xa7, 0x84, 0xe6, 0xa0, 0xbc, 0xe5, 0x8d, 0x95, 0xe4, 0xbd, 0x8d, 0x49, 0x44,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x2b, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe8, 0xa7, 0x84, 0xe6, 0xa0, 0xbc, 0xe5,
	0x8d, 0x95, 0xe4, 0xbd, 0x8d, 0xe5, 0x90, 0x8d, 0xe7, 0xa7, 0xb0, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x3a, 0x0a, 0x09, 0x73, 0x68, 0x6f, 0x77, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x1d, 0x92, 0x41, 0x1a, 0x2a, 0x18, 0xe8, 0xa7, 0x84, 0xe6, 0xa0,
	0xbc, 0xe5, 0x8d, 0x95, 0xe4, 0xbd, 0x8d, 0xe5, 0xb1, 0x95, 0xe7, 0xa4, 0xba, 0xe5, 0x90, 0x8d,
	0xe7, 0xa7, 0xb0, 0x52, 0x08, 0x73, 0x68, 0x6f, 0x77, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a,
	0x03, 0x6b, 0x65, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0e, 0x92, 0x41, 0x0b, 0x2a,
	0x09, 0xe8, 0xa7, 0x84, 0xe6, 0xa0, 0xbc, 0xe5, 0x80, 0xbc, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x22,
	0x86, 0x02, 0x0a, 0x14, 0x47, 0x6f, 0x6f, 0x64, 0x73, 0x53, 0x74, 0x61, 0x74, 0x69, 0x63, 0x53,
	0x70, 0x65, 0x63, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x20, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x10, 0x92, 0x41, 0x0d, 0x2a, 0x0b, 0xe8, 0xa7, 0x84, 0xe6, 0xa0,
	0xbc, 0xe5, 0x80, 0xbc, 0x49, 0x44, 0x52, 0x02, 0x69, 0x64, 0x12, 0x28, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x14, 0x92, 0x41, 0x11, 0x2a, 0x0f, 0xe8,
	0xa7, 0x84, 0xe6, 0xa0, 0xbc, 0xe5, 0x80, 0xbc, 0xe5, 0x90, 0x8d, 0xe7, 0xa7, 0xb0, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x64, 0x0a, 0x09, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74,
	0x65, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f,
	0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x6f, 0x6f, 0x64,
	0x73, 0x53, 0x74, 0x61, 0x74, 0x69, 0x63, 0x53, 0x70, 0x65, 0x63, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x42,
	0x0e, 0x92, 0x41, 0x0b, 0x2a, 0x09, 0xe5, 0xa4, 0x9a, 0xe8, 0xaf, 0xad, 0xe8, 0xa8, 0x80, 0x52,
	0x09, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74, 0x65, 0x1a, 0x3c, 0x0a, 0x0e, 0x54, 0x72,
	0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03,
	0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xde, 0x03, 0x0a, 0x0f, 0x47, 0x6f, 0x6f,
	0x64, 0x73, 0x53, 0x74, 0x61, 0x74, 0x69, 0x63, 0x53, 0x70, 0x65, 0x63, 0x12, 0x23, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x13, 0x92, 0x41, 0x10, 0x2a, 0x0e, 0xe5,
	0x95, 0x86, 0xe5, 0x93, 0x81, 0xe8, 0xa7, 0x84, 0xe6, 0xa0, 0xbc, 0x49, 0x44, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x2b, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe5, 0x95, 0x86, 0xe5, 0x93, 0x81, 0xe8, 0xa7, 0x84, 0xe6,
	0xa0, 0xbc, 0xe5, 0x90, 0x8d, 0xe7, 0xa7, 0xb0, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x5f,
	0x0a, 0x09, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x31, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f,
	0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x6f, 0x6f, 0x64, 0x73, 0x53, 0x74, 0x61, 0x74, 0x69,
	0x63, 0x53, 0x70, 0x65, 0x63, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74, 0x65, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x42, 0x0e, 0x92, 0x41, 0x0b, 0x2a, 0x09, 0xe5, 0xa4, 0x9a, 0xe8, 0xaf,
	0xad, 0xe8, 0xa8, 0x80, 0x52, 0x09, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74, 0x65, 0x12,
	0x55, 0x0a, 0x05, 0x75, 0x6e, 0x69, 0x74, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e,
	0x76, 0x31, 0x2e, 0x47, 0x6f, 0x6f, 0x64, 0x73, 0x53, 0x74, 0x61, 0x74, 0x69, 0x63, 0x53, 0x70,
	0x65, 0x63, 0x55, 0x6e, 0x69, 0x74, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe5, 0x95, 0x86,
	0xe5, 0x93, 0x81, 0xe8, 0xa7, 0x84, 0xe6, 0xa0, 0xbc, 0xe5, 0x8d, 0x95, 0xe4, 0xbd, 0x8d, 0x52,
	0x05, 0x75, 0x6e, 0x69, 0x74, 0x73, 0x12, 0x5b, 0x0a, 0x06, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73,
	0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c,
	0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x6f, 0x6f, 0x64, 0x73,
	0x53, 0x74, 0x61, 0x74, 0x69, 0x63, 0x53, 0x70, 0x65, 0x63, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x42,
	0x1a, 0x92, 0x41, 0x17, 0x2a, 0x15, 0xe5, 0x95, 0x86, 0xe5, 0x93, 0x81, 0xe5, 0x8f, 0xaf, 0xe9,
	0x80, 0x89, 0xe8, 0xa7, 0x84, 0xe6, 0xa0, 0xbc, 0xe5, 0x80, 0xbc, 0x52, 0x06, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x73, 0x12, 0x26, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x14, 0x92, 0x41, 0x11, 0x2a, 0x0f, 0xe5, 0x95, 0x86, 0xe5, 0x93, 0x81, 0xe8, 0xa7, 0x84,
	0xe6, 0xa0, 0xbc, 0xe9, 0x94, 0xae, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x1a, 0x3c, 0x0a, 0x0e, 0x54,
	0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a,
	0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12,
	0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xe0, 0x01, 0x0a, 0x19, 0x47, 0x6f,
	0x6f, 0x64, 0x73, 0x53, 0x74, 0x61, 0x74, 0x69, 0x63, 0x53, 0x70, 0x65, 0x63, 0x4f, 0x66, 0x43,
	0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x76, 0x0a, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x6f,
	0x6f, 0x64, 0x73, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x42, 0x38, 0x92, 0x41, 0x35,
	0x2a, 0x33, 0xe5, 0x95, 0x86, 0xe5, 0x93, 0x81, 0xe5, 0x88, 0x86, 0xe7, 0xb1, 0xbb, 0xef, 0xbc,
	0x9a, 0x30, 0xef, 0xbc, 0x9a, 0xe5, 0xae, 0x9e, 0xe7, 0x89, 0xa9, 0xe5, 0x95, 0x86, 0xe5, 0x93,
	0x81, 0xef, 0xbc, 0x9b, 0x31, 0xef, 0xbc, 0x9a, 0x56, 0x49, 0x50, 0xef, 0xbc, 0x9b, 0x32, 0xef,
	0xbc, 0x9a, 0x56, 0x50, 0x53, 0x52, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12,
	0x4b, 0x0a, 0x05, 0x73, 0x70, 0x65, 0x63, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x22,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e,
	0x76, 0x31, 0x2e, 0x47, 0x6f, 0x6f, 0x64, 0x73, 0x53, 0x74, 0x61, 0x74, 0x69, 0x63, 0x53, 0x70,
	0x65, 0x63, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe8, 0xa7, 0x84, 0xe6, 0xa0, 0xbc, 0xe5,
	0x88, 0x97, 0xe8, 0xa1, 0xa8, 0x52, 0x05, 0x73, 0x70, 0x65, 0x63, 0x73, 0x22, 0x7b, 0x0a, 0x12,
	0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x74, 0x69, 0x63, 0x53, 0x70, 0x65, 0x63, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x12, 0x65, 0x0a, 0x0a, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c,
	0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x6f, 0x6f, 0x64, 0x73,
	0x53, 0x74, 0x61, 0x74, 0x69, 0x63, 0x53, 0x70, 0x65, 0x63, 0x4f, 0x66, 0x43, 0x61, 0x74, 0x65,
	0x67, 0x6f, 0x72, 0x79, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe5, 0x95, 0x86, 0xe5, 0x93,
	0x81, 0xe8, 0xa7, 0x84, 0xe6, 0xa0, 0xbc, 0xe5, 0x88, 0x97, 0xe8, 0xa1, 0xa8, 0x52, 0x0a, 0x63,
	0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x22, 0xd1, 0x01, 0x0a, 0x12, 0x47, 0x6f,
	0x6f, 0x64, 0x73, 0x53, 0x70, 0x65, 0x63, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74, 0x65,
	0x12, 0x25, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11,
	0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe8, 0xa7, 0x84, 0xe6, 0xa0, 0xbc, 0xe5, 0x90, 0x8d, 0xe7, 0xa7,
	0xb0, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x59, 0x0a, 0x06, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f,
	0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x6f, 0x6f, 0x64,
	0x73, 0x53, 0x70, 0x65, 0x63, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74, 0x65, 0x2e, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x42, 0x0e, 0x92, 0x41, 0x0b, 0x2a,
	0x09, 0xe8, 0xa7, 0x84, 0xe6, 0xa0, 0xbc, 0xe5, 0x80, 0xbc, 0x52, 0x06, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x73, 0x1a, 0x39, 0x0a, 0x0b, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xb9, 0x02,
	0x0a, 0x0e, 0x47, 0x6f, 0x6f, 0x64, 0x73, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74, 0x65,
	0x12, 0x25, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11,
	0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0x95, 0x86, 0xe5, 0x93, 0x81, 0xe5, 0x90, 0x8d, 0xe7, 0xa7,
	0xb0, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x48, 0x0a, 0x07, 0x64, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67,
	0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6d, 0x61,
	0x67, 0x65, 0x42, 0x14, 0x92, 0x41, 0x11, 0x2a, 0x0f, 0xe5, 0x95, 0x86, 0xe5, 0x93, 0x81, 0xe8,
	0xaf, 0xa6, 0xe6, 0x83, 0x85, 0xe5, 0x9b, 0xbe, 0x52, 0x07, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x12, 0x55, 0x0a, 0x05, 0x73, 0x70, 0x65, 0x63, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x2c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72,
	0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x6f, 0x6f, 0x64, 0x73, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x6c,
	0x61, 0x74, 0x65, 0x2e, 0x53, 0x70, 0x65, 0x63, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x42, 0x11,
	0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0x95, 0x86, 0xe5, 0x93, 0x81, 0xe8, 0xa7, 0x84, 0xe6, 0xa0,
	0xbc, 0x52, 0x05, 0x73, 0x70, 0x65, 0x63, 0x73, 0x1a, 0x5f, 0x0a, 0x0a, 0x53, 0x70, 0x65, 0x63,
	0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x3b, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f,
	0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x6f, 0x6f, 0x64,
	0x73, 0x53, 0x70, 0x65, 0x63, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xd7, 0x09, 0x0a, 0x09, 0x47, 0x6f,
	0x6f, 0x64, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x27, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe6, 0x96, 0xb0, 0xe5, 0xa2, 0x9e,
	0xe6, 0x97, 0xb6, 0xe4, 0xb8, 0x8d, 0xe7, 0x94, 0xa8, 0xe7, 0xae, 0xa1, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x76, 0x0a, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74,
	0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x6f, 0x6f, 0x64, 0x73, 0x43, 0x61, 0x74, 0x65,
	0x67, 0x6f, 0x72, 0x79, 0x42, 0x38, 0x92, 0x41, 0x35, 0x2a, 0x33, 0xe5, 0x95, 0x86, 0xe5, 0x93,
	0x81, 0xe7, 0xb1, 0xbb, 0xe5, 0x9e, 0x8b, 0xef, 0xbc, 0x9a, 0x30, 0xef, 0xbc, 0x9a, 0xe5, 0xae,
	0x9e, 0xe7, 0x89, 0xa9, 0xe5, 0x95, 0x86, 0xe5, 0x93, 0x81, 0xef, 0xbc, 0x9b, 0x31, 0xef, 0xbc,
	0x9a, 0x56, 0x49, 0x50, 0xef, 0xbc, 0x9b, 0x32, 0xef, 0xbc, 0x9a, 0x56, 0x50, 0x53, 0x52, 0x08,
	0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x25, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0x95, 0x86,
	0xe5, 0x93, 0x81, 0xe5, 0x90, 0x8d, 0xe7, 0xa7, 0xb0, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x27, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11,
	0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0x95, 0x86, 0xe5, 0x93, 0x81, 0xe6, 0x8f, 0x8f, 0xe8, 0xbf,
	0xb0, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x28, 0x0a, 0x08, 0x6c, 0x61, 0x62, 0x65,
	0x6c, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x03, 0x28, 0x09, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a,
	0x08, 0xe6, 0xa0, 0x87, 0xe7, 0xad, 0xbe, 0x49, 0x44, 0x52, 0x07, 0x6c, 0x61, 0x62, 0x65, 0x6c,
	0x49, 0x64, 0x12, 0x41, 0x0a, 0x05, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f,
	0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x42, 0x11, 0x92, 0x41, 0x0e,
	0x2a, 0x0c, 0xe5, 0x95, 0x86, 0xe5, 0x93, 0x81, 0xe4, 0xb8, 0xbb, 0xe5, 0x9b, 0xbe, 0x52, 0x05,
	0x69, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x63, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64,
	0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x6f, 0x6f, 0x64, 0x73, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x2b, 0x92, 0x41, 0x28, 0x2a, 0x26, 0xe5, 0x95, 0x86, 0xe5,
	0x93, 0x81, 0xe7, 0x8a, 0xb6, 0xe6, 0x80, 0x81, 0xef, 0xbc, 0x9a, 0x30, 0xef, 0xbc, 0x9a, 0xe4,
	0xb8, 0x8b, 0xe6, 0x9e, 0xb6, 0xef, 0xbc, 0x9b, 0x31, 0xef, 0xbc, 0x9a, 0xe4, 0xb8, 0x8a, 0xe6,
	0x9e, 0xb6, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x39, 0x0a, 0x0d, 0x66, 0x72,
	0x65, 0x65, 0x5f, 0x73, 0x68, 0x69, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x08, 0x42, 0x14, 0x92, 0x41, 0x11, 0x2a, 0x0f, 0xe6, 0x98, 0xaf, 0xe5, 0x90, 0xa6, 0xe5, 0x85,
	0x8d, 0xe8, 0xbf, 0x90, 0xe8, 0xb4, 0xb9, 0x52, 0x0c, 0x66, 0x72, 0x65, 0x65, 0x53, 0x68, 0x69,
	0x70, 0x70, 0x69, 0x6e, 0x67, 0x12, 0x36, 0x0a, 0x0a, 0x62, 0x61, 0x73, 0x65, 0x5f, 0x70, 0x72,
	0x69, 0x63, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x02, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12,
	0xe5, 0x95, 0x86, 0xe5, 0x93, 0x81, 0xe5, 0x9f, 0xba, 0xe7, 0xa1, 0x80, 0xe4, 0xbb, 0xb7, 0xe6,
	0xa0, 0xbc, 0x52, 0x09, 0x62, 0x61, 0x73, 0x65, 0x50, 0x72, 0x69, 0x63, 0x65, 0x12, 0x43, 0x0a,
	0x0e, 0x75, 0x73, 0x65, 0x5f, 0x62, 0x61, 0x73, 0x65, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x08, 0x42, 0x1d, 0x92, 0x41, 0x1a, 0x2a, 0x18, 0xe6, 0x98, 0xaf, 0xe5,
	0x90, 0xa6, 0xe4, 0xbd, 0xbf, 0xe7, 0x94, 0xa8, 0xe5, 0x9f, 0xba, 0xe7, 0xa1, 0x80, 0xe4, 0xbb,
	0xb7, 0xe6, 0xa0, 0xbc, 0x52, 0x0c, 0x75, 0x73, 0x65, 0x42, 0x61, 0x73, 0x65, 0x50, 0x72, 0x69,
	0x63, 0x65, 0x12, 0x3e, 0x0a, 0x0e, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x70,
	0x72, 0x69, 0x63, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x02, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a,
	0x12, 0xe9, 0x80, 0x89, 0xe4, 0xb8, 0xad, 0xe7, 0x9a, 0x84, 0x73, 0x6b, 0x75, 0xe4, 0xbb, 0xb7,
	0xe6, 0xa0, 0xbc, 0x52, 0x0d, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x50, 0x72, 0x69,
	0x63, 0x65, 0x12, 0x4c, 0x0a, 0x09, 0x63, 0x61, 0x72, 0x6f, 0x75, 0x73, 0x65, 0x6c, 0x73, 0x18,
	0x10, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64,
	0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x42,
	0x14, 0x92, 0x41, 0x11, 0x2a, 0x0f, 0xe5, 0x95, 0x86, 0xe5, 0x93, 0x81, 0xe8, 0xbd, 0xae, 0xe6,
	0x92, 0xad, 0xe5, 0x9b, 0xbe, 0x52, 0x09, 0x63, 0x61, 0x72, 0x6f, 0x75, 0x73, 0x65, 0x6c, 0x73,
	0x12, 0x48, 0x0a, 0x07, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x11, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f,
	0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x42, 0x14, 0x92, 0x41, 0x11,
	0x2a, 0x0f, 0xe5, 0x95, 0x86, 0xe5, 0x93, 0x81, 0xe8, 0xaf, 0xa6, 0xe6, 0x83, 0x85, 0xe5, 0x9b,
	0xbe, 0x52, 0x07, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x45, 0x0a, 0x05, 0x73, 0x70,
	0x65, 0x63, 0x73, 0x18, 0x12, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x6f,
	0x6f, 0x64, 0x73, 0x53, 0x70, 0x65, 0x63, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0x95,
	0x86, 0xe5, 0x93, 0x81, 0xe8, 0xa7, 0x84, 0xe6, 0xa0, 0xbc, 0x52, 0x05, 0x73, 0x70, 0x65, 0x63,
	0x73, 0x12, 0x3f, 0x0a, 0x04, 0x73, 0x6b, 0x75, 0x73, 0x18, 0x13, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x1b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65,
	0x2e, 0x76, 0x31, 0x2e, 0x47, 0x6f, 0x6f, 0x64, 0x73, 0x53, 0x6b, 0x75, 0x42, 0x0e, 0x92, 0x41,
	0x0b, 0x2a, 0x09, 0xe5, 0x95, 0x86, 0xe5, 0x93, 0x81, 0x73, 0x6b, 0x75, 0x52, 0x04, 0x73, 0x6b,
	0x75, 0x73, 0x12, 0x59, 0x0a, 0x09, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74, 0x65, 0x18,
	0x14, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64,
	0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x6f, 0x6f, 0x64, 0x73, 0x49,
	0x6e, 0x66, 0x6f, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74, 0x65, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x42, 0x0e, 0x92, 0x41, 0x0b, 0x2a, 0x09, 0xe5, 0xa4, 0x9a, 0xe8, 0xaf, 0xad, 0xe8,
	0xa8, 0x80, 0x52, 0x09, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74, 0x65, 0x12, 0x33, 0x0a,
	0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x15, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0x95, 0x86, 0xe5, 0x93, 0x81, 0xe6,
	0x8f, 0x8f, 0xe8, 0xbf, 0xb0, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x1a, 0x5f, 0x0a, 0x0e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74, 0x65, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x37, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64,
	0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x6f, 0x6f, 0x64, 0x73, 0x54,
	0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a,
	0x02, 0x38, 0x01, 0x22, 0x1f, 0x0a, 0x0d, 0x41, 0x64, 0x64, 0x47, 0x6f, 0x6f, 0x64, 0x73, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x02, 0x69, 0x64, 0x22, 0x33, 0x0a, 0x12, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x47, 0x6f,
	0x6f, 0x64, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe5, 0x95, 0x86,
	0xe5, 0x93, 0x81, 0x49, 0x44, 0x52, 0x02, 0x69, 0x64, 0x22, 0x34, 0x0a, 0x13, 0x47, 0x65, 0x74,
	0x47, 0x6f, 0x6f, 0x64, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x1d, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d, 0x92, 0x41,
	0x0a, 0x2a, 0x08, 0xe5, 0x95, 0x86, 0xe5, 0x93, 0x81, 0x49, 0x44, 0x52, 0x02, 0x69, 0x64, 0x22,
	0xd0, 0x01, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x47, 0x6f, 0x6f, 0x64, 0x73, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1f, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe9, 0xa1, 0xb5, 0xe7,
	0xa0, 0x81, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x25, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe6, 0xaf, 0x8f,
	0xe9, 0xa1, 0xb5, 0xe6, 0x95, 0xb0, 0xe9, 0x87, 0x8f, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12,
	0x2e, 0x0a, 0x07, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x14, 0x92, 0x41, 0x11, 0x2a, 0x0f, 0xe6, 0x90, 0x9c, 0xe7, 0xb4, 0xa2, 0xe5, 0x85, 0xb3,
	0xe9, 0x94, 0xae, 0xe5, 0xad, 0x97, 0x52, 0x07, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x12,
	0x41, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x42,
	0x29, 0x92, 0x41, 0x26, 0x2a, 0x24, 0xe5, 0x95, 0x86, 0xe5, 0x93, 0x81, 0xe7, 0x8a, 0xb6, 0xe6,
	0x80, 0x81, 0x3a, 0x30, 0xef, 0xbc, 0x9a, 0xe4, 0xb8, 0x8b, 0xe6, 0x9e, 0xb6, 0xef, 0xbc, 0x9b,
	0x31, 0xef, 0xbc, 0x9a, 0xe4, 0xb8, 0x8a, 0xe6, 0x9e, 0xb6, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x22, 0x80, 0x01, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x47, 0x6f, 0x6f, 0x64, 0x73, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x24, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61,
	0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x42, 0x0e, 0x92, 0x41, 0x0b, 0x2a, 0x09, 0xe6, 0x80,
	0xbb, 0xe6, 0x95, 0xb0, 0xe9, 0x87, 0x8f, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x45,
	0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76,
	0x31, 0x2e, 0x47, 0x6f, 0x6f, 0x64, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x11, 0x92, 0x41, 0x0e,
	0x2a, 0x0c, 0xe5, 0x95, 0x86, 0xe5, 0x93, 0x81, 0xe5, 0x88, 0x97, 0xe8, 0xa1, 0xa8, 0x52, 0x05,
	0x69, 0x74, 0x65, 0x6d, 0x73, 0x22, 0x98, 0x01, 0x0a, 0x12, 0x47, 0x6f, 0x6f, 0x64, 0x73, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe5,
	0x95, 0x86, 0xe5, 0x93, 0x81, 0x49, 0x44, 0x52, 0x02, 0x69, 0x64, 0x12, 0x63, 0x0a, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1e, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e,
	0x47, 0x6f, 0x6f, 0x64, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x2b, 0x92, 0x41, 0x28,
	0x2a, 0x26, 0xe5, 0x95, 0x86, 0xe5, 0x93, 0x81, 0xe7, 0x8a, 0xb6, 0xe6, 0x80, 0x81, 0xef, 0xbc,
	0x9a, 0x30, 0xef, 0xbc, 0x9a, 0xe4, 0xb8, 0x8b, 0xe6, 0x9e, 0xb6, 0xef, 0xbc, 0x9b, 0x31, 0xef,
	0xbc, 0x9a, 0xe4, 0xb8, 0x8a, 0xe6, 0x9e, 0xb6, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x22, 0x68, 0x0a, 0x1e, 0x47, 0x69, 0x66, 0x74, 0x43, 0x61, 0x72, 0x64, 0x53, 0x65, 0x74, 0x74,
	0x69, 0x6e, 0x67, 0x50, 0x61, 0x67, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x1f, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe9, 0xa1, 0xb5, 0xe7, 0xa0, 0x81, 0x52, 0x04, 0x70,
	0x61, 0x67, 0x65, 0x12, 0x25, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x05, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe6, 0xaf, 0x8f, 0xe9, 0xa1, 0xb5, 0xe6, 0x95,
	0xb0, 0xe9, 0x87, 0x8f, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x22, 0x99, 0x01, 0x0a, 0x1c, 0x47,
	0x69, 0x66, 0x74, 0x43, 0x61, 0x72, 0x64, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x50, 0x61,
	0x67, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x56, 0x0a, 0x05, 0x69,
	0x74, 0x65, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47,
	0x69, 0x66, 0x74, 0x43, 0x61, 0x72, 0x64, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x50, 0x61,
	0x67, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x49, 0x74, 0x65, 0x6d, 0x42,
	0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe5, 0x88, 0x97, 0xe8, 0xa1, 0xa8, 0x52, 0x05, 0x69, 0x74,
	0x65, 0x6d, 0x73, 0x12, 0x21, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x05, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe6, 0x80, 0xbb, 0xe6, 0x95, 0xb0, 0x52,
	0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x22, 0xf8, 0x04, 0x0a, 0x20, 0x47, 0x69, 0x66, 0x74, 0x43,
	0x61, 0x72, 0x64, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x67, 0x65, 0x4c, 0x69,
	0x73, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x17, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x42, 0x07, 0x92, 0x41, 0x04, 0x2a, 0x02, 0x49, 0x44,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x2c, 0x0a, 0x08, 0x67, 0x6f, 0x6f, 0x64, 0x73, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0x95, 0x86,
	0xe5, 0x93, 0x81, 0xe5, 0x90, 0x8d, 0xe7, 0xa7, 0xb0, 0x52, 0x07, 0x67, 0x6f, 0x6f, 0x64, 0x73,
	0x49, 0x64, 0x12, 0x2d, 0x0a, 0x07, 0x69, 0x6e, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x14, 0x92, 0x41, 0x11, 0x2a, 0x0f, 0xe6, 0x9c, 0x89, 0xe6, 0x95, 0x88,
	0xe6, 0x9c, 0x9f, 0xe7, 0xb1, 0xbb, 0xe5, 0x9e, 0x8b, 0x52, 0x06, 0x69, 0x6e, 0x44, 0x61, 0x74,
	0x65, 0x12, 0x36, 0x0a, 0x0c, 0x69, 0x6e, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x64, 0x61, 0x79,
	0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x14, 0x92, 0x41, 0x11, 0x2a, 0x0f, 0xe6, 0x9c,
	0x89, 0xe6, 0x95, 0x88, 0xe6, 0x9c, 0x9f, 0xe5, 0xa4, 0xa9, 0xe6, 0x95, 0xb0, 0x52, 0x0a, 0x69,
	0x6e, 0x44, 0x61, 0x74, 0x65, 0x44, 0x61, 0x79, 0x73, 0x12, 0x39, 0x0a, 0x0a, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1a, 0x92,
	0x41, 0x17, 0x2a, 0x15, 0xe6, 0x9c, 0x89, 0xe6, 0x95, 0x88, 0xe6, 0x9c, 0x9f, 0xe5, 0xbc, 0x80,
	0xe5, 0xa7, 0x8b, 0xe6, 0x97, 0xb6, 0xe9, 0x97, 0xb4, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x35, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1a, 0x92, 0x41, 0x17, 0x2a, 0x15, 0xe6, 0x9c, 0x89,
	0xe6, 0x95, 0x88, 0xe6, 0x9c, 0x9f, 0xe7, 0xbb, 0x93, 0xe6, 0x9d, 0x9f, 0xe6, 0x97, 0xb6, 0xe9,
	0x97, 0xb4, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x2e, 0x0a, 0x09, 0x75,
	0x73, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x42, 0x11,
	0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe4, 0xbd, 0xbf, 0xe7, 0x94, 0xa8, 0xe6, 0xac, 0xa1, 0xe6, 0x95,
	0xb0, 0x52, 0x08, 0x75, 0x73, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x12, 0x33, 0x0a, 0x0a, 0x75,
	0x73, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x42,
	0x14, 0x92, 0x41, 0x11, 0x2a, 0x0f, 0xe5, 0xb7, 0xb2, 0xe4, 0xbd, 0xbf, 0xe7, 0x94, 0xa8, 0xe6,
	0xac, 0xa1, 0xe6, 0x95, 0xb0, 0x52, 0x09, 0x75, 0x73, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x12, 0x3e, 0x0a, 0x11, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x68,
	0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e,
	0x2a, 0x0c, 0xe6, 0x8e, 0xa8, 0xe5, 0xb9, 0xbf, 0xe6, 0xb8, 0xa0, 0xe9, 0x81, 0x93, 0x52, 0x10,
	0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c,
	0x12, 0x2a, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x70, 0x6f, 0x73, 0x65, 0x72, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x0e, 0x92, 0x41, 0x0b, 0x2a, 0x09, 0xe7, 0x94, 0xb3, 0xe8, 0xaf, 0xb7, 0xe4,
	0xba, 0xba, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x70, 0x6f, 0x73, 0x65, 0x72, 0x12, 0x31, 0x0a, 0x0c,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x0e, 0x92, 0x41, 0x0b, 0x2a, 0x09, 0xe5, 0x88, 0x9b, 0xe5, 0xbb, 0xba, 0xe4,
	0xba, 0xba, 0x52, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x55, 0x73, 0x65, 0x72, 0x12,
	0x30, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0c, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0x88, 0x9b, 0xe5, 0xbb, 0xba,
	0xe6, 0x97, 0xb6, 0xe9, 0x97, 0xb4, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41,
	0x74, 0x22, 0x88, 0x05, 0x0a, 0x1a, 0x45, 0x64, 0x69, 0x74, 0x47, 0x69, 0x66, 0x74, 0x43, 0x61,
	0x72, 0x64, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x28, 0x0a, 0x08, 0x67, 0x6f, 0x6f, 0x64, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe5, 0x95, 0x86, 0xe5, 0x93, 0x81, 0x49,
	0x44, 0x52, 0x07, 0x67, 0x6f, 0x6f, 0x64, 0x73, 0x49, 0x64, 0x12, 0x3d, 0x0a, 0x07, 0x69, 0x6e,
	0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x42, 0x24, 0x92, 0x41, 0x21,
	0x2a, 0x1f, 0xe6, 0x9c, 0x89, 0xe6, 0x95, 0x88, 0xe6, 0x9c, 0x9f, 0xe7, 0xb1, 0xbb, 0xe5, 0x9e,
	0x8b, 0x20, 0x30, 0xe5, 0x9b, 0xba, 0xe5, 0xae, 0x9a, 0x20, 0x31, 0xe7, 0x9b, 0xb8, 0xe5, 0xaf,
	0xb9, 0x52, 0x06, 0x69, 0x6e, 0x44, 0x61, 0x74, 0x65, 0x12, 0x46, 0x0a, 0x0c, 0x69, 0x6e, 0x5f,
	0x64, 0x61, 0x74, 0x65, 0x5f, 0x64, 0x61, 0x79, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x42,
	0x24, 0x92, 0x41, 0x21, 0x2a, 0x1f, 0xe6, 0x9c, 0x89, 0xe6, 0x95, 0x88, 0xe6, 0x9c, 0x9f, 0xe5,
	0xa4, 0xa9, 0xe6, 0x95, 0xb0, 0x20, 0xe7, 0x9b, 0xb8, 0xe5, 0xaf, 0xb9, 0xe6, 0x97, 0xb6, 0xe6,
	0x9c, 0x89, 0xe5, 0x80, 0xbc, 0x52, 0x0a, 0x69, 0x6e, 0x44, 0x61, 0x74, 0x65, 0x44, 0x61, 0x79,
	0x73, 0x12, 0x39, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1a, 0x92, 0x41, 0x17, 0x2a, 0x15, 0xe6, 0x9c, 0x89, 0xe6,
	0x95, 0x88, 0xe6, 0x9c, 0x9f, 0xe5, 0xbc, 0x80, 0xe5, 0xa7, 0x8b, 0xe6, 0x97, 0xb6, 0xe9, 0x97,
	0xb4, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x35, 0x0a, 0x08,
	0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1a,
	0x92, 0x41, 0x17, 0x2a, 0x15, 0xe6, 0x9c, 0x89, 0xe6, 0x95, 0x88, 0xe6, 0x9c, 0x9f, 0xe7, 0xbb,
	0x93, 0xe6, 0x9d, 0x9f, 0xe6, 0x97, 0xb6, 0xe9, 0x97, 0xb4, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54,
	0x69, 0x6d, 0x65, 0x12, 0x2e, 0x0a, 0x09, 0x75, 0x73, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe4, 0xbd, 0xbf,
	0xe7, 0x94, 0xa8, 0xe6, 0xac, 0xa1, 0xe6, 0x95, 0xb0, 0x52, 0x08, 0x75, 0x73, 0x65, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x12, 0x3e, 0x0a, 0x11, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11,
	0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe6, 0x8e, 0xa8, 0xe5, 0xb9, 0xbf, 0xe6, 0xb8, 0xa0, 0xe9, 0x81,
	0x93, 0x52, 0x10, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x68, 0x61, 0x6e,
	0x6e, 0x65, 0x6c, 0x12, 0x2a, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x70, 0x6f, 0x73, 0x65, 0x72, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0e, 0x92, 0x41, 0x0b, 0x2a, 0x09, 0xe7, 0x94, 0xb3, 0xe8,
	0xaf, 0xb7, 0xe4, 0xba, 0xba, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x70, 0x6f, 0x73, 0x65, 0x72, 0x12,
	0x31, 0x0a, 0x0c, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0e, 0x92, 0x41, 0x0b, 0x2a, 0x09, 0xe5, 0x88, 0x9b, 0xe5,
	0xbb, 0xba, 0xe4, 0xba, 0xba, 0x52, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x55, 0x73,
	0x65, 0x72, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x05, 0x42, 0x07,
	0x92, 0x41, 0x04, 0x2a, 0x02, 0x69, 0x64, 0x52, 0x02, 0x69, 0x64, 0x12, 0x30, 0x0a, 0x0a, 0x69,
	0x73, 0x5f, 0x70, 0x69, 0x6e, 0x6b, 0x61, 0x67, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x05, 0x42,
	0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe6, 0x98, 0xaf, 0xe5, 0x90, 0xa6, 0xe5, 0x8c, 0x85, 0xe9,
	0x82, 0xae, 0x52, 0x09, 0x69, 0x73, 0x50, 0x69, 0x6e, 0x6b, 0x61, 0x67, 0x65, 0x12, 0x2d, 0x0a,
	0x05, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x42, 0x17, 0x92, 0x41,
	0x14, 0x2a, 0x12, 0xe5, 0xbc, 0xb9, 0xe6, 0xa1, 0x86, 0xe5, 0xb1, 0x95, 0xe7, 0xa4, 0xba, 0xe5,
	0x9b, 0xbe, 0xe7, 0x89, 0x87, 0x52, 0x05, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x22, 0x1a, 0x0a, 0x18,
	0x45, 0x64, 0x69, 0x74, 0x47, 0x69, 0x66, 0x74, 0x43, 0x61, 0x72, 0x64, 0x53, 0x65, 0x74, 0x74,
	0x69, 0x6e, 0x67, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x37, 0x0a, 0x1c, 0x44, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x47, 0x69, 0x66, 0x74, 0x43, 0x61, 0x72, 0x64, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e,
	0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x42, 0x07, 0x92, 0x41, 0x04, 0x2a, 0x02, 0x69, 0x64, 0x52, 0x02, 0x69,
	0x64, 0x22, 0x1c, 0x0a, 0x1a, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x47, 0x69, 0x66, 0x74, 0x43,
	0x61, 0x72, 0x64, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22,
	0x3a, 0x0a, 0x1f, 0x47, 0x65, 0x74, 0x53, 0x69, 0x6e, 0x67, 0x6c, 0x65, 0x47, 0x69, 0x66, 0x74,
	0x43, 0x61, 0x72, 0x64, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x42, 0x07,
	0x92, 0x41, 0x04, 0x2a, 0x02, 0x69, 0x64, 0x52, 0x02, 0x69, 0x64, 0x22, 0xa7, 0x05, 0x0a, 0x1d,
	0x47, 0x65, 0x74, 0x53, 0x69, 0x6e, 0x67, 0x6c, 0x65, 0x47, 0x69, 0x66, 0x74, 0x43, 0x61, 0x72,
	0x64, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x28, 0x0a,
	0x08, 0x67, 0x6f, 0x6f, 0x64, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe5, 0x95, 0x86, 0xe5, 0x93, 0x81, 0x49, 0x44, 0x52, 0x07,
	0x67, 0x6f, 0x6f, 0x64, 0x73, 0x49, 0x64, 0x12, 0x3d, 0x0a, 0x07, 0x69, 0x6e, 0x5f, 0x64, 0x61,
	0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x42, 0x24, 0x92, 0x41, 0x21, 0x2a, 0x1f, 0xe6,
	0x9c, 0x89, 0xe6, 0x95, 0x88, 0xe6, 0x9c, 0x9f, 0xe7, 0xb1, 0xbb, 0xe5, 0x9e, 0x8b, 0x20, 0x30,
	0xe5, 0x9b, 0xba, 0xe5, 0xae, 0x9a, 0x20, 0x31, 0xe7, 0x9b, 0xb8, 0xe5, 0xaf, 0xb9, 0x52, 0x06,
	0x69, 0x6e, 0x44, 0x61, 0x74, 0x65, 0x12, 0x46, 0x0a, 0x0c, 0x69, 0x6e, 0x5f, 0x64, 0x61, 0x74,
	0x65, 0x5f, 0x64, 0x61, 0x79, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x42, 0x24, 0x92, 0x41,
	0x21, 0x2a, 0x1f, 0xe6, 0x9c, 0x89, 0xe6, 0x95, 0x88, 0xe6, 0x9c, 0x9f, 0xe5, 0xa4, 0xa9, 0xe6,
	0x95, 0xb0, 0x20, 0xe7, 0x9b, 0xb8, 0xe5, 0xaf, 0xb9, 0xe6, 0x97, 0xb6, 0xe6, 0x9c, 0x89, 0xe5,
	0x80, 0xbc, 0x52, 0x0a, 0x69, 0x6e, 0x44, 0x61, 0x74, 0x65, 0x44, 0x61, 0x79, 0x73, 0x12, 0x39,
	0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x1a, 0x92, 0x41, 0x17, 0x2a, 0x15, 0xe6, 0x9c, 0x89, 0xe6, 0x95, 0x88, 0xe6,
	0x9c, 0x9f, 0xe5, 0xbc, 0x80, 0xe5, 0xa7, 0x8b, 0xe6, 0x97, 0xb6, 0xe9, 0x97, 0xb4, 0x52, 0x09,
	0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x35, 0x0a, 0x08, 0x65, 0x6e, 0x64,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1a, 0x92, 0x41, 0x17,
	0x2a, 0x15, 0xe6, 0x9c, 0x89, 0xe6, 0x95, 0x88, 0xe6, 0x9c, 0x9f, 0xe7, 0xbb, 0x93, 0xe6, 0x9d,
	0x9f, 0xe6, 0x97, 0xb6, 0xe9, 0x97, 0xb4, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65,
	0x12, 0x2e, 0x0a, 0x09, 0x75, 0x73, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x05, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe4, 0xbd, 0xbf, 0xe7, 0x94, 0xa8,
	0xe6, 0xac, 0xa1, 0xe6, 0x95, 0xb0, 0x52, 0x08, 0x75, 0x73, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x12, 0x3e, 0x0a, 0x11, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x68,
	0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e,
	0x2a, 0x0c, 0xe6, 0x8e, 0xa8, 0xe5, 0xb9, 0xbf, 0xe6, 0xb8, 0xa0, 0xe9, 0x81, 0x93, 0x52, 0x10,
	0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c,
	0x12, 0x2a, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x70, 0x6f, 0x73, 0x65, 0x72, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x0e, 0x92, 0x41, 0x0b, 0x2a, 0x09, 0xe7, 0x94, 0xb3, 0xe8, 0xaf, 0xb7, 0xe4,
	0xba, 0xba, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x70, 0x6f, 0x73, 0x65, 0x72, 0x12, 0x31, 0x0a, 0x0c,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x0e, 0x92, 0x41, 0x0b, 0x2a, 0x09, 0xe5, 0x88, 0x9b, 0xe5, 0xbb, 0xba, 0xe4,
	0xba, 0xba, 0x52, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x55, 0x73, 0x65, 0x72, 0x12,
	0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0x92, 0x41, 0x04,
	0x2a, 0x02, 0x69, 0x64, 0x52, 0x02, 0x69, 0x64, 0x12, 0x26, 0x0a, 0x0a, 0x67, 0x6f, 0x6f, 0x64,
	0x73, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0x92, 0x41,
	0x04, 0x2a, 0x02, 0x69, 0x64, 0x52, 0x09, 0x67, 0x6f, 0x6f, 0x64, 0x73, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x30, 0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x70, 0x69, 0x6e, 0x6b, 0x61, 0x67, 0x65, 0x18, 0x0c,
	0x20, 0x01, 0x28, 0x05, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe6, 0x98, 0xaf, 0xe5, 0x90,
	0xa6, 0xe5, 0x8c, 0x85, 0xe9, 0x82, 0xae, 0x52, 0x09, 0x69, 0x73, 0x50, 0x69, 0x6e, 0x6b, 0x61,
	0x67, 0x65, 0x12, 0x21, 0x0a, 0x05, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe5, 0x9b, 0xbe, 0xe7, 0x89, 0x87, 0x52, 0x05,
	0x69, 0x6d, 0x61, 0x67, 0x65, 0x22, 0x40, 0x0a, 0x25, 0x47, 0x65, 0x74, 0x53, 0x69, 0x6e, 0x67,
	0x6c, 0x65, 0x47, 0x69, 0x66, 0x74, 0x43, 0x61, 0x72, 0x64, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e,
	0x67, 0x49, 0x73, 0x53, 0x65, 0x6e, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x42, 0x07, 0x92, 0x41, 0x04, 0x2a,
	0x02, 0x69, 0x64, 0x52, 0x02, 0x69, 0x64, 0x22, 0x45, 0x0a, 0x23, 0x47, 0x65, 0x74, 0x53, 0x69,
	0x6e, 0x67, 0x6c, 0x65, 0x47, 0x69, 0x66, 0x74, 0x43, 0x61, 0x72, 0x64, 0x53, 0x65, 0x74, 0x74,
	0x69, 0x6e, 0x67, 0x49, 0x73, 0x53, 0x65, 0x6e, 0x64, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x1e,
	0x0a, 0x06, 0x69, 0x73, 0x5f, 0x75, 0x73, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x42, 0x07,
	0x92, 0x41, 0x04, 0x2a, 0x02, 0x69, 0x64, 0x52, 0x05, 0x69, 0x73, 0x55, 0x73, 0x65, 0x22, 0x73,
	0x0a, 0x13, 0x53, 0x65, 0x6e, 0x64, 0x47, 0x69, 0x66, 0x74, 0x43, 0x61, 0x72, 0x64, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x28, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe7, 0x94,
	0xa8, 0xe6, 0x88, 0xb7, 0x49, 0x64, 0x52, 0x07, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x73, 0x12,
	0x32, 0x0a, 0x0a, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x05, 0x42, 0x13, 0x92, 0x41, 0x10, 0x2a, 0x0e, 0xe5, 0x8d, 0xa1, 0xe5, 0x88, 0xb8,
	0xe9, 0x85, 0x8d, 0xe7, 0xbd, 0xae, 0x49, 0x64, 0x52, 0x09, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e,
	0x67, 0x49, 0x64, 0x22, 0x13, 0x0a, 0x11, 0x53, 0x65, 0x6e, 0x64, 0x47, 0x69, 0x66, 0x74, 0x43,
	0x61, 0x72, 0x64, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x97, 0x01, 0x0a, 0x19, 0x53, 0x65, 0x6e,
	0x64, 0x47, 0x69, 0x66, 0x74, 0x43, 0x61, 0x72, 0x64, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x32, 0x0a, 0x0a, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e,
	0x67, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x42, 0x13, 0x92, 0x41, 0x10, 0x2a,
	0x0e, 0xe5, 0x8d, 0xa1, 0xe5, 0x88, 0xb8, 0xe9, 0x85, 0x8d, 0xe7, 0xbd, 0xae, 0x49, 0x64, 0x52,
	0x09, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x04, 0x70, 0x61,
	0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe9,
	0xa1, 0xb5, 0xe7, 0xa0, 0x81, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x25, 0x0a, 0x04, 0x73,
	0x69, 0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c,
	0xe6, 0xaf, 0x8f, 0xe9, 0xa1, 0xb5, 0xe6, 0x95, 0xb0, 0xe9, 0x87, 0x8f, 0x52, 0x04, 0x73, 0x69,
	0x7a, 0x65, 0x22, 0x8a, 0x01, 0x0a, 0x17, 0x53, 0x65, 0x6e, 0x64, 0x47, 0x69, 0x66, 0x74, 0x43,
	0x61, 0x72, 0x64, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x4c,
	0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76,
	0x31, 0x2e, 0x53, 0x65, 0x6e, 0x64, 0x47, 0x69, 0x66, 0x74, 0x43, 0x61, 0x72, 0x64, 0x52, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe5,
	0x88, 0x97, 0xe8, 0xa1, 0xa8, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x21, 0x0a, 0x05,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x42, 0x0b, 0x92, 0x41, 0x08,
	0x2a, 0x06, 0xe6, 0x80, 0xbb, 0xe6, 0x95, 0xb0, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x22,
	0xa7, 0x02, 0x0a, 0x16, 0x53, 0x65, 0x6e, 0x64, 0x47, 0x69, 0x66, 0x74, 0x43, 0x61, 0x72, 0x64,
	0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x24, 0x0a, 0x07, 0x75, 0x73,
	0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0x92, 0x41, 0x08,
	0x2a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x69, 0x64, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64,
	0x12, 0x34, 0x0a, 0x0c, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe9, 0xa2, 0x86,
	0xe5, 0x8f, 0x96, 0xe6, 0x97, 0xb6, 0xe9, 0x97, 0xb4, 0x52, 0x0b, 0x72, 0x65, 0x63, 0x65, 0x69,
	0x76, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x2e, 0x0a, 0x09, 0x75, 0x73, 0x65, 0x64, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c,
	0xe4, 0xbd, 0xbf, 0xe7, 0x94, 0xa8, 0xe6, 0x97, 0xb6, 0xe9, 0x97, 0xb4, 0x52, 0x08, 0x75, 0x73,
	0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe7, 0x8a, 0xb6,
	0xe6, 0x80, 0x81, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x32, 0x0a, 0x0b, 0x73,
	0x68, 0x6f, 0x77, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe6, 0x98, 0xbe, 0xe7, 0xa4, 0xba, 0xe7, 0x8a, 0xb6,
	0xe6, 0x80, 0x81, 0x52, 0x0a, 0x73, 0x68, 0x6f, 0x77, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x28, 0x0a, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x0e, 0x92, 0x41, 0x0b, 0x2a, 0x09, 0xe8, 0xae, 0xa2, 0xe5, 0x8d, 0x95, 0xe5, 0x8f, 0xb7,
	0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x2a, 0x42, 0x0a, 0x0d, 0x4c, 0x61, 0x62,
	0x65, 0x6c, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x18, 0x0a, 0x14, 0x4c, 0x41,
	0x42, 0x45, 0x4c, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x49, 0x4d, 0x41,
	0x47, 0x45, 0x10, 0x00, 0x12, 0x17, 0x0a, 0x13, 0x4c, 0x41, 0x42, 0x45, 0x4c, 0x5f, 0x43, 0x41,
	0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x54, 0x45, 0x58, 0x54, 0x10, 0x01, 0x32, 0x99, 0x2a,
	0x0a, 0x0a, 0x42, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x12, 0x76, 0x0a, 0x09,
	0x4c, 0x61, 0x62, 0x65, 0x6c, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65,
	0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x22, 0x30, 0x92, 0x41, 0x16, 0x0a, 0x06, 0xe6, 0xa0, 0x87, 0xe7, 0xad, 0xbe, 0x12,
	0x0c, 0xe6, 0xb7, 0xbb, 0xe5, 0x8a, 0xa0, 0xe6, 0xa0, 0x87, 0xe7, 0xad, 0xbe, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x11, 0x12, 0x0f, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x6c,
	0x61, 0x62, 0x65, 0x6c, 0x12, 0xa3, 0x01, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x74,
	0x69, 0x63, 0x53, 0x70, 0x65, 0x63, 0x12, 0x27, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c,
	0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x74,
	0x61, 0x74, 0x69, 0x63, 0x53, 0x70, 0x65, 0x63, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x25, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65,
	0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x74, 0x69, 0x63, 0x53, 0x70, 0x65,
	0x63, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x42, 0x92, 0x41, 0x1c, 0x0a, 0x06, 0xe5, 0x95, 0x86,
	0xe5, 0x93, 0x81, 0x12, 0x12, 0xe5, 0x95, 0x86, 0xe5, 0x93, 0x81, 0xe9, 0x9d, 0x99, 0xe6, 0x80,
	0x81, 0xe8, 0xa7, 0x84, 0xe6, 0xa0, 0xbc, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1d, 0x12, 0x1b, 0x2f,
	0x76, 0x31, 0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x67, 0x6f, 0x6f, 0x64, 0x73, 0x2f, 0x73,
	0x74, 0x61, 0x74, 0x69, 0x63, 0x2f, 0x73, 0x70, 0x65, 0x63, 0x12, 0x7f, 0x0a, 0x08, 0x41, 0x64,
	0x64, 0x47, 0x6f, 0x6f, 0x64, 0x73, 0x12, 0x1c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c,
	0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x6f, 0x6f, 0x64, 0x73,
	0x49, 0x6e, 0x66, 0x6f, 0x1a, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f,
	0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64, 0x64, 0x47, 0x6f, 0x6f, 0x64,
	0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x33, 0x92, 0x41, 0x16, 0x0a, 0x06, 0xe5, 0x95, 0x86,
	0xe5, 0x93, 0x81, 0x12, 0x0c, 0xe6, 0xb7, 0xbb, 0xe5, 0x8a, 0xa0, 0xe5, 0x95, 0x86, 0xe5, 0x93,
	0x81, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x14, 0x3a, 0x01, 0x2a, 0x22, 0x0f, 0x2f, 0x76, 0x31, 0x2f,
	0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x67, 0x6f, 0x6f, 0x64, 0x73, 0x12, 0x79, 0x0a, 0x0b, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x47, 0x6f, 0x6f, 0x64, 0x73, 0x12, 0x1c, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47,
	0x6f, 0x6f, 0x64, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x1a, 0x12, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x38, 0x92, 0x41,
	0x16, 0x0a, 0x06, 0xe5, 0x95, 0x86, 0xe5, 0x93, 0x81, 0x12, 0x0c, 0xe4, 0xbf, 0xae, 0xe6, 0x94,
	0xb9, 0xe5, 0x95, 0x86, 0xe5, 0x93, 0x81, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x19, 0x3a, 0x01, 0x2a,
	0x1a, 0x14, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x67, 0x6f, 0x6f, 0x64,
	0x73, 0x2f, 0x7b, 0x69, 0x64, 0x7d, 0x12, 0x8b, 0x01, 0x0a, 0x0c, 0x47, 0x65, 0x74, 0x47, 0x6f,
	0x6f, 0x64, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f,
	0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x47,
	0x6f, 0x6f, 0x64, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x1c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65,
	0x2e, 0x76, 0x31, 0x2e, 0x47, 0x6f, 0x6f, 0x64, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x35, 0x92,
	0x41, 0x16, 0x0a, 0x06, 0xe5, 0x95, 0x86, 0xe5, 0x93, 0x81, 0x12, 0x0c, 0xe5, 0x95, 0x86, 0xe5,
	0x93, 0x81, 0xe8, 0xaf, 0xa6, 0xe6, 0x83, 0x85, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x16, 0x12, 0x14,
	0x2f, 0x76, 0x31, 0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x67, 0x6f, 0x6f, 0x64, 0x73, 0x2f,
	0x7b, 0x69, 0x64, 0x7d, 0x12, 0x7f, 0x0a, 0x0b, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x47, 0x6f,
	0x6f, 0x64, 0x73, 0x12, 0x25, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73,
	0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x47, 0x6f,
	0x6f, 0x64, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x12, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x35,
	0x92, 0x41, 0x16, 0x0a, 0x06, 0xe5, 0x95, 0x86, 0xe5, 0x93, 0x81, 0x12, 0x0c, 0xe5, 0x88, 0xa0,
	0xe9, 0x99, 0xa4, 0xe5, 0x95, 0x86, 0xe5, 0x93, 0x81, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x16, 0x2a,
	0x14, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x67, 0x6f, 0x6f, 0x64, 0x73,
	0x2f, 0x7b, 0x69, 0x64, 0x7d, 0x12, 0x8e, 0x01, 0x0a, 0x0c, 0x47, 0x65, 0x74, 0x47, 0x6f, 0x6f,
	0x64, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c,
	0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x47, 0x6f,
	0x6f, 0x64, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x24,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e,
	0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x47, 0x6f, 0x6f, 0x64, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x22, 0x30, 0x92, 0x41, 0x16, 0x0a, 0x06, 0xe5, 0x95, 0x86, 0xe5, 0x93,
	0x81, 0x12, 0x0c, 0xe5, 0x95, 0x86, 0xe5, 0x93, 0x81, 0xe5, 0x88, 0x97, 0xe8, 0xa1, 0xa8, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x11, 0x12, 0x0f, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e,
	0x2f, 0x67, 0x6f, 0x6f, 0x64, 0x73, 0x12, 0x95, 0x01, 0x0a, 0x0e, 0x53, 0x65, 0x74, 0x47, 0x6f,
	0x6f, 0x64, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x25, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x6f,
	0x6f, 0x64, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x12, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x22, 0x48, 0x92, 0x41, 0x1f, 0x0a, 0x06, 0xe5, 0x95, 0x86, 0xe5, 0x93,
	0x81, 0x12, 0x15, 0xe4, 0xbf, 0xae, 0xe6, 0x94, 0xb9, 0xe5, 0x95, 0x86, 0xe5, 0x93, 0x81, 0xe4,
	0xb8, 0x8a, 0xe4, 0xb8, 0x8b, 0xe6, 0x9e, 0xb6, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x20, 0x3a, 0x01,
	0x2a, 0x1a, 0x1b, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x67, 0x6f, 0x6f,
	0x64, 0x73, 0x2f, 0x7b, 0x69, 0x64, 0x7d, 0x2f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0xa8,
	0x01, 0x0a, 0x0c, 0x47, 0x65, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12,
	0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65,
	0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x27, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f,
	0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x54,
	0x61, 0x73, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x47, 0x92, 0x41, 0x28, 0x0a, 0x0c, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a, 0xa1, 0xe7, 0xae, 0xa1,
	0xe7, 0x90, 0x86, 0x12, 0x18, 0xe8, 0x8e, 0xb7, 0xe5, 0x8f, 0x96, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a,
	0xa1, 0xe7, 0xb1, 0xbb, 0xe5, 0x9e, 0x8b, 0xe5, 0x88, 0x97, 0xe8, 0xa1, 0xa8, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x16, 0x12, 0x14, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x74,
	0x61, 0x73, 0x6b, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x12, 0xa2, 0x01, 0x0a, 0x08, 0x4c, 0x69,
	0x73, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x27, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c,
	0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64, 0x6d, 0x69, 0x6e,
	0x4c, 0x69, 0x73, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x25, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65,
	0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64, 0x6d, 0x69, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x61, 0x73,
	0x6b, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x46, 0x92, 0x41, 0x28, 0x0a, 0x0c, 0xe4, 0xbb, 0xbb,
	0xe5, 0x8a, 0xa1, 0xe7, 0xae, 0xa1, 0xe7, 0x90, 0x86, 0x12, 0x18, 0xe5, 0x90, 0x8e, 0xe5, 0x8f,
	0xb0, 0xe6, 0x9f, 0xa5, 0xe8, 0xaf, 0xa2, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a, 0xa1, 0xe5, 0x88, 0x97,
	0xe8, 0xa1, 0xa8, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x15, 0x12, 0x13, 0x2f, 0x76, 0x31, 0x2f, 0x61,
	0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x74, 0x61, 0x73, 0x6b, 0x2f, 0x6c, 0x69, 0x73, 0x74, 0x12, 0xcf,
	0x01, 0x0a, 0x10, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x50, 0x72, 0x6f, 0x67, 0x72,
	0x65, 0x73, 0x73, 0x12, 0x2f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73,
	0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64, 0x6d, 0x69, 0x6e, 0x4c, 0x69, 0x73,
	0x74, 0x54, 0x61, 0x73, 0x6b, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x2d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f,
	0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64, 0x6d, 0x69, 0x6e, 0x4c, 0x69,
	0x73, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x52, 0x65,
	0x70, 0x6c, 0x79, 0x22, 0x5b, 0x92, 0x41, 0x34, 0x0a, 0x0c, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a, 0xa1,
	0xe7, 0xae, 0xa1, 0xe7, 0x90, 0x86, 0x12, 0x24, 0xe5, 0x90, 0x8e, 0xe5, 0x8f, 0xb0, 0xe6, 0x9f,
	0xa5, 0xe8, 0xaf, 0xa2, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a, 0xa1,
	0xe8, 0xbf, 0x9b, 0xe5, 0xba, 0xa6, 0xe5, 0x88, 0x97, 0xe8, 0xa1, 0xa8, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x1e, 0x12, 0x1c, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x74, 0x61,
	0x73, 0x6b, 0x2f, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x2f, 0x6c, 0x69, 0x73, 0x74,
	0x12, 0xab, 0x01, 0x0a, 0x10, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64,
	0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64, 0x6d, 0x69, 0x6e, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x12, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x52, 0x92, 0x41, 0x28, 0x0a,
	0x0c, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a, 0xa1, 0xe7, 0xae, 0xa1, 0xe7, 0x90, 0x86, 0x12, 0x18, 0xe5,
	0x90, 0x8e, 0xe5, 0x8f, 0xb0, 0xe4, 0xbf, 0xae, 0xe6, 0x94, 0xb9, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a,
	0xa1, 0xe7, 0x8a, 0xb6, 0xe6, 0x80, 0x81, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x21, 0x3a, 0x01, 0x2a,
	0x22, 0x1c, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x74, 0x61, 0x73, 0x6b,
	0x2f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0xab,
	0x01, 0x0a, 0x10, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x12, 0x2f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73,
	0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64, 0x6d, 0x69, 0x6e, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x12, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x6d,
	0x70, 0x74, 0x79, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x52, 0x92, 0x41, 0x28, 0x0a, 0x0c, 0xe4,
	0xbb, 0xbb, 0xe5, 0x8a, 0xa1, 0xe7, 0xae, 0xa1, 0xe7, 0x90, 0x86, 0x12, 0x18, 0xe5, 0x90, 0x8e,
	0xe5, 0x8f, 0xb0, 0xe4, 0xbf, 0xae, 0xe6, 0x94, 0xb9, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a, 0xa1, 0xe4,
	0xbf, 0xa1, 0xe6, 0x81, 0xaf, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x21, 0x3a, 0x01, 0x2a, 0x22, 0x1c,
	0x2f, 0x76, 0x31, 0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x74, 0x61, 0x73, 0x6b, 0x2f, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0x99, 0x01, 0x0a,
	0x0a, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x29, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e,
	0x41, 0x64, 0x6d, 0x69, 0x6e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x12, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x4c, 0x92, 0x41, 0x22, 0x0a,
	0x0c, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a, 0xa1, 0xe7, 0xae, 0xa1, 0xe7, 0x90, 0x86, 0x12, 0x12, 0xe5,
	0x90, 0x8e, 0xe5, 0x8f, 0xb0, 0xe5, 0x88, 0xa0, 0xe9, 0x99, 0xa4, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a,
	0xa1, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x21, 0x3a, 0x01, 0x2a, 0x22, 0x1c, 0x2f, 0x76, 0x31, 0x2f,
	0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x74, 0x61, 0x73, 0x6b, 0x2f, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x2f, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x12, 0xa7, 0x01, 0x0a, 0x0a, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x29, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f,
	0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64, 0x6d, 0x69,
	0x6e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x27, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74,
	0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64, 0x6d, 0x69, 0x6e, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x45, 0x92, 0x41, 0x22,
	0x0a, 0x0c, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a, 0xa1, 0xe7, 0xae, 0xa1, 0xe7, 0x90, 0x86, 0x12, 0x12,
	0xe5, 0x90, 0x8e, 0xe5, 0x8f, 0xb0, 0xe6, 0x96, 0xb0, 0xe5, 0xa2, 0x9e, 0xe4, 0xbb, 0xbb, 0xe5,
	0x8a, 0xa1, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1a, 0x3a, 0x01, 0x2a, 0x22, 0x15, 0x2f, 0x76, 0x31,
	0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x74, 0x61, 0x73, 0x6b, 0x2f, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x12, 0xb3, 0x01, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x12, 0x2c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f,
	0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64, 0x6d, 0x69, 0x6e, 0x47, 0x65,
	0x74, 0x54, 0x61, 0x73, 0x6b, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x2a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74,
	0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64, 0x6d, 0x69, 0x6e, 0x47, 0x65, 0x74, 0x54,
	0x61, 0x73, 0x6b, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x48,
	0x92, 0x41, 0x28, 0x0a, 0x0c, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a, 0xa1, 0xe7, 0xae, 0xa1, 0xe7, 0x90,
	0x86, 0x12, 0x18, 0xe5, 0x90, 0x8e, 0xe5, 0x8f, 0xb0, 0xe8, 0x8e, 0xb7, 0xe5, 0x8f, 0x96, 0xe4,
	0xbb, 0xbb, 0xe5, 0x8a, 0xa1, 0xe8, 0xaf, 0xa6, 0xe6, 0x83, 0x85, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x17, 0x12, 0x15, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x74, 0x61, 0x73,
	0x6b, 0x2f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0xa8, 0x01, 0x0a, 0x0d, 0x4c, 0x69, 0x73,
	0x74, 0x54, 0x61, 0x73, 0x6b, 0x47, 0x6f, 0x6f, 0x64, 0x73, 0x12, 0x14, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x2a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72,
	0x65, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64, 0x6d, 0x69, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x61,
	0x73, 0x6b, 0x47, 0x6f, 0x6f, 0x64, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x55, 0x92, 0x41,
	0x31, 0x0a, 0x0c, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a, 0xa1, 0xe7, 0xae, 0xa1, 0xe7, 0x90, 0x86, 0x12,
	0x21, 0xe5, 0x90, 0x8e, 0xe5, 0x8f, 0xb0, 0xe6, 0x9f, 0xa5, 0xe8, 0xaf, 0xa2, 0xe4, 0xbb, 0xbb,
	0xe5, 0x8a, 0xa1, 0xe9, 0x85, 0x8d, 0xe7, 0xbd, 0xae, 0xe7, 0x9a, 0x84, 0xe5, 0x95, 0x86, 0xe5,
	0x93, 0x81, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1b, 0x12, 0x19, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x64,
	0x6d, 0x69, 0x6e, 0x2f, 0x74, 0x61, 0x73, 0x6b, 0x2f, 0x67, 0x6f, 0x6f, 0x64, 0x73, 0x2f, 0x6c,
	0x69, 0x73, 0x74, 0x12, 0xb2, 0x01, 0x0a, 0x0e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x69, 0x67, 0x6e,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x28, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c,
	0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53,
	0x69, 0x67, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72,
	0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x69, 0x67, 0x6e, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x4e, 0x92, 0x41, 0x28, 0x0a, 0x0c, 0xe7,
	0xad, 0xbe, 0xe5, 0x88, 0xb0, 0xe9, 0x85, 0x8d, 0xe7, 0xbd, 0xae, 0x12, 0x18, 0xe8, 0x8e, 0xb7,
	0xe5, 0x8f, 0x96, 0xe7, 0xad, 0xbe, 0xe5, 0x88, 0xb0, 0xe9, 0x85, 0x8d, 0xe7, 0xbd, 0xae, 0xe5,
	0x88, 0x97, 0xe8, 0xa1, 0xa8, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1d, 0x12, 0x1b, 0x2f, 0x76, 0x31,
	0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x73, 0x69, 0x67, 0x6e, 0x2d, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x73, 0x2f, 0x6c, 0x69, 0x73, 0x74, 0x12, 0xb6, 0x01, 0x0a, 0x10, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x53, 0x69, 0x67, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x2a, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76,
	0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x69, 0x67, 0x6e, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x28, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x53, 0x69, 0x67, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65,
	0x70, 0x6c, 0x79, 0x22, 0x4c, 0x92, 0x41, 0x22, 0x0a, 0x0c, 0xe7, 0xad, 0xbe, 0xe5, 0x88, 0xb0,
	0xe9, 0x85, 0x8d, 0xe7, 0xbd, 0xae, 0x12, 0x12, 0xe6, 0x96, 0xb0, 0xe5, 0xa2, 0x9e, 0xe7, 0xad,
	0xbe, 0xe5, 0x88, 0xb0, 0xe9, 0x85, 0x8d, 0xe7, 0xbd, 0xae, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x21,
	0x3a, 0x01, 0x2a, 0x22, 0x1c, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x73,
	0x69, 0x67, 0x6e, 0x2f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2f, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x12, 0xb6, 0x01, 0x0a, 0x10, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x69, 0x67, 0x6e,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x2a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c,
	0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x53, 0x69, 0x67, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x28, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74,
	0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x69, 0x67,
	0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x4c, 0x92, 0x41,
	0x22, 0x0a, 0x0c, 0xe7, 0xad, 0xbe, 0xe5, 0x88, 0xb0, 0xe9, 0x85, 0x8d, 0xe7, 0xbd, 0xae, 0x12,
	0x12, 0xe4, 0xbf, 0xae, 0xe6, 0x94, 0xb9, 0xe7, 0xad, 0xbe, 0xe5, 0x88, 0xb0, 0xe9, 0x85, 0x8d,
	0xe7, 0xbd, 0xae, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x21, 0x3a, 0x01, 0x2a, 0x22, 0x1c, 0x2f, 0x76,
	0x31, 0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x73, 0x69, 0x67, 0x6e, 0x2f, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x2f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0xb6, 0x01, 0x0a, 0x10, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x69, 0x67, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12,
	0x2a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65,
	0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x69, 0x67, 0x6e, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x28, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x69, 0x67, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x4c, 0x92, 0x41, 0x22, 0x0a, 0x0c, 0xe7, 0xad, 0xbe, 0xe5,
	0x88, 0xb0, 0xe9, 0x85, 0x8d, 0xe7, 0xbd, 0xae, 0x12, 0x12, 0xe5, 0x88, 0xa0, 0xe9, 0x99, 0xa4,
	0xe7, 0xad, 0xbe, 0xe5, 0x88, 0xb0, 0xe9, 0x85, 0x8d, 0xe7, 0xbd, 0xae, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x21, 0x3a, 0x01, 0x2a, 0x22, 0x1c, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e,
	0x2f, 0x73, 0x69, 0x67, 0x6e, 0x2f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2f, 0x64, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x12, 0xc4, 0x01, 0x0a, 0x17, 0x47, 0x69, 0x66, 0x74, 0x43, 0x61, 0x72, 0x64,
	0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x67, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12,
	0x31, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65,
	0x2e, 0x76, 0x31, 0x2e, 0x47, 0x69, 0x66, 0x74, 0x43, 0x61, 0x72, 0x64, 0x53, 0x65, 0x74, 0x74,
	0x69, 0x6e, 0x67, 0x50, 0x61, 0x67, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x2f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74,
	0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x69, 0x66, 0x74, 0x43, 0x61, 0x72, 0x64, 0x53,
	0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x67, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x70, 0x6c, 0x79, 0x22, 0x45, 0x92, 0x41, 0x22, 0x0a, 0x09, 0xe5, 0x85, 0x91, 0xe6, 0x8d, 0xa2,
	0xe5, 0x8d, 0xa1, 0x12, 0x15, 0xe5, 0x85, 0x91, 0xe6, 0x8d, 0xa2, 0xe5, 0x8d, 0xa1, 0xe9, 0x85,
	0x8d, 0xe7, 0xbd, 0xae, 0xe5, 0x88, 0x97, 0xe8, 0xa1, 0xa8, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1a,
	0x12, 0x18, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x67, 0x69, 0x66, 0x74,
	0x2d, 0x63, 0x61, 0x72, 0x64, 0x2f, 0x6c, 0x69, 0x73, 0x74, 0x12, 0xbb, 0x01, 0x0a, 0x13, 0x45,
	0x64, 0x69, 0x74, 0x47, 0x69, 0x66, 0x74, 0x43, 0x61, 0x72, 0x64, 0x53, 0x65, 0x74, 0x74, 0x69,
	0x6e, 0x67, 0x12, 0x2d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74,
	0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x64, 0x69, 0x74, 0x47, 0x69, 0x66, 0x74, 0x43,
	0x61, 0x72, 0x64, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x2b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f,
	0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x64, 0x69, 0x74, 0x47, 0x69, 0x66, 0x74, 0x43, 0x61,
	0x72, 0x64, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x48,
	0x92, 0x41, 0x22, 0x0a, 0x09, 0xe5, 0x85, 0x91, 0xe6, 0x8d, 0xa2, 0xe5, 0x8d, 0xa1, 0x12, 0x15,
	0xe7, 0xbc, 0x96, 0xe8, 0xbe, 0x91, 0xe5, 0x85, 0x91, 0xe6, 0x8d, 0xa2, 0xe5, 0x8d, 0xa1, 0xe9,
	0x85, 0x8d, 0xe7, 0xbd, 0xae, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1d, 0x3a, 0x01, 0x2a, 0x22, 0x18,
	0x2f, 0x76, 0x31, 0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x67, 0x69, 0x66, 0x74, 0x2d, 0x63,
	0x61, 0x72, 0x64, 0x2f, 0x65, 0x64, 0x69, 0x74, 0x12, 0xc3, 0x01, 0x0a, 0x15, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x47, 0x69, 0x66, 0x74, 0x43, 0x61, 0x72, 0x64, 0x53, 0x65, 0x74, 0x74, 0x69,
	0x6e, 0x67, 0x12, 0x2f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74,
	0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x47, 0x69, 0x66,
	0x74, 0x43, 0x61, 0x72, 0x64, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x2d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73,
	0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x47, 0x69,
	0x66, 0x74, 0x43, 0x61, 0x72, 0x64, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x22, 0x4a, 0x92, 0x41, 0x22, 0x0a, 0x09, 0xe5, 0x85, 0x91, 0xe6, 0x8d, 0xa2, 0xe5,
	0x8d, 0xa1, 0x12, 0x15, 0xe5, 0x88, 0xa0, 0xe9, 0x99, 0xa4, 0xe5, 0x85, 0x91, 0xe6, 0x8d, 0xa2,
	0xe5, 0x8d, 0xa1, 0xe9, 0x85, 0x8d, 0xe7, 0xbd, 0xae, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1f, 0x3a,
	0x01, 0x2a, 0x22, 0x1a, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x67, 0x69,
	0x66, 0x74, 0x2d, 0x63, 0x61, 0x72, 0x64, 0x2f, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x12, 0xcf,
	0x01, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x53, 0x69, 0x6e, 0x67, 0x6c, 0x65, 0x47, 0x69, 0x66, 0x74,
	0x43, 0x61, 0x72, 0x64, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x12, 0x32, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e,
	0x47, 0x65, 0x74, 0x53, 0x69, 0x6e, 0x67, 0x6c, 0x65, 0x47, 0x69, 0x66, 0x74, 0x43, 0x61, 0x72,
	0x64, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x30, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65,
	0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x69, 0x6e, 0x67, 0x6c, 0x65, 0x47, 0x69, 0x66,
	0x74, 0x43, 0x61, 0x72, 0x64, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x70, 0x6c,
	0x79, 0x22, 0x4d, 0x92, 0x41, 0x28, 0x0a, 0x09, 0xe5, 0x85, 0x91, 0xe6, 0x8d, 0xa2, 0xe5, 0x8d,
	0xa1, 0x12, 0x1b, 0xe8, 0x8e, 0xb7, 0xe5, 0x8f, 0x96, 0xe5, 0x8d, 0x95, 0xe4, 0xb8, 0xaa, 0xe5,
	0x85, 0x91, 0xe6, 0x8d, 0xa2, 0xe5, 0x8d, 0xa1, 0xe9, 0x85, 0x8d, 0xe7, 0xbd, 0xae, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x1c, 0x12, 0x1a, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f,
	0x67, 0x69, 0x66, 0x74, 0x2d, 0x63, 0x61, 0x72, 0x64, 0x2f, 0x73, 0x69, 0x6e, 0x67, 0x6c, 0x65,
	0x12, 0xe8, 0x01, 0x0a, 0x1e, 0x47, 0x65, 0x74, 0x53, 0x69, 0x6e, 0x67, 0x6c, 0x65, 0x47, 0x69,
	0x66, 0x74, 0x43, 0x61, 0x72, 0x64, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x49, 0x73, 0x53,
	0x65, 0x6e, 0x64, 0x12, 0x38, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73,
	0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x69, 0x6e, 0x67, 0x6c,
	0x65, 0x47, 0x69, 0x66, 0x74, 0x43, 0x61, 0x72, 0x64, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67,
	0x49, 0x73, 0x53, 0x65, 0x6e, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x36, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76,
	0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x69, 0x6e, 0x67, 0x6c, 0x65, 0x47, 0x69, 0x66, 0x74, 0x43,
	0x61, 0x72, 0x64, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x49, 0x73, 0x53, 0x65, 0x6e, 0x64,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x54, 0x92, 0x41, 0x2e, 0x0a, 0x09, 0xe5, 0x85, 0x91, 0xe6,
	0x8d, 0xa2, 0xe5, 0x8d, 0xa1, 0x12, 0x21, 0xe8, 0x8e, 0xb7, 0xe5, 0x8f, 0x96, 0xe5, 0x8d, 0x95,
	0xe4, 0xb8, 0xaa, 0xe5, 0x85, 0x91, 0xe6, 0x8d, 0xa2, 0xe5, 0x8d, 0xa1, 0xe6, 0x98, 0xaf, 0xe5,
	0x90, 0xa6, 0xe5, 0x8f, 0x91, 0xe6, 0x94, 0xbe, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1d, 0x12, 0x1b,
	0x2f, 0x76, 0x31, 0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x67, 0x69, 0x66, 0x74, 0x2d, 0x63,
	0x61, 0x72, 0x64, 0x2f, 0x69, 0x73, 0x2d, 0x73, 0x65, 0x6e, 0x64, 0x12, 0xa0, 0x01, 0x0a, 0x0c,
	0x53, 0x65, 0x6e, 0x64, 0x47, 0x69, 0x66, 0x74, 0x43, 0x61, 0x72, 0x64, 0x12, 0x26, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31,
	0x2e, 0x53, 0x65, 0x6e, 0x64, 0x47, 0x69, 0x66, 0x74, 0x43, 0x61, 0x72, 0x64, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f,
	0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x6e, 0x64, 0x47, 0x69, 0x66,
	0x74, 0x43, 0x61, 0x72, 0x64, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x42, 0x92, 0x41, 0x1c, 0x0a,
	0x09, 0xe5, 0x85, 0x91, 0xe6, 0x8d, 0xa2, 0xe5, 0x8d, 0xa1, 0x12, 0x0f, 0xe5, 0x8f, 0x91, 0xe6,
	0x94, 0xbe, 0xe5, 0x85, 0x91, 0xe6, 0x8d, 0xa2, 0xe5, 0x8d, 0xa1, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x1d, 0x3a, 0x01, 0x2a, 0x22, 0x18, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f,
	0x67, 0x69, 0x66, 0x74, 0x2d, 0x63, 0x61, 0x72, 0x64, 0x2f, 0x73, 0x65, 0x6e, 0x64, 0x12, 0xb3,
	0x01, 0x0a, 0x12, 0x53, 0x65, 0x6e, 0x64, 0x47, 0x69, 0x66, 0x74, 0x43, 0x61, 0x72, 0x64, 0x52,
	0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x2c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64,
	0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x6e, 0x64, 0x47, 0x69,
	0x66, 0x74, 0x43, 0x61, 0x72, 0x64, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x2a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73,
	0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x6e, 0x64, 0x47, 0x69, 0x66, 0x74,
	0x43, 0x61, 0x72, 0x64, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22,
	0x43, 0x92, 0x41, 0x19, 0x0a, 0x09, 0xe5, 0x85, 0x91, 0xe6, 0x8d, 0xa2, 0xe5, 0x8d, 0xa1, 0x12,
	0x0c, 0xe5, 0x8f, 0x91, 0xe6, 0x94, 0xbe, 0xe8, 0xae, 0xb0, 0xe5, 0xbd, 0x95, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x21, 0x12, 0x1f, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x67,
	0x69, 0x66, 0x74, 0x2d, 0x63, 0x61, 0x72, 0x64, 0x2f, 0x73, 0x65, 0x6e, 0x64, 0x2d, 0x72, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x12, 0x99, 0x01, 0x0a, 0x0e, 0x41, 0x64, 0x6d, 0x69, 0x6e, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x28, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f,
	0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64, 0x6d, 0x69,
	0x6e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f,
	0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64, 0x6d, 0x69, 0x6e, 0x4f, 0x72, 0x64, 0x65, 0x72,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x35, 0x92, 0x41, 0x16, 0x0a, 0x06,
	0xe8, 0xae, 0xa2, 0xe5, 0x8d, 0x95, 0x12, 0x0c, 0xe8, 0xae, 0xa2, 0xe5, 0x8d, 0x95, 0xe5, 0x88,
	0x97, 0xe8, 0xa1, 0xa8, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x16, 0x12, 0x14, 0x2f, 0x76, 0x31, 0x2f,
	0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f, 0x6c, 0x69, 0x73, 0x74,
	0x12, 0xa1, 0x01, 0x0a, 0x10, 0x41, 0x64, 0x6d, 0x69, 0x6e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x2a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64,
	0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64, 0x6d, 0x69, 0x6e, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x28, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f,
	0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64, 0x6d, 0x69, 0x6e, 0x4f, 0x72, 0x64, 0x65, 0x72,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x37, 0x92, 0x41, 0x16,
	0x0a, 0x06, 0xe8, 0xae, 0xa2, 0xe5, 0x8d, 0x95, 0x12, 0x0c, 0xe8, 0xae, 0xa2, 0xe5, 0x8d, 0x95,
	0xe8, 0xaf, 0xa6, 0xe6, 0x83, 0x85, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x18, 0x12, 0x16, 0x2f, 0x76,
	0x31, 0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f, 0x64, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x12, 0x87, 0x01, 0x0a, 0x0c, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x44, 0x65,
	0x6c, 0x69, 0x76, 0x65, 0x72, 0x12, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64,
	0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x44,
	0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x12, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65, 0x70, 0x6c,
	0x79, 0x22, 0x3b, 0x92, 0x41, 0x16, 0x0a, 0x06, 0xe8, 0xae, 0xa2, 0xe5, 0x8d, 0x95, 0x12, 0x0c,
	0xe8, 0xae, 0xa2, 0xe5, 0x8d, 0x95, 0xe5, 0x8f, 0x91, 0xe8, 0xb4, 0xa7, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x1c, 0x3a, 0x01, 0x2a, 0x22, 0x17, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e,
	0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f, 0x64, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x12, 0x88,
	0x01, 0x0a, 0x11, 0x46, 0x65, 0x74, 0x63, 0x68, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x73, 0x12, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x6d,
	0x70, 0x74, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x12, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x49,
	0x92, 0x41, 0x22, 0x0a, 0x06, 0xe8, 0xae, 0xa2, 0xe5, 0x8d, 0x95, 0x12, 0x18, 0xe6, 0x89, 0x8b,
	0xe5, 0x8a, 0xa8, 0xe6, 0x8b, 0x89, 0xe5, 0x8f, 0x96, 0xe6, 0x8a, 0xa5, 0xe5, 0x91, 0x8a, 0xe8,
	0xae, 0xa2, 0xe5, 0x8d, 0x95, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1e, 0x12, 0x1c, 0x2f, 0x76, 0x31,
	0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f, 0x72, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x2f, 0x66, 0x65, 0x74, 0x63, 0x68, 0x42, 0x16, 0x5a, 0x14, 0x61, 0x70, 0x69,
	0x2f, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2f, 0x76, 0x31, 0x3b, 0x76,
	0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_gold_store_v1_background_proto_rawDescOnce sync.Once
	file_gold_store_v1_background_proto_rawDescData = file_gold_store_v1_background_proto_rawDesc
)

func file_gold_store_v1_background_proto_rawDescGZIP() []byte {
	file_gold_store_v1_background_proto_rawDescOnce.Do(func() {
		file_gold_store_v1_background_proto_rawDescData = protoimpl.X.CompressGZIP(file_gold_store_v1_background_proto_rawDescData)
	})
	return file_gold_store_v1_background_proto_rawDescData
}

var file_gold_store_v1_background_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_gold_store_v1_background_proto_msgTypes = make([]protoimpl.MessageInfo, 45)
var file_gold_store_v1_background_proto_goTypes = []interface{}{
	(LabelCategory)(0),                            // 0: api.gold_store.v1.LabelCategory
	(*OrderDeliverRequest)(nil),                   // 1: api.gold_store.v1.OrderDeliverRequest
	(*AdminOrderListRequest)(nil),                 // 2: api.gold_store.v1.AdminOrderListRequest
	(*AdminOrder)(nil),                            // 3: api.gold_store.v1.AdminOrder
	(*AdminOrderListReply)(nil),                   // 4: api.gold_store.v1.AdminOrderListReply
	(*AdminOrderDetailRequest)(nil),               // 5: api.gold_store.v1.AdminOrderDetailRequest
	(*AdminOrderDetailReply)(nil),                 // 6: api.gold_store.v1.AdminOrderDetailReply
	(*Label)(nil),                                 // 7: api.gold_store.v1.Label
	(*LabelListReply)(nil),                        // 8: api.gold_store.v1.LabelListReply
	(*GetStaticSpecRequest)(nil),                  // 9: api.gold_store.v1.GetStaticSpecRequest
	(*GoodsStaticSpecUnit)(nil),                   // 10: api.gold_store.v1.GoodsStaticSpecUnit
	(*GoodsStaticSpecValue)(nil),                  // 11: api.gold_store.v1.GoodsStaticSpecValue
	(*GoodsStaticSpec)(nil),                       // 12: api.gold_store.v1.GoodsStaticSpec
	(*GoodsStaticSpecOfCategory)(nil),             // 13: api.gold_store.v1.GoodsStaticSpecOfCategory
	(*GetStaticSpecReply)(nil),                    // 14: api.gold_store.v1.GetStaticSpecReply
	(*GoodsSpecTranslate)(nil),                    // 15: api.gold_store.v1.GoodsSpecTranslate
	(*GoodsTranslate)(nil),                        // 16: api.gold_store.v1.GoodsTranslate
	(*GoodsInfo)(nil),                             // 17: api.gold_store.v1.GoodsInfo
	(*AddGoodsReply)(nil),                         // 18: api.gold_store.v1.AddGoodsReply
	(*DeleteGoodsRequest)(nil),                    // 19: api.gold_store.v1.DeleteGoodsRequest
	(*GetGoodsInfoRequest)(nil),                   // 20: api.gold_store.v1.GetGoodsInfoRequest
	(*GetGoodsListRequest)(nil),                   // 21: api.gold_store.v1.GetGoodsListRequest
	(*GetGoodsListReply)(nil),                     // 22: api.gold_store.v1.GetGoodsListReply
	(*GoodsStatusRequest)(nil),                    // 23: api.gold_store.v1.GoodsStatusRequest
	(*GiftCardSettingPageListRequest)(nil),        // 24: api.gold_store.v1.GiftCardSettingPageListRequest
	(*GiftCardSettingPageListReply)(nil),          // 25: api.gold_store.v1.GiftCardSettingPageListReply
	(*GiftCardSettingPageListReplyItem)(nil),      // 26: api.gold_store.v1.GiftCardSettingPageListReplyItem
	(*EditGiftCardSettingRequest)(nil),            // 27: api.gold_store.v1.EditGiftCardSettingRequest
	(*EditGiftCardSettingReply)(nil),              // 28: api.gold_store.v1.EditGiftCardSettingReply
	(*DeleteGiftCardSettingRequest)(nil),          // 29: api.gold_store.v1.DeleteGiftCardSettingRequest
	(*DeleteGiftCardSettingReply)(nil),            // 30: api.gold_store.v1.DeleteGiftCardSettingReply
	(*GetSingleGiftCardSettingRequest)(nil),       // 31: api.gold_store.v1.GetSingleGiftCardSettingRequest
	(*GetSingleGiftCardSettingReply)(nil),         // 32: api.gold_store.v1.GetSingleGiftCardSettingReply
	(*GetSingleGiftCardSettingIsSendRequest)(nil), // 33: api.gold_store.v1.GetSingleGiftCardSettingIsSendRequest
	(*GetSingleGiftCardSettingIsSendReply)(nil),   // 34: api.gold_store.v1.GetSingleGiftCardSettingIsSendReply
	(*SendGiftCardRequest)(nil),                   // 35: api.gold_store.v1.SendGiftCardRequest
	(*SendGiftCardReply)(nil),                     // 36: api.gold_store.v1.SendGiftCardReply
	(*SendGiftCardRecordRequest)(nil),             // 37: api.gold_store.v1.SendGiftCardRecordRequest
	(*SendGiftCardRecordReply)(nil),               // 38: api.gold_store.v1.SendGiftCardRecordReply
	(*SendGiftCardRecordItem)(nil),                // 39: api.gold_store.v1.SendGiftCardRecordItem
	nil,                                           // 40: api.gold_store.v1.Label.ValuesEntry
	nil,                                           // 41: api.gold_store.v1.GoodsStaticSpecValue.TranslateEntry
	nil,                                           // 42: api.gold_store.v1.GoodsStaticSpec.TranslateEntry
	nil,                                           // 43: api.gold_store.v1.GoodsSpecTranslate.ValuesEntry
	nil,                                           // 44: api.gold_store.v1.GoodsTranslate.SpecsEntry
	nil,                                           // 45: api.gold_store.v1.GoodsInfo.TranslateEntry
	(PaymentMethod)(0),                            // 46: api.gold_store.v1.PaymentMethod
	(OrderStatus)(0),                              // 47: api.gold_store.v1.OrderStatus
	(*Address)(nil),                               // 48: api.gold_store.v1.Address
	(*LogisticStep)(nil),                          // 49: api.gold_store.v1.LogisticStep
	(GoodsCategory)(0),                            // 50: api.gold_store.v1.GoodsCategory
	(*Image)(nil),                                 // 51: api.gold_store.v1.Image
	(GoodsStatus)(0),                              // 52: api.gold_store.v1.GoodsStatus
	(*GoodsSpec)(nil),                             // 53: api.gold_store.v1.GoodsSpec
	(*GoodsSku)(nil),                              // 54: api.gold_store.v1.GoodsSku
	(*common.EmptyRequest)(nil),                   // 55: common.EmptyRequest
	(*GetTaskTypesRequest)(nil),                   // 56: api.gold_store.v1.GetTaskTypesRequest
	(*AdminListTaskRequest)(nil),                  // 57: api.gold_store.v1.AdminListTaskRequest
	(*AdminListTaskProgressRequest)(nil),          // 58: api.gold_store.v1.AdminListTaskProgressRequest
	(*AdminUpdateTaskStatusRequest)(nil),          // 59: api.gold_store.v1.AdminUpdateTaskStatusRequest
	(*AdminUpdateTaskConfigRequest)(nil),          // 60: api.gold_store.v1.AdminUpdateTaskConfigRequest
	(*AdminDeleteTaskRequest)(nil),                // 61: api.gold_store.v1.AdminDeleteTaskRequest
	(*AdminCreateTaskRequest)(nil),                // 62: api.gold_store.v1.AdminCreateTaskRequest
	(*AdminGetTaskDetailRequest)(nil),             // 63: api.gold_store.v1.AdminGetTaskDetailRequest
	(*ListSignConfigRequest)(nil),                 // 64: api.gold_store.v1.ListSignConfigRequest
	(*CreateSignConfigRequest)(nil),               // 65: api.gold_store.v1.CreateSignConfigRequest
	(*UpdateSignConfigRequest)(nil),               // 66: api.gold_store.v1.UpdateSignConfigRequest
	(*DeleteSignConfigRequest)(nil),               // 67: api.gold_store.v1.DeleteSignConfigRequest
	(*common.EmptyReply)(nil),                     // 68: common.EmptyReply
	(*GetTaskTypesResponse)(nil),                  // 69: api.gold_store.v1.GetTaskTypesResponse
	(*AdminListTaskReply)(nil),                    // 70: api.gold_store.v1.AdminListTaskReply
	(*AdminListTaskProgressReply)(nil),            // 71: api.gold_store.v1.AdminListTaskProgressReply
	(*AdminCreateTaskReply)(nil),                  // 72: api.gold_store.v1.AdminCreateTaskReply
	(*AdminGetTaskDetailReply)(nil),               // 73: api.gold_store.v1.AdminGetTaskDetailReply
	(*AdminListTaskGoodsReply)(nil),               // 74: api.gold_store.v1.AdminListTaskGoodsReply
	(*ListSignConfigReply)(nil),                   // 75: api.gold_store.v1.ListSignConfigReply
	(*CreateSignConfigReply)(nil),                 // 76: api.gold_store.v1.CreateSignConfigReply
	(*UpdateSignConfigReply)(nil),                 // 77: api.gold_store.v1.UpdateSignConfigReply
	(*DeleteSignConfigReply)(nil),                 // 78: api.gold_store.v1.DeleteSignConfigReply
}
var file_gold_store_v1_background_proto_depIdxs = []int32{
	46, // 0: api.gold_store.v1.AdminOrderListRequest.payment_method:type_name -> api.gold_store.v1.PaymentMethod
	47, // 1: api.gold_store.v1.AdminOrderListRequest.status:type_name -> api.gold_store.v1.OrderStatus
	48, // 2: api.gold_store.v1.AdminOrder.address:type_name -> api.gold_store.v1.Address
	46, // 3: api.gold_store.v1.AdminOrder.payment_method:type_name -> api.gold_store.v1.PaymentMethod
	47, // 4: api.gold_store.v1.AdminOrder.status:type_name -> api.gold_store.v1.OrderStatus
	3,  // 5: api.gold_store.v1.AdminOrderListReply.orders:type_name -> api.gold_store.v1.AdminOrder
	48, // 6: api.gold_store.v1.AdminOrderDetailReply.address:type_name -> api.gold_store.v1.Address
	46, // 7: api.gold_store.v1.AdminOrderDetailReply.payment_method:type_name -> api.gold_store.v1.PaymentMethod
	47, // 8: api.gold_store.v1.AdminOrderDetailReply.status:type_name -> api.gold_store.v1.OrderStatus
	49, // 9: api.gold_store.v1.AdminOrderDetailReply.steps:type_name -> api.gold_store.v1.LogisticStep
	0,  // 10: api.gold_store.v1.Label.category:type_name -> api.gold_store.v1.LabelCategory
	40, // 11: api.gold_store.v1.Label.values:type_name -> api.gold_store.v1.Label.ValuesEntry
	7,  // 12: api.gold_store.v1.LabelListReply.items:type_name -> api.gold_store.v1.Label
	50, // 13: api.gold_store.v1.GetStaticSpecRequest.category:type_name -> api.gold_store.v1.GoodsCategory
	41, // 14: api.gold_store.v1.GoodsStaticSpecValue.translate:type_name -> api.gold_store.v1.GoodsStaticSpecValue.TranslateEntry
	42, // 15: api.gold_store.v1.GoodsStaticSpec.translate:type_name -> api.gold_store.v1.GoodsStaticSpec.TranslateEntry
	10, // 16: api.gold_store.v1.GoodsStaticSpec.units:type_name -> api.gold_store.v1.GoodsStaticSpecUnit
	11, // 17: api.gold_store.v1.GoodsStaticSpec.values:type_name -> api.gold_store.v1.GoodsStaticSpecValue
	50, // 18: api.gold_store.v1.GoodsStaticSpecOfCategory.category:type_name -> api.gold_store.v1.GoodsCategory
	12, // 19: api.gold_store.v1.GoodsStaticSpecOfCategory.specs:type_name -> api.gold_store.v1.GoodsStaticSpec
	13, // 20: api.gold_store.v1.GetStaticSpecReply.categories:type_name -> api.gold_store.v1.GoodsStaticSpecOfCategory
	43, // 21: api.gold_store.v1.GoodsSpecTranslate.values:type_name -> api.gold_store.v1.GoodsSpecTranslate.ValuesEntry
	51, // 22: api.gold_store.v1.GoodsTranslate.details:type_name -> api.gold_store.v1.Image
	44, // 23: api.gold_store.v1.GoodsTranslate.specs:type_name -> api.gold_store.v1.GoodsTranslate.SpecsEntry
	50, // 24: api.gold_store.v1.GoodsInfo.category:type_name -> api.gold_store.v1.GoodsCategory
	51, // 25: api.gold_store.v1.GoodsInfo.image:type_name -> api.gold_store.v1.Image
	52, // 26: api.gold_store.v1.GoodsInfo.status:type_name -> api.gold_store.v1.GoodsStatus
	51, // 27: api.gold_store.v1.GoodsInfo.carousels:type_name -> api.gold_store.v1.Image
	51, // 28: api.gold_store.v1.GoodsInfo.details:type_name -> api.gold_store.v1.Image
	53, // 29: api.gold_store.v1.GoodsInfo.specs:type_name -> api.gold_store.v1.GoodsSpec
	54, // 30: api.gold_store.v1.GoodsInfo.skus:type_name -> api.gold_store.v1.GoodsSku
	45, // 31: api.gold_store.v1.GoodsInfo.translate:type_name -> api.gold_store.v1.GoodsInfo.TranslateEntry
	17, // 32: api.gold_store.v1.GetGoodsListReply.items:type_name -> api.gold_store.v1.GoodsInfo
	52, // 33: api.gold_store.v1.GoodsStatusRequest.status:type_name -> api.gold_store.v1.GoodsStatus
	26, // 34: api.gold_store.v1.GiftCardSettingPageListReply.items:type_name -> api.gold_store.v1.GiftCardSettingPageListReplyItem
	39, // 35: api.gold_store.v1.SendGiftCardRecordReply.items:type_name -> api.gold_store.v1.SendGiftCardRecordItem
	15, // 36: api.gold_store.v1.GoodsTranslate.SpecsEntry.value:type_name -> api.gold_store.v1.GoodsSpecTranslate
	16, // 37: api.gold_store.v1.GoodsInfo.TranslateEntry.value:type_name -> api.gold_store.v1.GoodsTranslate
	55, // 38: api.gold_store.v1.Background.LabelList:input_type -> common.EmptyRequest
	9,  // 39: api.gold_store.v1.Background.GetStaticSpec:input_type -> api.gold_store.v1.GetStaticSpecRequest
	17, // 40: api.gold_store.v1.Background.AddGoods:input_type -> api.gold_store.v1.GoodsInfo
	17, // 41: api.gold_store.v1.Background.UpdateGoods:input_type -> api.gold_store.v1.GoodsInfo
	20, // 42: api.gold_store.v1.Background.GetGoodsInfo:input_type -> api.gold_store.v1.GetGoodsInfoRequest
	19, // 43: api.gold_store.v1.Background.DeleteGoods:input_type -> api.gold_store.v1.DeleteGoodsRequest
	21, // 44: api.gold_store.v1.Background.GetGoodsList:input_type -> api.gold_store.v1.GetGoodsListRequest
	23, // 45: api.gold_store.v1.Background.SetGoodsStatus:input_type -> api.gold_store.v1.GoodsStatusRequest
	56, // 46: api.gold_store.v1.Background.GetTaskTypes:input_type -> api.gold_store.v1.GetTaskTypesRequest
	57, // 47: api.gold_store.v1.Background.ListTask:input_type -> api.gold_store.v1.AdminListTaskRequest
	58, // 48: api.gold_store.v1.Background.ListTaskProgress:input_type -> api.gold_store.v1.AdminListTaskProgressRequest
	59, // 49: api.gold_store.v1.Background.UpdateTaskStatus:input_type -> api.gold_store.v1.AdminUpdateTaskStatusRequest
	60, // 50: api.gold_store.v1.Background.UpdateTaskConfig:input_type -> api.gold_store.v1.AdminUpdateTaskConfigRequest
	61, // 51: api.gold_store.v1.Background.DeleteTask:input_type -> api.gold_store.v1.AdminDeleteTaskRequest
	62, // 52: api.gold_store.v1.Background.CreateTask:input_type -> api.gold_store.v1.AdminCreateTaskRequest
	63, // 53: api.gold_store.v1.Background.GetTaskDetail:input_type -> api.gold_store.v1.AdminGetTaskDetailRequest
	55, // 54: api.gold_store.v1.Background.ListTaskGoods:input_type -> common.EmptyRequest
	64, // 55: api.gold_store.v1.Background.ListSignConfig:input_type -> api.gold_store.v1.ListSignConfigRequest
	65, // 56: api.gold_store.v1.Background.CreateSignConfig:input_type -> api.gold_store.v1.CreateSignConfigRequest
	66, // 57: api.gold_store.v1.Background.UpdateSignConfig:input_type -> api.gold_store.v1.UpdateSignConfigRequest
	67, // 58: api.gold_store.v1.Background.DeleteSignConfig:input_type -> api.gold_store.v1.DeleteSignConfigRequest
	24, // 59: api.gold_store.v1.Background.GiftCardSettingPageList:input_type -> api.gold_store.v1.GiftCardSettingPageListRequest
	27, // 60: api.gold_store.v1.Background.EditGiftCardSetting:input_type -> api.gold_store.v1.EditGiftCardSettingRequest
	29, // 61: api.gold_store.v1.Background.DeleteGiftCardSetting:input_type -> api.gold_store.v1.DeleteGiftCardSettingRequest
	31, // 62: api.gold_store.v1.Background.GetSingleGiftCardSetting:input_type -> api.gold_store.v1.GetSingleGiftCardSettingRequest
	33, // 63: api.gold_store.v1.Background.GetSingleGiftCardSettingIsSend:input_type -> api.gold_store.v1.GetSingleGiftCardSettingIsSendRequest
	35, // 64: api.gold_store.v1.Background.SendGiftCard:input_type -> api.gold_store.v1.SendGiftCardRequest
	37, // 65: api.gold_store.v1.Background.SendGiftCardRecord:input_type -> api.gold_store.v1.SendGiftCardRecordRequest
	2,  // 66: api.gold_store.v1.Background.AdminOrderList:input_type -> api.gold_store.v1.AdminOrderListRequest
	5,  // 67: api.gold_store.v1.Background.AdminOrderDetail:input_type -> api.gold_store.v1.AdminOrderDetailRequest
	1,  // 68: api.gold_store.v1.Background.OrderDeliver:input_type -> api.gold_store.v1.OrderDeliverRequest
	55, // 69: api.gold_store.v1.Background.FetchReportOrders:input_type -> common.EmptyRequest
	8,  // 70: api.gold_store.v1.Background.LabelList:output_type -> api.gold_store.v1.LabelListReply
	14, // 71: api.gold_store.v1.Background.GetStaticSpec:output_type -> api.gold_store.v1.GetStaticSpecReply
	18, // 72: api.gold_store.v1.Background.AddGoods:output_type -> api.gold_store.v1.AddGoodsReply
	68, // 73: api.gold_store.v1.Background.UpdateGoods:output_type -> common.EmptyReply
	17, // 74: api.gold_store.v1.Background.GetGoodsInfo:output_type -> api.gold_store.v1.GoodsInfo
	68, // 75: api.gold_store.v1.Background.DeleteGoods:output_type -> common.EmptyReply
	22, // 76: api.gold_store.v1.Background.GetGoodsList:output_type -> api.gold_store.v1.GetGoodsListReply
	68, // 77: api.gold_store.v1.Background.SetGoodsStatus:output_type -> common.EmptyReply
	69, // 78: api.gold_store.v1.Background.GetTaskTypes:output_type -> api.gold_store.v1.GetTaskTypesResponse
	70, // 79: api.gold_store.v1.Background.ListTask:output_type -> api.gold_store.v1.AdminListTaskReply
	71, // 80: api.gold_store.v1.Background.ListTaskProgress:output_type -> api.gold_store.v1.AdminListTaskProgressReply
	68, // 81: api.gold_store.v1.Background.UpdateTaskStatus:output_type -> common.EmptyReply
	68, // 82: api.gold_store.v1.Background.UpdateTaskConfig:output_type -> common.EmptyReply
	68, // 83: api.gold_store.v1.Background.DeleteTask:output_type -> common.EmptyReply
	72, // 84: api.gold_store.v1.Background.CreateTask:output_type -> api.gold_store.v1.AdminCreateTaskReply
	73, // 85: api.gold_store.v1.Background.GetTaskDetail:output_type -> api.gold_store.v1.AdminGetTaskDetailReply
	74, // 86: api.gold_store.v1.Background.ListTaskGoods:output_type -> api.gold_store.v1.AdminListTaskGoodsReply
	75, // 87: api.gold_store.v1.Background.ListSignConfig:output_type -> api.gold_store.v1.ListSignConfigReply
	76, // 88: api.gold_store.v1.Background.CreateSignConfig:output_type -> api.gold_store.v1.CreateSignConfigReply
	77, // 89: api.gold_store.v1.Background.UpdateSignConfig:output_type -> api.gold_store.v1.UpdateSignConfigReply
	78, // 90: api.gold_store.v1.Background.DeleteSignConfig:output_type -> api.gold_store.v1.DeleteSignConfigReply
	25, // 91: api.gold_store.v1.Background.GiftCardSettingPageList:output_type -> api.gold_store.v1.GiftCardSettingPageListReply
	28, // 92: api.gold_store.v1.Background.EditGiftCardSetting:output_type -> api.gold_store.v1.EditGiftCardSettingReply
	30, // 93: api.gold_store.v1.Background.DeleteGiftCardSetting:output_type -> api.gold_store.v1.DeleteGiftCardSettingReply
	32, // 94: api.gold_store.v1.Background.GetSingleGiftCardSetting:output_type -> api.gold_store.v1.GetSingleGiftCardSettingReply
	34, // 95: api.gold_store.v1.Background.GetSingleGiftCardSettingIsSend:output_type -> api.gold_store.v1.GetSingleGiftCardSettingIsSendReply
	36, // 96: api.gold_store.v1.Background.SendGiftCard:output_type -> api.gold_store.v1.SendGiftCardReply
	38, // 97: api.gold_store.v1.Background.SendGiftCardRecord:output_type -> api.gold_store.v1.SendGiftCardRecordReply
	4,  // 98: api.gold_store.v1.Background.AdminOrderList:output_type -> api.gold_store.v1.AdminOrderListReply
	6,  // 99: api.gold_store.v1.Background.AdminOrderDetail:output_type -> api.gold_store.v1.AdminOrderDetailReply
	68, // 100: api.gold_store.v1.Background.OrderDeliver:output_type -> common.EmptyReply
	68, // 101: api.gold_store.v1.Background.FetchReportOrders:output_type -> common.EmptyReply
	70, // [70:102] is the sub-list for method output_type
	38, // [38:70] is the sub-list for method input_type
	38, // [38:38] is the sub-list for extension type_name
	38, // [38:38] is the sub-list for extension extendee
	0,  // [0:38] is the sub-list for field type_name
}

func init() { file_gold_store_v1_background_proto_init() }
func file_gold_store_v1_background_proto_init() {
	if File_gold_store_v1_background_proto != nil {
		return
	}
	file_gold_store_v1_models_proto_init()
	file_gold_store_v1_task_proto_init()
	file_gold_store_v1_sign_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_gold_store_v1_background_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OrderDeliverRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_background_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AdminOrderListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_background_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AdminOrder); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_background_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AdminOrderListReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_background_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AdminOrderDetailRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_background_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AdminOrderDetailReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_background_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Label); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_background_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LabelListReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_background_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetStaticSpecRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_background_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GoodsStaticSpecUnit); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_background_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GoodsStaticSpecValue); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_background_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GoodsStaticSpec); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_background_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GoodsStaticSpecOfCategory); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_background_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetStaticSpecReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_background_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GoodsSpecTranslate); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_background_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GoodsTranslate); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_background_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GoodsInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_background_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddGoodsReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_background_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteGoodsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_background_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetGoodsInfoRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_background_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetGoodsListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_background_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetGoodsListReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_background_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GoodsStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_background_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GiftCardSettingPageListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_background_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GiftCardSettingPageListReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_background_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GiftCardSettingPageListReplyItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_background_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EditGiftCardSettingRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_background_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EditGiftCardSettingReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_background_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteGiftCardSettingRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_background_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteGiftCardSettingReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_background_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSingleGiftCardSettingRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_background_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSingleGiftCardSettingReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_background_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSingleGiftCardSettingIsSendRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_background_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSingleGiftCardSettingIsSendReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_background_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SendGiftCardRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_background_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SendGiftCardReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_background_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SendGiftCardRecordRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_background_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SendGiftCardRecordReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_background_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SendGiftCardRecordItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_gold_store_v1_background_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   45,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_gold_store_v1_background_proto_goTypes,
		DependencyIndexes: file_gold_store_v1_background_proto_depIdxs,
		EnumInfos:         file_gold_store_v1_background_proto_enumTypes,
		MessageInfos:      file_gold_store_v1_background_proto_msgTypes,
	}.Build()
	File_gold_store_v1_background_proto = out.File
	file_gold_store_v1_background_proto_rawDesc = nil
	file_gold_store_v1_background_proto_goTypes = nil
	file_gold_store_v1_background_proto_depIdxs = nil
}
