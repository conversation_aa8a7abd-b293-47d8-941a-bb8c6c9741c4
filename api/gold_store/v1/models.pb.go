// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.25.3
// source: gold_store/v1/models.proto

package v1

import (
	_ "api-community/api/common"
	_ "github.com/grpc-ecosystem/grpc-gateway/v2/protoc-gen-openapiv2/options"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type PaymentMethod int32

const (
	PaymentMethod_GOLD                   PaymentMethod = 0  // 金币
	PaymentMethod_GIFT_CARD              PaymentMethod = 1  // 礼品卡
	PaymentMethod_TASK                   PaymentMethod = 2  // 用户任务
	PaymentMethod_ALI                    PaymentMethod = 3  // 支付宝
	PaymentMethod_WECHAT                 PaymentMethod = 4  // 微信
	PaymentMethod_APPLE_IAP              PaymentMethod = 5  // 苹果内购
	PaymentMethod_GOOGLE_IAP             PaymentMethod = 6  // google 内购
	PaymentMethod_UNION_PAY              PaymentMethod = 7  // 银联
	PaymentMethod_APPLE_PAY              PaymentMethod = 8  // 苹果支付
	PaymentMethod_GOOGLE_PAY             PaymentMethod = 9  // google支付
	PaymentMethod_PAYPAL                 PaymentMethod = 10 // Paypal
	PaymentMethod_WECHAT_H5              PaymentMethod = 11 // 微信H5
	PaymentMethod_ALI_WAP                PaymentMethod = 12 // 支付宝WAP
	PaymentMethod_FOREX_PAY              PaymentMethod = 13 // ForexPay
	PaymentMethod_POINTS                 PaymentMethod = 14 // 积分
	PaymentMethod_PAYMENT_METHOD_UNKNOWN PaymentMethod = 99 // 未知
)

// Enum value maps for PaymentMethod.
var (
	PaymentMethod_name = map[int32]string{
		0:  "GOLD",
		1:  "GIFT_CARD",
		2:  "TASK",
		3:  "ALI",
		4:  "WECHAT",
		5:  "APPLE_IAP",
		6:  "GOOGLE_IAP",
		7:  "UNION_PAY",
		8:  "APPLE_PAY",
		9:  "GOOGLE_PAY",
		10: "PAYPAL",
		11: "WECHAT_H5",
		12: "ALI_WAP",
		13: "FOREX_PAY",
		14: "POINTS",
		99: "PAYMENT_METHOD_UNKNOWN",
	}
	PaymentMethod_value = map[string]int32{
		"GOLD":                   0,
		"GIFT_CARD":              1,
		"TASK":                   2,
		"ALI":                    3,
		"WECHAT":                 4,
		"APPLE_IAP":              5,
		"GOOGLE_IAP":             6,
		"UNION_PAY":              7,
		"APPLE_PAY":              8,
		"GOOGLE_PAY":             9,
		"PAYPAL":                 10,
		"WECHAT_H5":              11,
		"ALI_WAP":                12,
		"FOREX_PAY":              13,
		"POINTS":                 14,
		"PAYMENT_METHOD_UNKNOWN": 99,
	}
)

func (x PaymentMethod) Enum() *PaymentMethod {
	p := new(PaymentMethod)
	*p = x
	return p
}

func (x PaymentMethod) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PaymentMethod) Descriptor() protoreflect.EnumDescriptor {
	return file_gold_store_v1_models_proto_enumTypes[0].Descriptor()
}

func (PaymentMethod) Type() protoreflect.EnumType {
	return &file_gold_store_v1_models_proto_enumTypes[0]
}

func (x PaymentMethod) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PaymentMethod.Descriptor instead.
func (PaymentMethod) EnumDescriptor() ([]byte, []int) {
	return file_gold_store_v1_models_proto_rawDescGZIP(), []int{0}
}

type OrderStatus int32

const (
	OrderStatus_UNKNOWN  OrderStatus = 0
	OrderStatus_UNPAY    OrderStatus = 1 // 待支付
	OrderStatus_PAID     OrderStatus = 2 // 已支付
	OrderStatus_CANCEL   OrderStatus = 3 // 已取消
	OrderStatus_DELIVERY OrderStatus = 4 // 待收货
	OrderStatus_COMPLETE OrderStatus = 5 // 已完成
)

// Enum value maps for OrderStatus.
var (
	OrderStatus_name = map[int32]string{
		0: "UNKNOWN",
		1: "UNPAY",
		2: "PAID",
		3: "CANCEL",
		4: "DELIVERY",
		5: "COMPLETE",
	}
	OrderStatus_value = map[string]int32{
		"UNKNOWN":  0,
		"UNPAY":    1,
		"PAID":     2,
		"CANCEL":   3,
		"DELIVERY": 4,
		"COMPLETE": 5,
	}
)

func (x OrderStatus) Enum() *OrderStatus {
	p := new(OrderStatus)
	*p = x
	return p
}

func (x OrderStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (OrderStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_gold_store_v1_models_proto_enumTypes[1].Descriptor()
}

func (OrderStatus) Type() protoreflect.EnumType {
	return &file_gold_store_v1_models_proto_enumTypes[1]
}

func (x OrderStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use OrderStatus.Descriptor instead.
func (OrderStatus) EnumDescriptor() ([]byte, []int) {
	return file_gold_store_v1_models_proto_rawDescGZIP(), []int{1}
}

type PaymentStatus int32

const (
	PaymentStatus_PaymentStatusUNPAY    PaymentStatus = 0 // 未支付
	PaymentStatus_PaymentStatusPAID     PaymentStatus = 1 // 已支付
	PaymentStatus_PaymentStatusCANCELED PaymentStatus = 2 // 已取消
	PaymentStatus_PaymentStatusTOREFUND PaymentStatus = 3 // 待退款
	PaymentStatus_PaymentStatusREFUNDED PaymentStatus = 4 // 已退款
)

// Enum value maps for PaymentStatus.
var (
	PaymentStatus_name = map[int32]string{
		0: "PaymentStatusUNPAY",
		1: "PaymentStatusPAID",
		2: "PaymentStatusCANCELED",
		3: "PaymentStatusTOREFUND",
		4: "PaymentStatusREFUNDED",
	}
	PaymentStatus_value = map[string]int32{
		"PaymentStatusUNPAY":    0,
		"PaymentStatusPAID":     1,
		"PaymentStatusCANCELED": 2,
		"PaymentStatusTOREFUND": 3,
		"PaymentStatusREFUNDED": 4,
	}
)

func (x PaymentStatus) Enum() *PaymentStatus {
	p := new(PaymentStatus)
	*p = x
	return p
}

func (x PaymentStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PaymentStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_gold_store_v1_models_proto_enumTypes[2].Descriptor()
}

func (PaymentStatus) Type() protoreflect.EnumType {
	return &file_gold_store_v1_models_proto_enumTypes[2]
}

func (x PaymentStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PaymentStatus.Descriptor instead.
func (PaymentStatus) EnumDescriptor() ([]byte, []int) {
	return file_gold_store_v1_models_proto_rawDescGZIP(), []int{2}
}

type Platform int32

const (
	Platform_IOS             Platform = 0   // 苹果
	Platform_ANDROID         Platform = 1   // 安卓
	Platform_PC              Platform = 2   // pc
	Platform_WEB             Platform = 3   // web
	Platform_PlatformUNKNOWN Platform = 999 // 未知
)

// Enum value maps for Platform.
var (
	Platform_name = map[int32]string{
		0:   "IOS",
		1:   "ANDROID",
		2:   "PC",
		3:   "WEB",
		999: "PlatformUNKNOWN",
	}
	Platform_value = map[string]int32{
		"IOS":             0,
		"ANDROID":         1,
		"PC":              2,
		"WEB":             3,
		"PlatformUNKNOWN": 999,
	}
)

func (x Platform) Enum() *Platform {
	p := new(Platform)
	*p = x
	return p
}

func (x Platform) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Platform) Descriptor() protoreflect.EnumDescriptor {
	return file_gold_store_v1_models_proto_enumTypes[3].Descriptor()
}

func (Platform) Type() protoreflect.EnumType {
	return &file_gold_store_v1_models_proto_enumTypes[3]
}

func (x Platform) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Platform.Descriptor instead.
func (Platform) EnumDescriptor() ([]byte, []int) {
	return file_gold_store_v1_models_proto_rawDescGZIP(), []int{3}
}

type GoodsCategory int32

const (
	GoodsCategory_GOODS_CATEGORY_PHYSICAL   GoodsCategory = 0 // 实物商品
	GoodsCategory_GOODS_CATEGORY_VIP        GoodsCategory = 1 // VIP
	GoodsCategory_GOODS_CATEGORY_VPS        GoodsCategory = 2 // VPS
	GoodsCategory_GOODS_CATEGORY_REPORT     GoodsCategory = 3 // 报告
	GoodsCategory_GOODS_CATEGORY_EXHIBITION GoodsCategory = 4 // 展会
	GoodsCategory_GOODS_CATEGORY_EA         GoodsCategory = 5 // EA
)

// Enum value maps for GoodsCategory.
var (
	GoodsCategory_name = map[int32]string{
		0: "GOODS_CATEGORY_PHYSICAL",
		1: "GOODS_CATEGORY_VIP",
		2: "GOODS_CATEGORY_VPS",
		3: "GOODS_CATEGORY_REPORT",
		4: "GOODS_CATEGORY_EXHIBITION",
		5: "GOODS_CATEGORY_EA",
	}
	GoodsCategory_value = map[string]int32{
		"GOODS_CATEGORY_PHYSICAL":   0,
		"GOODS_CATEGORY_VIP":        1,
		"GOODS_CATEGORY_VPS":        2,
		"GOODS_CATEGORY_REPORT":     3,
		"GOODS_CATEGORY_EXHIBITION": 4,
		"GOODS_CATEGORY_EA":         5,
	}
)

func (x GoodsCategory) Enum() *GoodsCategory {
	p := new(GoodsCategory)
	*p = x
	return p
}

func (x GoodsCategory) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GoodsCategory) Descriptor() protoreflect.EnumDescriptor {
	return file_gold_store_v1_models_proto_enumTypes[4].Descriptor()
}

func (GoodsCategory) Type() protoreflect.EnumType {
	return &file_gold_store_v1_models_proto_enumTypes[4]
}

func (x GoodsCategory) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GoodsCategory.Descriptor instead.
func (GoodsCategory) EnumDescriptor() ([]byte, []int) {
	return file_gold_store_v1_models_proto_rawDescGZIP(), []int{4}
}

type GoodsStatus int32

const (
	GoodsStatus_GoodsStatusOff GoodsStatus = 0 // 下架
	GoodsStatus_GoodsStatusOn  GoodsStatus = 1 // 上架
)

// Enum value maps for GoodsStatus.
var (
	GoodsStatus_name = map[int32]string{
		0: "GoodsStatusOff",
		1: "GoodsStatusOn",
	}
	GoodsStatus_value = map[string]int32{
		"GoodsStatusOff": 0,
		"GoodsStatusOn":  1,
	}
)

func (x GoodsStatus) Enum() *GoodsStatus {
	p := new(GoodsStatus)
	*p = x
	return p
}

func (x GoodsStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GoodsStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_gold_store_v1_models_proto_enumTypes[5].Descriptor()
}

func (GoodsStatus) Type() protoreflect.EnumType {
	return &file_gold_store_v1_models_proto_enumTypes[5]
}

func (x GoodsStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GoodsStatus.Descriptor instead.
func (GoodsStatus) EnumDescriptor() ([]byte, []int) {
	return file_gold_store_v1_models_proto_rawDescGZIP(), []int{5}
}

type OrderSource int32

const (
	OrderSource_STORE      OrderSource = 0 // 金币商城
	OrderSource_VIP        OrderSource = 1 // vip
	OrderSource_VPS        OrderSource = 2 // VPS
	OrderSource_REPORT     OrderSource = 3 // 报告
	OrderSource_EXHIBITION OrderSource = 4 // 展会
	OrderSource_EA         OrderSource = 5 // ea商城
)

// Enum value maps for OrderSource.
var (
	OrderSource_name = map[int32]string{
		0: "STORE",
		1: "VIP",
		2: "VPS",
		3: "REPORT",
		4: "EXHIBITION",
		5: "EA",
	}
	OrderSource_value = map[string]int32{
		"STORE":      0,
		"VIP":        1,
		"VPS":        2,
		"REPORT":     3,
		"EXHIBITION": 4,
		"EA":         5,
	}
)

func (x OrderSource) Enum() *OrderSource {
	p := new(OrderSource)
	*p = x
	return p
}

func (x OrderSource) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (OrderSource) Descriptor() protoreflect.EnumDescriptor {
	return file_gold_store_v1_models_proto_enumTypes[6].Descriptor()
}

func (OrderSource) Type() protoreflect.EnumType {
	return &file_gold_store_v1_models_proto_enumTypes[6]
}

func (x OrderSource) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use OrderSource.Descriptor instead.
func (OrderSource) EnumDescriptor() ([]byte, []int) {
	return file_gold_store_v1_models_proto_rawDescGZIP(), []int{6}
}

type LogisticStepStatus int32

const (
	LogisticStepStatus_LogisticStepStatusORDERED    LogisticStepStatus = 0 // 已下单
	LogisticStepStatus_LogisticStepStatusPROCESSING LogisticStepStatus = 1 // 仓库处理中
	LogisticStepStatus_LogisticStepStatusSHIPPED    LogisticStepStatus = 2 // 已发货
	LogisticStepStatus_LogisticStepStatusPICKED     LogisticStepStatus = 3 // 已揽件
	LogisticStepStatus_LogisticStepStatusTRANSIT    LogisticStepStatus = 4 // 运输中
	LogisticStepStatus_LogisticStepStatusDELIVERY   LogisticStepStatus = 5 // 派送中
	LogisticStepStatus_LogisticStepStatusSIGNED     LogisticStepStatus = 6 // 已签收
)

// Enum value maps for LogisticStepStatus.
var (
	LogisticStepStatus_name = map[int32]string{
		0: "LogisticStepStatusORDERED",
		1: "LogisticStepStatusPROCESSING",
		2: "LogisticStepStatusSHIPPED",
		3: "LogisticStepStatusPICKED",
		4: "LogisticStepStatusTRANSIT",
		5: "LogisticStepStatusDELIVERY",
		6: "LogisticStepStatusSIGNED",
	}
	LogisticStepStatus_value = map[string]int32{
		"LogisticStepStatusORDERED":    0,
		"LogisticStepStatusPROCESSING": 1,
		"LogisticStepStatusSHIPPED":    2,
		"LogisticStepStatusPICKED":     3,
		"LogisticStepStatusTRANSIT":    4,
		"LogisticStepStatusDELIVERY":   5,
		"LogisticStepStatusSIGNED":     6,
	}
)

func (x LogisticStepStatus) Enum() *LogisticStepStatus {
	p := new(LogisticStepStatus)
	*p = x
	return p
}

func (x LogisticStepStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LogisticStepStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_gold_store_v1_models_proto_enumTypes[7].Descriptor()
}

func (LogisticStepStatus) Type() protoreflect.EnumType {
	return &file_gold_store_v1_models_proto_enumTypes[7]
}

func (x LogisticStepStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LogisticStepStatus.Descriptor instead.
func (LogisticStepStatus) EnumDescriptor() ([]byte, []int) {
	return file_gold_store_v1_models_proto_rawDescGZIP(), []int{7}
}

type LogisticStep struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StepName string             `protobuf:"bytes,1,opt,name=step_name,json=stepName,proto3" json:"step_name"`
	StepDesc string             `protobuf:"bytes,2,opt,name=step_desc,json=stepDesc,proto3" json:"step_desc"`
	StepTime int64              `protobuf:"varint,3,opt,name=step_time,json=stepTime,proto3" json:"step_time"`
	Status   LogisticStepStatus `protobuf:"varint,4,opt,name=status,json=status,proto3,enum=api.gold_store.v1.LogisticStepStatus" json:"status"`
}

func (x *LogisticStep) Reset() {
	*x = LogisticStep{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LogisticStep) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LogisticStep) ProtoMessage() {}

func (x *LogisticStep) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LogisticStep.ProtoReflect.Descriptor instead.
func (*LogisticStep) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_models_proto_rawDescGZIP(), []int{0}
}

func (x *LogisticStep) GetStepName() string {
	if x != nil {
		return x.StepName
	}
	return ""
}

func (x *LogisticStep) GetStepDesc() string {
	if x != nil {
		return x.StepDesc
	}
	return ""
}

func (x *LogisticStep) GetStepTime() int64 {
	if x != nil {
		return x.StepTime
	}
	return 0
}

func (x *LogisticStep) GetStatus() LogisticStepStatus {
	if x != nil {
		return x.Status
	}
	return LogisticStepStatus_LogisticStepStatusORDERED
}

type Address struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id                  int32  `protobuf:"varint,1,opt,name=id,json=id,proto3" json:"id"`
	CountryCodeIso      string `protobuf:"bytes,2,opt,name=country_code_iso,json=countryCodeIso,proto3" json:"country_code_iso"`
	UserName            string `protobuf:"bytes,3,opt,name=user_name,json=userName,proto3" json:"user_name"`
	PhoneCountryCode    string `protobuf:"bytes,4,opt,name=phone_country_code,json=phoneCountryCode,proto3" json:"phone_country_code"`
	PhoneAreaCode       string `protobuf:"bytes,5,opt,name=phone_area_code,json=phoneAreaCode,proto3" json:"phone_area_code"`
	Phone               string `protobuf:"bytes,6,opt,name=phone,json=phone,proto3" json:"phone"`
	ProvinceName        string `protobuf:"bytes,7,opt,name=province_name,json=provinceName,proto3" json:"province_name"`
	CityName            string `protobuf:"bytes,8,opt,name=city_name,json=cityName,proto3" json:"city_name"`
	StreetAddress       string `protobuf:"bytes,9,opt,name=street_address,json=streetAddress,proto3" json:"street_address"`
	BuildingUnitAddress string `protobuf:"bytes,10,opt,name=building_unit_address,json=buildingUnitAddress,proto3" json:"building_unit_address"`
	PostalCode          string `protobuf:"bytes,11,opt,name=postal_code,json=postalCode,proto3" json:"postal_code"`
	IsDefault           bool   `protobuf:"varint,12,opt,name=is_default,json=isDefault,proto3" json:"is_default"`
	AddressShow         string `protobuf:"bytes,13,opt,name=address_show,json=addressShow,proto3" json:"address_show"`
	CountryName         string `protobuf:"bytes,14,opt,name=country_name,json=countryName,proto3" json:"country_name"`
}

func (x *Address) Reset() {
	*x = Address{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_models_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Address) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Address) ProtoMessage() {}

func (x *Address) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_models_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Address.ProtoReflect.Descriptor instead.
func (*Address) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_models_proto_rawDescGZIP(), []int{1}
}

func (x *Address) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Address) GetCountryCodeIso() string {
	if x != nil {
		return x.CountryCodeIso
	}
	return ""
}

func (x *Address) GetUserName() string {
	if x != nil {
		return x.UserName
	}
	return ""
}

func (x *Address) GetPhoneCountryCode() string {
	if x != nil {
		return x.PhoneCountryCode
	}
	return ""
}

func (x *Address) GetPhoneAreaCode() string {
	if x != nil {
		return x.PhoneAreaCode
	}
	return ""
}

func (x *Address) GetPhone() string {
	if x != nil {
		return x.Phone
	}
	return ""
}

func (x *Address) GetProvinceName() string {
	if x != nil {
		return x.ProvinceName
	}
	return ""
}

func (x *Address) GetCityName() string {
	if x != nil {
		return x.CityName
	}
	return ""
}

func (x *Address) GetStreetAddress() string {
	if x != nil {
		return x.StreetAddress
	}
	return ""
}

func (x *Address) GetBuildingUnitAddress() string {
	if x != nil {
		return x.BuildingUnitAddress
	}
	return ""
}

func (x *Address) GetPostalCode() string {
	if x != nil {
		return x.PostalCode
	}
	return ""
}

func (x *Address) GetIsDefault() bool {
	if x != nil {
		return x.IsDefault
	}
	return false
}

func (x *Address) GetAddressShow() string {
	if x != nil {
		return x.AddressShow
	}
	return ""
}

func (x *Address) GetCountryName() string {
	if x != nil {
		return x.CountryName
	}
	return ""
}

type Image struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Url    string `protobuf:"bytes,1,opt,name=url,json=url,proto3" json:"url"`
	Width  int32  `protobuf:"varint,2,opt,name=width,json=width,proto3" json:"width"`
	Height int32  `protobuf:"varint,3,opt,name=height,json=height,proto3" json:"height"`
}

func (x *Image) Reset() {
	*x = Image{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_models_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Image) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Image) ProtoMessage() {}

func (x *Image) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_models_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Image.ProtoReflect.Descriptor instead.
func (*Image) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_models_proto_rawDescGZIP(), []int{2}
}

func (x *Image) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *Image) GetWidth() int32 {
	if x != nil {
		return x.Width
	}
	return 0
}

func (x *Image) GetHeight() int32 {
	if x != nil {
		return x.Height
	}
	return 0
}

type GoodsSpecValue struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id       string `protobuf:"bytes,1,opt,name=id,json=id,proto3" json:"id"`
	Name     string `protobuf:"bytes,2,opt,name=name,json=name,proto3" json:"name"`
	Selected bool   `protobuf:"varint,3,opt,name=selected,json=selected,proto3" json:"selected"`
	Image    *Image `protobuf:"bytes,4,opt,name=image,json=image,proto3" json:"image"`
	SpecId   string `protobuf:"bytes,5,opt,name=spec_id,json=specId,proto3" json:"spec_id"`
	UnitId   string `protobuf:"bytes,6,opt,name=unit_id,json=unitId,proto3" json:"unit_id"`
}

func (x *GoodsSpecValue) Reset() {
	*x = GoodsSpecValue{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_models_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GoodsSpecValue) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GoodsSpecValue) ProtoMessage() {}

func (x *GoodsSpecValue) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_models_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GoodsSpecValue.ProtoReflect.Descriptor instead.
func (*GoodsSpecValue) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_models_proto_rawDescGZIP(), []int{3}
}

func (x *GoodsSpecValue) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *GoodsSpecValue) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GoodsSpecValue) GetSelected() bool {
	if x != nil {
		return x.Selected
	}
	return false
}

func (x *GoodsSpecValue) GetImage() *Image {
	if x != nil {
		return x.Image
	}
	return nil
}

func (x *GoodsSpecValue) GetSpecId() string {
	if x != nil {
		return x.SpecId
	}
	return ""
}

func (x *GoodsSpecValue) GetUnitId() string {
	if x != nil {
		return x.UnitId
	}
	return ""
}

type GoodsSpec struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id     string            `protobuf:"bytes,1,opt,name=id,json=id,proto3" json:"id"`
	Name   string            `protobuf:"bytes,2,opt,name=name,json=name,proto3" json:"name"`
	Values []*GoodsSpecValue `protobuf:"bytes,3,rep,name=values,json=values,proto3" json:"values"`
}

func (x *GoodsSpec) Reset() {
	*x = GoodsSpec{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_models_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GoodsSpec) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GoodsSpec) ProtoMessage() {}

func (x *GoodsSpec) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_models_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GoodsSpec.ProtoReflect.Descriptor instead.
func (*GoodsSpec) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_models_proto_rawDescGZIP(), []int{4}
}

func (x *GoodsSpec) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *GoodsSpec) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GoodsSpec) GetValues() []*GoodsSpecValue {
	if x != nil {
		return x.Values
	}
	return nil
}

type GoodsSkuSpec struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SpecId  string `protobuf:"bytes,1,opt,name=spec_id,json=specId,proto3" json:"spec_id"`
	ValueId string `protobuf:"bytes,2,opt,name=value_id,json=valueId,proto3" json:"value_id"`
	UnitId  string `protobuf:"bytes,3,opt,name=unit_id,json=unitId,proto3" json:"unit_id"`
}

func (x *GoodsSkuSpec) Reset() {
	*x = GoodsSkuSpec{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_models_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GoodsSkuSpec) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GoodsSkuSpec) ProtoMessage() {}

func (x *GoodsSkuSpec) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_models_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GoodsSkuSpec.ProtoReflect.Descriptor instead.
func (*GoodsSkuSpec) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_models_proto_rawDescGZIP(), []int{5}
}

func (x *GoodsSkuSpec) GetSpecId() string {
	if x != nil {
		return x.SpecId
	}
	return ""
}

func (x *GoodsSkuSpec) GetValueId() string {
	if x != nil {
		return x.ValueId
	}
	return ""
}

func (x *GoodsSkuSpec) GetUnitId() string {
	if x != nil {
		return x.UnitId
	}
	return ""
}

type GoodsSku struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SkuId   string          `protobuf:"bytes,1,opt,name=sku_id,json=skuId,proto3" json:"sku_id"`
	Specs   []*GoodsSkuSpec `protobuf:"bytes,2,rep,name=specs,json=specs,proto3" json:"specs"`
	Price   float32         `protobuf:"fixed32,3,opt,name=price,json=price,proto3" json:"price"`
	Disable bool            `protobuf:"varint,4,opt,name=disable,json=disable,proto3" json:"disable"`
	Stock   int32           `protobuf:"varint,5,opt,name=stock,json=stock,proto3" json:"stock"`
}

func (x *GoodsSku) Reset() {
	*x = GoodsSku{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_models_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GoodsSku) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GoodsSku) ProtoMessage() {}

func (x *GoodsSku) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_models_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GoodsSku.ProtoReflect.Descriptor instead.
func (*GoodsSku) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_models_proto_rawDescGZIP(), []int{6}
}

func (x *GoodsSku) GetSkuId() string {
	if x != nil {
		return x.SkuId
	}
	return ""
}

func (x *GoodsSku) GetSpecs() []*GoodsSkuSpec {
	if x != nil {
		return x.Specs
	}
	return nil
}

func (x *GoodsSku) GetPrice() float32 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *GoodsSku) GetDisable() bool {
	if x != nil {
		return x.Disable
	}
	return false
}

func (x *GoodsSku) GetStock() int32 {
	if x != nil {
		return x.Stock
	}
	return 0
}

var File_gold_store_v1_models_proto protoreflect.FileDescriptor

var file_gold_store_v1_models_proto_rawDesc = []byte{
	0x0a, 0x1a, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2f, 0x76, 0x31, 0x2f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x11, 0x61, 0x70,
	0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x1a,
	0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f,
	0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x63, 0x2d, 0x67, 0x65, 0x6e, 0x2d, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70,
	0x69, 0x76, 0x32, 0x2f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x61, 0x6e, 0x6e, 0x6f,
	0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x13, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x22, 0xf0, 0x01, 0x0a, 0x0c, 0x4c, 0x6f, 0x67, 0x69, 0x73, 0x74, 0x69, 0x63, 0x53,
	0x74, 0x65, 0x70, 0x12, 0x2e, 0x0a, 0x09, 0x73, 0x74, 0x65, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe6, 0xad, 0xa5,
	0xe9, 0xaa, 0xa4, 0xe5, 0x90, 0x8d, 0xe7, 0xa7, 0xb0, 0x52, 0x08, 0x73, 0x74, 0x65, 0x70, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x2e, 0x0a, 0x09, 0x73, 0x74, 0x65, 0x70, 0x5f, 0x64, 0x65, 0x73, 0x63,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe6, 0xad, 0xa5,
	0xe9, 0xaa, 0xa4, 0xe6, 0x8f, 0x8f, 0xe8, 0xbf, 0xb0, 0x52, 0x08, 0x73, 0x74, 0x65, 0x70, 0x44,
	0x65, 0x73, 0x63, 0x12, 0x2e, 0x0a, 0x09, 0x73, 0x74, 0x65, 0x70, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe6, 0xad, 0xa5,
	0xe9, 0xaa, 0xa4, 0xe6, 0x97, 0xb6, 0xe9, 0x97, 0xb4, 0x52, 0x08, 0x73, 0x74, 0x65, 0x70, 0x54,
	0x69, 0x6d, 0x65, 0x12, 0x50, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73,
	0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x67, 0x69, 0x73, 0x74, 0x69, 0x63,
	0x53, 0x74, 0x65, 0x70, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a,
	0x0c, 0xe7, 0x89, 0xa9, 0xe6, 0xb5, 0x81, 0xe7, 0x8a, 0xb6, 0xe6, 0x80, 0x81, 0x52, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0xef, 0x06, 0x0a, 0x07, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x12, 0x35, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x42, 0x25, 0x92,
	0x41, 0x22, 0x2a, 0x20, 0xe5, 0x9c, 0xb0, 0xe5, 0x9d, 0x80, 0x49, 0x44, 0xef, 0xbc, 0x8c, 0xe6,
	0x96, 0xb0, 0xe5, 0xbb, 0xba, 0xe6, 0x97, 0xb6, 0xe4, 0xb8, 0x8d, 0xe7, 0x94, 0xa8, 0xe4, 0xbc,
	0xa0, 0xe9, 0x80, 0x92, 0x52, 0x02, 0x69, 0x64, 0x12, 0x76, 0x0a, 0x10, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x72, 0x79, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x5f, 0x69, 0x73, 0x6f, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x4c, 0x92, 0x41, 0x49, 0x2a, 0x47, 0xe5, 0x9b, 0xbd, 0xe5, 0xae, 0xb6, 0x63,
	0x6f, 0x64, 0x65, 0x2c, 0xe8, 0x81, 0x9a, 0xe5, 0x90, 0x88, 0xe5, 0xb1, 0x82, 0xe4, 0xbc, 0x9a,
	0xe5, 0x9c, 0xa8, 0x62, 0x6f, 0x64, 0x79, 0xe4, 0xb8, 0xad, 0xe5, 0xa2, 0x9e, 0xe5, 0x8a, 0xa0,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x43, 0x6f, 0x64, 0x65, 0xe5, 0xad, 0x97, 0xe6, 0xae,
	0xb5, 0xe4, 0xbc, 0x9a, 0xe5, 0xaf, 0xbc, 0xe8, 0x87, 0xb4, 0xe5, 0x86, 0xb2, 0xe7, 0xaa, 0x81,
	0x52, 0x0e, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x49, 0x73, 0x6f,
	0x12, 0x2e, 0x0a, 0x09, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7,
	0xe5, 0x90, 0x8d, 0xe7, 0xa7, 0xb0, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x46, 0x0a, 0x12, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72,
	0x79, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x18, 0x92, 0x41,
	0x15, 0x2a, 0x13, 0xe6, 0x89, 0x8b, 0xe6, 0x9c, 0xba, 0xe5, 0x8f, 0xb7, 0xe5, 0x9b, 0xbd, 0xe5,
	0xae, 0xb6, 0x63, 0x6f, 0x64, 0x65, 0x52, 0x10, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x43, 0x6f, 0x75,
	0x6e, 0x74, 0x72, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x40, 0x0a, 0x0f, 0x70, 0x68, 0x6f, 0x6e,
	0x65, 0x5f, 0x61, 0x72, 0x65, 0x61, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x18, 0x92, 0x41, 0x15, 0x2a, 0x13, 0xe6, 0x89, 0x8b, 0xe6, 0x9c, 0xba, 0xe5, 0x8f,
	0xb7, 0xe5, 0x9c, 0xb0, 0xe5, 0x8c, 0xba, 0x63, 0x6f, 0x64, 0x65, 0x52, 0x0d, 0x70, 0x68, 0x6f,
	0x6e, 0x65, 0x41, 0x72, 0x65, 0x61, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x24, 0x0a, 0x05, 0x70, 0x68,
	0x6f, 0x6e, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0e, 0x92, 0x41, 0x0b, 0x2a, 0x09,
	0xe6, 0x89, 0x8b, 0xe6, 0x9c, 0xba, 0xe5, 0x8f, 0xb7, 0x52, 0x05, 0x70, 0x68, 0x6f, 0x6e, 0x65,
	0x12, 0x34, 0x0a, 0x0d, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x6e, 0x63, 0x65, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0f, 0x92, 0x41, 0x0c, 0x2a, 0x0a, 0xe7, 0x9c,
	0x81, 0xe4, 0xbb, 0xbd, 0x63, 0x6f, 0x64, 0x65, 0x52, 0x0c, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x6e,
	0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2e, 0x0a, 0x09, 0x63, 0x69, 0x74, 0x79, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c,
	0xe5, 0x9f, 0x8e, 0xe5, 0xb8, 0x82, 0xe5, 0x90, 0x8d, 0xe7, 0xa7, 0xb0, 0x52, 0x08, 0x63, 0x69,
	0x74, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x38, 0x0a, 0x0e, 0x73, 0x74, 0x72, 0x65, 0x65, 0x74,
	0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11,
	0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe8, 0xa1, 0x97, 0xe9, 0x81, 0x93, 0xe5, 0x9c, 0xb0, 0xe5, 0x9d,
	0x80, 0x52, 0x0d, 0x73, 0x74, 0x72, 0x65, 0x65, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x12, 0x45, 0x0a, 0x15, 0x62, 0x75, 0x69, 0x6c, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x75, 0x6e, 0x69,
	0x74, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0x8d, 0x95, 0xe5, 0x85, 0x83, 0xe5, 0x9c, 0xb0, 0xe5,
	0x9d, 0x80, 0x52, 0x13, 0x62, 0x75, 0x69, 0x6c, 0x64, 0x69, 0x6e, 0x67, 0x55, 0x6e, 0x69, 0x74,
	0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x2c, 0x0a, 0x0b, 0x70, 0x6f, 0x73, 0x74, 0x61,
	0x6c, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0x92, 0x41,
	0x08, 0x2a, 0x06, 0xe9, 0x82, 0xae, 0xe7, 0xbc, 0x96, 0x52, 0x0a, 0x70, 0x6f, 0x73, 0x74, 0x61,
	0x6c, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x36, 0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x64, 0x65, 0x66, 0x61,
	0x75, 0x6c, 0x74, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x08, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12,
	0xe6, 0x98, 0xaf, 0xe5, 0x90, 0xa6, 0xe9, 0xbb, 0x98, 0xe8, 0xae, 0xa4, 0xe5, 0x9c, 0xb0, 0xe5,
	0x9d, 0x80, 0x52, 0x09, 0x69, 0x73, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x12, 0x52, 0x0a,
	0x0c, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x73, 0x68, 0x6f, 0x77, 0x18, 0x0d, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x2f, 0x92, 0x41, 0x2c, 0x2a, 0x2a, 0xe6, 0x98, 0xbe, 0xe7, 0xa4, 0xba,
	0xe5, 0x90, 0x8d, 0xe7, 0xa7, 0xb0, 0xef, 0xbc, 0x8c, 0xe8, 0xbf, 0x94, 0xe5, 0x9b, 0x9e, 0xe7,
	0x9a, 0x84, 0xe6, 0x97, 0xb6, 0xe5, 0x80, 0x99, 0xe5, 0xb1, 0x95, 0xe7, 0xa4, 0xba, 0xe4, 0xbd,
	0xbf, 0xe7, 0x94, 0xa8, 0x52, 0x0b, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x53, 0x68, 0x6f,
	0x77, 0x12, 0x34, 0x0a, 0x0c, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0x9b,
	0xbd, 0xe5, 0xae, 0xb6, 0xe5, 0x90, 0x8d, 0xe7, 0xa7, 0xb0, 0x52, 0x0b, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x72, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x7a, 0x0a, 0x05, 0x49, 0x6d, 0x61, 0x67, 0x65,
	0x12, 0x23, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92,
	0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0x9b, 0xbe, 0xe7, 0x89, 0x87, 0xe5, 0x9c, 0xb0, 0xe5, 0x9d, 0x80,
	0x52, 0x03, 0x75, 0x72, 0x6c, 0x12, 0x24, 0x0a, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x05, 0x42, 0x0e, 0x92, 0x41, 0x0b, 0x2a, 0x09, 0xe5, 0x9b, 0xbe, 0xe7, 0x89,
	0x87, 0xe5, 0xae, 0xbd, 0x52, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x12, 0x26, 0x0a, 0x06, 0x68,
	0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x42, 0x0e, 0x92, 0x41, 0x0b,
	0x2a, 0x09, 0xe5, 0x9b, 0xbe, 0xe7, 0x89, 0x87, 0xe9, 0xab, 0x98, 0x52, 0x06, 0x68, 0x65, 0x69,
	0x67, 0x68, 0x74, 0x22, 0xb7, 0x02, 0x0a, 0x0e, 0x47, 0x6f, 0x6f, 0x64, 0x73, 0x53, 0x70, 0x65,
	0x63, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x20, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x10, 0x92, 0x41, 0x0d, 0x2a, 0x0b, 0xe8, 0xa7, 0x84, 0xe6, 0xa0, 0xbc, 0xe5,
	0x80, 0xbc, 0x49, 0x44, 0x52, 0x02, 0x69, 0x64, 0x12, 0x22, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0e, 0x92, 0x41, 0x0b, 0x2a, 0x09, 0xe8, 0xa7, 0x84,
	0xe6, 0xa0, 0xbc, 0xe5, 0x80, 0xbc, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x2d, 0x0a, 0x08,
	0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x42, 0x11,
	0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe6, 0x98, 0xaf, 0xe5, 0x90, 0xa6, 0xe9, 0x80, 0x89, 0xe4, 0xb8,
	0xad, 0x52, 0x08, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x12, 0x44, 0x0a, 0x05, 0x69,
	0x6d, 0x61, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x49,
	0x6d, 0x61, 0x67, 0x65, 0x42, 0x14, 0x92, 0x41, 0x11, 0x2a, 0x0f, 0xe8, 0xa7, 0x84, 0xe6, 0xa0,
	0xbc, 0xe5, 0x80, 0xbc, 0xe5, 0x9b, 0xbe, 0xe7, 0x89, 0x87, 0x52, 0x05, 0x69, 0x6d, 0x61, 0x67,
	0x65, 0x12, 0x33, 0x0a, 0x07, 0x73, 0x70, 0x65, 0x63, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x1a, 0x92, 0x41, 0x17, 0x2a, 0x15, 0xe8, 0xa7, 0x84, 0xe6, 0xa0, 0xbc, 0x49,
	0x44, 0x2c, 0x61, 0x70, 0x70, 0xe7, 0xab, 0xaf, 0xe9, 0x9c, 0x80, 0xe8, 0xa6, 0x81, 0x52, 0x06,
	0x73, 0x70, 0x65, 0x63, 0x49, 0x64, 0x12, 0x35, 0x0a, 0x07, 0x75, 0x6e, 0x69, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1c, 0x92, 0x41, 0x19, 0x2a, 0x17, 0xe8, 0xa7,
	0x84, 0xe6, 0xa0, 0xbc, 0xe5, 0x8d, 0x95, 0xe4, 0xbd, 0x8d, 0xef, 0xbc, 0x9a, 0xe5, 0x8d, 0x95,
	0xe4, 0xbd, 0x8d, 0x69, 0x64, 0x52, 0x06, 0x75, 0x6e, 0x69, 0x74, 0x49, 0x64, 0x22, 0x9c, 0x01,
	0x0a, 0x09, 0x47, 0x6f, 0x6f, 0x64, 0x73, 0x53, 0x70, 0x65, 0x63, 0x12, 0x1d, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe8, 0xa7,
	0x84, 0xe6, 0xa0, 0xbc, 0x49, 0x44, 0x52, 0x02, 0x69, 0x64, 0x12, 0x25, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe8,
	0xa7, 0x84, 0xe6, 0xa0, 0xbc, 0xe5, 0x90, 0x8d, 0xe7, 0xa7, 0xb0, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x49, 0x0a, 0x06, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f,
	0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x6f, 0x6f, 0x64, 0x73, 0x53, 0x70, 0x65, 0x63, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x42, 0x0e, 0x92, 0x41, 0x0b, 0x2a, 0x09, 0xe8, 0xa7, 0x84, 0xe6, 0xa0,
	0xbc, 0xe5, 0x80, 0xbc, 0x52, 0x06, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x22, 0x91, 0x01, 0x0a,
	0x0c, 0x47, 0x6f, 0x6f, 0x64, 0x73, 0x53, 0x6b, 0x75, 0x53, 0x70, 0x65, 0x63, 0x12, 0x26, 0x0a,
	0x07, 0x73, 0x70, 0x65, 0x63, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d,
	0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe8, 0xa7, 0x84, 0xe6, 0xa0, 0xbc, 0x49, 0x44, 0x52, 0x06, 0x73,
	0x70, 0x65, 0x63, 0x49, 0x64, 0x12, 0x2b, 0x0a, 0x08, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x10, 0x92, 0x41, 0x0d, 0x2a, 0x0b, 0xe8, 0xa7,
	0x84, 0xe6, 0xa0, 0xbc, 0xe5, 0x80, 0xbc, 0x49, 0x44, 0x52, 0x07, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x49, 0x64, 0x12, 0x2c, 0x0a, 0x07, 0x75, 0x6e, 0x69, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x13, 0x92, 0x41, 0x10, 0x2a, 0x0e, 0xe8, 0xa7, 0x84, 0xe6, 0xa0, 0xbc,
	0xe5, 0x8d, 0x95, 0xe4, 0xbd, 0x8d, 0x49, 0x44, 0x52, 0x06, 0x75, 0x6e, 0x69, 0x74, 0x49, 0x64,
	0x22, 0xf1, 0x01, 0x0a, 0x08, 0x47, 0x6f, 0x6f, 0x64, 0x73, 0x53, 0x6b, 0x75, 0x12, 0x22, 0x0a,
	0x06, 0x73, 0x6b, 0x75, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0x92,
	0x41, 0x08, 0x2a, 0x06, 0x73, 0x6b, 0x75, 0x5f, 0x69, 0x64, 0x52, 0x05, 0x73, 0x6b, 0x75, 0x49,
	0x64, 0x12, 0x4b, 0x0a, 0x05, 0x73, 0x70, 0x65, 0x63, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72,
	0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x6f, 0x6f, 0x64, 0x73, 0x53, 0x6b, 0x75, 0x53, 0x70, 0x65,
	0x63, 0x42, 0x14, 0x92, 0x41, 0x11, 0x2a, 0x0f, 0x73, 0x6b, 0x75, 0xe8, 0xa7, 0x84, 0xe6, 0xa0,
	0xbc, 0xe7, 0xbb, 0x84, 0xe5, 0x90, 0x88, 0x52, 0x05, 0x73, 0x70, 0x65, 0x63, 0x73, 0x12, 0x24,
	0x0a, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x02, 0x42, 0x0e, 0x92,
	0x41, 0x0b, 0x2a, 0x09, 0x73, 0x6b, 0x75, 0xe4, 0xbb, 0xb7, 0xe6, 0xa0, 0xbc, 0x52, 0x05, 0x70,
	0x72, 0x69, 0x63, 0x65, 0x12, 0x2b, 0x0a, 0x07, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x08, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe6, 0x98, 0xaf, 0xe5,
	0x90, 0xa6, 0xe7, 0xa6, 0x81, 0xe7, 0x94, 0xa8, 0x52, 0x07, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c,
	0x65, 0x12, 0x21, 0x0a, 0x05, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05,
	0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe5, 0xba, 0x93, 0xe5, 0xad, 0x98, 0x52, 0x05, 0x73,
	0x74, 0x6f, 0x63, 0x6b, 0x2a, 0xf3, 0x01, 0x0a, 0x0d, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x12, 0x08, 0x0a, 0x04, 0x47, 0x4f, 0x4c, 0x44, 0x10, 0x00,
	0x12, 0x0d, 0x0a, 0x09, 0x47, 0x49, 0x46, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x10, 0x01, 0x12,
	0x08, 0x0a, 0x04, 0x54, 0x41, 0x53, 0x4b, 0x10, 0x02, 0x12, 0x07, 0x0a, 0x03, 0x41, 0x4c, 0x49,
	0x10, 0x03, 0x12, 0x0a, 0x0a, 0x06, 0x57, 0x45, 0x43, 0x48, 0x41, 0x54, 0x10, 0x04, 0x12, 0x0d,
	0x0a, 0x09, 0x41, 0x50, 0x50, 0x4c, 0x45, 0x5f, 0x49, 0x41, 0x50, 0x10, 0x05, 0x12, 0x0e, 0x0a,
	0x0a, 0x47, 0x4f, 0x4f, 0x47, 0x4c, 0x45, 0x5f, 0x49, 0x41, 0x50, 0x10, 0x06, 0x12, 0x0d, 0x0a,
	0x09, 0x55, 0x4e, 0x49, 0x4f, 0x4e, 0x5f, 0x50, 0x41, 0x59, 0x10, 0x07, 0x12, 0x0d, 0x0a, 0x09,
	0x41, 0x50, 0x50, 0x4c, 0x45, 0x5f, 0x50, 0x41, 0x59, 0x10, 0x08, 0x12, 0x0e, 0x0a, 0x0a, 0x47,
	0x4f, 0x4f, 0x47, 0x4c, 0x45, 0x5f, 0x50, 0x41, 0x59, 0x10, 0x09, 0x12, 0x0a, 0x0a, 0x06, 0x50,
	0x41, 0x59, 0x50, 0x41, 0x4c, 0x10, 0x0a, 0x12, 0x0d, 0x0a, 0x09, 0x57, 0x45, 0x43, 0x48, 0x41,
	0x54, 0x5f, 0x48, 0x35, 0x10, 0x0b, 0x12, 0x0b, 0x0a, 0x07, 0x41, 0x4c, 0x49, 0x5f, 0x57, 0x41,
	0x50, 0x10, 0x0c, 0x12, 0x0d, 0x0a, 0x09, 0x46, 0x4f, 0x52, 0x45, 0x58, 0x5f, 0x50, 0x41, 0x59,
	0x10, 0x0d, 0x12, 0x0a, 0x0a, 0x06, 0x50, 0x4f, 0x49, 0x4e, 0x54, 0x53, 0x10, 0x0e, 0x12, 0x1a,
	0x0a, 0x16, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x4d, 0x45, 0x54, 0x48, 0x4f, 0x44,
	0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x63, 0x2a, 0x57, 0x0a, 0x0b, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x0b, 0x0a, 0x07, 0x55, 0x4e, 0x4b,
	0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x09, 0x0a, 0x05, 0x55, 0x4e, 0x50, 0x41, 0x59, 0x10,
	0x01, 0x12, 0x08, 0x0a, 0x04, 0x50, 0x41, 0x49, 0x44, 0x10, 0x02, 0x12, 0x0a, 0x0a, 0x06, 0x43,
	0x41, 0x4e, 0x43, 0x45, 0x4c, 0x10, 0x03, 0x12, 0x0c, 0x0a, 0x08, 0x44, 0x45, 0x4c, 0x49, 0x56,
	0x45, 0x52, 0x59, 0x10, 0x04, 0x12, 0x0c, 0x0a, 0x08, 0x43, 0x4f, 0x4d, 0x50, 0x4c, 0x45, 0x54,
	0x45, 0x10, 0x05, 0x2a, 0x8f, 0x01, 0x0a, 0x0d, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x16, 0x0a, 0x12, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x55, 0x4e, 0x50, 0x41, 0x59, 0x10, 0x00, 0x12, 0x15, 0x0a,
	0x11, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x50, 0x41,
	0x49, 0x44, 0x10, 0x01, 0x12, 0x19, 0x0a, 0x15, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x43, 0x41, 0x4e, 0x43, 0x45, 0x4c, 0x45, 0x44, 0x10, 0x02, 0x12,
	0x19, 0x0a, 0x15, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x54, 0x4f, 0x52, 0x45, 0x46, 0x55, 0x4e, 0x44, 0x10, 0x03, 0x12, 0x19, 0x0a, 0x15, 0x50, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x45, 0x46, 0x55, 0x4e,
	0x44, 0x45, 0x44, 0x10, 0x04, 0x2a, 0x47, 0x0a, 0x08, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72,
	0x6d, 0x12, 0x07, 0x0a, 0x03, 0x49, 0x4f, 0x53, 0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07, 0x41, 0x4e,
	0x44, 0x52, 0x4f, 0x49, 0x44, 0x10, 0x01, 0x12, 0x06, 0x0a, 0x02, 0x50, 0x43, 0x10, 0x02, 0x12,
	0x07, 0x0a, 0x03, 0x57, 0x45, 0x42, 0x10, 0x03, 0x12, 0x14, 0x0a, 0x0f, 0x50, 0x6c, 0x61, 0x74,
	0x66, 0x6f, 0x72, 0x6d, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0xe7, 0x07, 0x2a, 0xad,
	0x01, 0x0a, 0x0d, 0x47, 0x6f, 0x6f, 0x64, 0x73, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79,
	0x12, 0x1b, 0x0a, 0x17, 0x47, 0x4f, 0x4f, 0x44, 0x53, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f,
	0x52, 0x59, 0x5f, 0x50, 0x48, 0x59, 0x53, 0x49, 0x43, 0x41, 0x4c, 0x10, 0x00, 0x12, 0x16, 0x0a,
	0x12, 0x47, 0x4f, 0x4f, 0x44, 0x53, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f,
	0x56, 0x49, 0x50, 0x10, 0x01, 0x12, 0x16, 0x0a, 0x12, 0x47, 0x4f, 0x4f, 0x44, 0x53, 0x5f, 0x43,
	0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x56, 0x50, 0x53, 0x10, 0x02, 0x12, 0x19, 0x0a,
	0x15, 0x47, 0x4f, 0x4f, 0x44, 0x53, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f,
	0x52, 0x45, 0x50, 0x4f, 0x52, 0x54, 0x10, 0x03, 0x12, 0x1d, 0x0a, 0x19, 0x47, 0x4f, 0x4f, 0x44,
	0x53, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x45, 0x58, 0x48, 0x49, 0x42,
	0x49, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x04, 0x12, 0x15, 0x0a, 0x11, 0x47, 0x4f, 0x4f, 0x44, 0x53,
	0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x45, 0x41, 0x10, 0x05, 0x2a, 0x34,
	0x0a, 0x0b, 0x47, 0x6f, 0x6f, 0x64, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x12, 0x0a,
	0x0e, 0x47, 0x6f, 0x6f, 0x64, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x4f, 0x66, 0x66, 0x10,
	0x00, 0x12, 0x11, 0x0a, 0x0d, 0x47, 0x6f, 0x6f, 0x64, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x4f, 0x6e, 0x10, 0x01, 0x2a, 0x4e, 0x0a, 0x0b, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x53, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x12, 0x09, 0x0a, 0x05, 0x53, 0x54, 0x4f, 0x52, 0x45, 0x10, 0x00, 0x12, 0x07,
	0x0a, 0x03, 0x56, 0x49, 0x50, 0x10, 0x01, 0x12, 0x07, 0x0a, 0x03, 0x56, 0x50, 0x53, 0x10, 0x02,
	0x12, 0x0a, 0x0a, 0x06, 0x52, 0x45, 0x50, 0x4f, 0x52, 0x54, 0x10, 0x03, 0x12, 0x0e, 0x0a, 0x0a,
	0x45, 0x58, 0x48, 0x49, 0x42, 0x49, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x04, 0x12, 0x06, 0x0a, 0x02,
	0x45, 0x41, 0x10, 0x05, 0x2a, 0xef, 0x01, 0x0a, 0x12, 0x4c, 0x6f, 0x67, 0x69, 0x73, 0x74, 0x69,
	0x63, 0x53, 0x74, 0x65, 0x70, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1d, 0x0a, 0x19, 0x4c,
	0x6f, 0x67, 0x69, 0x73, 0x74, 0x69, 0x63, 0x53, 0x74, 0x65, 0x70, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x45, 0x44, 0x10, 0x00, 0x12, 0x20, 0x0a, 0x1c, 0x4c, 0x6f,
	0x67, 0x69, 0x73, 0x74, 0x69, 0x63, 0x53, 0x74, 0x65, 0x70, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x50, 0x52, 0x4f, 0x43, 0x45, 0x53, 0x53, 0x49, 0x4e, 0x47, 0x10, 0x01, 0x12, 0x1d, 0x0a, 0x19,
	0x4c, 0x6f, 0x67, 0x69, 0x73, 0x74, 0x69, 0x63, 0x53, 0x74, 0x65, 0x70, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x53, 0x48, 0x49, 0x50, 0x50, 0x45, 0x44, 0x10, 0x02, 0x12, 0x1c, 0x0a, 0x18, 0x4c,
	0x6f, 0x67, 0x69, 0x73, 0x74, 0x69, 0x63, 0x53, 0x74, 0x65, 0x70, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x50, 0x49, 0x43, 0x4b, 0x45, 0x44, 0x10, 0x03, 0x12, 0x1d, 0x0a, 0x19, 0x4c, 0x6f, 0x67,
	0x69, 0x73, 0x74, 0x69, 0x63, 0x53, 0x74, 0x65, 0x70, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x54,
	0x52, 0x41, 0x4e, 0x53, 0x49, 0x54, 0x10, 0x04, 0x12, 0x1e, 0x0a, 0x1a, 0x4c, 0x6f, 0x67, 0x69,
	0x73, 0x74, 0x69, 0x63, 0x53, 0x74, 0x65, 0x70, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x44, 0x45,
	0x4c, 0x49, 0x56, 0x45, 0x52, 0x59, 0x10, 0x05, 0x12, 0x1c, 0x0a, 0x18, 0x4c, 0x6f, 0x67, 0x69,
	0x73, 0x74, 0x69, 0x63, 0x53, 0x74, 0x65, 0x70, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x53, 0x49,
	0x47, 0x4e, 0x45, 0x44, 0x10, 0x06, 0x42, 0x16, 0x5a, 0x14, 0x61, 0x70, 0x69, 0x2f, 0x67, 0x6f,
	0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2f, 0x76, 0x31, 0x3b, 0x76, 0x31, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_gold_store_v1_models_proto_rawDescOnce sync.Once
	file_gold_store_v1_models_proto_rawDescData = file_gold_store_v1_models_proto_rawDesc
)

func file_gold_store_v1_models_proto_rawDescGZIP() []byte {
	file_gold_store_v1_models_proto_rawDescOnce.Do(func() {
		file_gold_store_v1_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_gold_store_v1_models_proto_rawDescData)
	})
	return file_gold_store_v1_models_proto_rawDescData
}

var file_gold_store_v1_models_proto_enumTypes = make([]protoimpl.EnumInfo, 8)
var file_gold_store_v1_models_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_gold_store_v1_models_proto_goTypes = []interface{}{
	(PaymentMethod)(0),      // 0: api.gold_store.v1.PaymentMethod
	(OrderStatus)(0),        // 1: api.gold_store.v1.OrderStatus
	(PaymentStatus)(0),      // 2: api.gold_store.v1.PaymentStatus
	(Platform)(0),           // 3: api.gold_store.v1.Platform
	(GoodsCategory)(0),      // 4: api.gold_store.v1.GoodsCategory
	(GoodsStatus)(0),        // 5: api.gold_store.v1.GoodsStatus
	(OrderSource)(0),        // 6: api.gold_store.v1.OrderSource
	(LogisticStepStatus)(0), // 7: api.gold_store.v1.LogisticStepStatus
	(*LogisticStep)(nil),    // 8: api.gold_store.v1.LogisticStep
	(*Address)(nil),         // 9: api.gold_store.v1.Address
	(*Image)(nil),           // 10: api.gold_store.v1.Image
	(*GoodsSpecValue)(nil),  // 11: api.gold_store.v1.GoodsSpecValue
	(*GoodsSpec)(nil),       // 12: api.gold_store.v1.GoodsSpec
	(*GoodsSkuSpec)(nil),    // 13: api.gold_store.v1.GoodsSkuSpec
	(*GoodsSku)(nil),        // 14: api.gold_store.v1.GoodsSku
}
var file_gold_store_v1_models_proto_depIdxs = []int32{
	7,  // 0: api.gold_store.v1.LogisticStep.status:type_name -> api.gold_store.v1.LogisticStepStatus
	10, // 1: api.gold_store.v1.GoodsSpecValue.image:type_name -> api.gold_store.v1.Image
	11, // 2: api.gold_store.v1.GoodsSpec.values:type_name -> api.gold_store.v1.GoodsSpecValue
	13, // 3: api.gold_store.v1.GoodsSku.specs:type_name -> api.gold_store.v1.GoodsSkuSpec
	4,  // [4:4] is the sub-list for method output_type
	4,  // [4:4] is the sub-list for method input_type
	4,  // [4:4] is the sub-list for extension type_name
	4,  // [4:4] is the sub-list for extension extendee
	0,  // [0:4] is the sub-list for field type_name
}

func init() { file_gold_store_v1_models_proto_init() }
func file_gold_store_v1_models_proto_init() {
	if File_gold_store_v1_models_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_gold_store_v1_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LogisticStep); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_models_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Address); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_models_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Image); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_models_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GoodsSpecValue); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_models_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GoodsSpec); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_models_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GoodsSkuSpec); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_models_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GoodsSku); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_gold_store_v1_models_proto_rawDesc,
			NumEnums:      8,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_gold_store_v1_models_proto_goTypes,
		DependencyIndexes: file_gold_store_v1_models_proto_depIdxs,
		EnumInfos:         file_gold_store_v1_models_proto_enumTypes,
		MessageInfos:      file_gold_store_v1_models_proto_msgTypes,
	}.Build()
	File_gold_store_v1_models_proto = out.File
	file_gold_store_v1_models_proto_rawDesc = nil
	file_gold_store_v1_models_proto_goTypes = nil
	file_gold_store_v1_models_proto_depIdxs = nil
}
