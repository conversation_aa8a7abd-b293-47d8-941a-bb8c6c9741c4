// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.27.3
// source: community/v1/usercenter.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	UserCenter_GetUsersInfo_FullMethodName               = "/api.user_center.v1.UserCenter/GetUsersInfo"
	UserCenter_GetEnterpriseUserIdsByCode_FullMethodName = "/api.user_center.v1.UserCenter/GetEnterpriseUserIdsByCode"
	UserCenter_GetOfficialNumberTypeById_FullMethodName  = "/api.user_center.v1.UserCenter/GetOfficialNumberTypeById"
	UserCenter_GetUserFollow_FullMethodName              = "/api.user_center.v1.UserCenter/GetUserFollow"
)

// UserCenterClient is the client API for UserCenter service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type UserCenterClient interface {
	GetUsersInfo(ctx context.Context, in *GetUsersRequest, opts ...grpc.CallOption) (*GetUsersReply, error)
	// 根据企业code获取 企业员工和管理员userid
	GetEnterpriseUserIdsByCode(ctx context.Context, in *GetEnterpriseUserIdsByCodeRequest, opts ...grpc.CallOption) (*GetEnterpriseUserIdsByCodeReply, error)
	// 根据id获取企业类型
	GetOfficialNumberTypeById(ctx context.Context, in *GetOfficialNumberTypeByIdRequest, opts ...grpc.CallOption) (*GetOfficialNumberTypeByIdReply, error)
	// 获取用户关注对象
	GetUserFollow(ctx context.Context, in *GetUserFollowRequest, opts ...grpc.CallOption) (*GetUserFollowReply, error)
}

type userCenterClient struct {
	cc grpc.ClientConnInterface
}

func NewUserCenterClient(cc grpc.ClientConnInterface) UserCenterClient {
	return &userCenterClient{cc}
}

func (c *userCenterClient) GetUsersInfo(ctx context.Context, in *GetUsersRequest, opts ...grpc.CallOption) (*GetUsersReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetUsersReply)
	err := c.cc.Invoke(ctx, UserCenter_GetUsersInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userCenterClient) GetEnterpriseUserIdsByCode(ctx context.Context, in *GetEnterpriseUserIdsByCodeRequest, opts ...grpc.CallOption) (*GetEnterpriseUserIdsByCodeReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetEnterpriseUserIdsByCodeReply)
	err := c.cc.Invoke(ctx, UserCenter_GetEnterpriseUserIdsByCode_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userCenterClient) GetOfficialNumberTypeById(ctx context.Context, in *GetOfficialNumberTypeByIdRequest, opts ...grpc.CallOption) (*GetOfficialNumberTypeByIdReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetOfficialNumberTypeByIdReply)
	err := c.cc.Invoke(ctx, UserCenter_GetOfficialNumberTypeById_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userCenterClient) GetUserFollow(ctx context.Context, in *GetUserFollowRequest, opts ...grpc.CallOption) (*GetUserFollowReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetUserFollowReply)
	err := c.cc.Invoke(ctx, UserCenter_GetUserFollow_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// UserCenterServer is the server API for UserCenter service.
// All implementations must embed UnimplementedUserCenterServer
// for forward compatibility.
type UserCenterServer interface {
	GetUsersInfo(context.Context, *GetUsersRequest) (*GetUsersReply, error)
	// 根据企业code获取 企业员工和管理员userid
	GetEnterpriseUserIdsByCode(context.Context, *GetEnterpriseUserIdsByCodeRequest) (*GetEnterpriseUserIdsByCodeReply, error)
	// 根据id获取企业类型
	GetOfficialNumberTypeById(context.Context, *GetOfficialNumberTypeByIdRequest) (*GetOfficialNumberTypeByIdReply, error)
	// 获取用户关注对象
	GetUserFollow(context.Context, *GetUserFollowRequest) (*GetUserFollowReply, error)
	mustEmbedUnimplementedUserCenterServer()
}

// UnimplementedUserCenterServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedUserCenterServer struct{}

func (UnimplementedUserCenterServer) GetUsersInfo(context.Context, *GetUsersRequest) (*GetUsersReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUsersInfo not implemented")
}
func (UnimplementedUserCenterServer) GetEnterpriseUserIdsByCode(context.Context, *GetEnterpriseUserIdsByCodeRequest) (*GetEnterpriseUserIdsByCodeReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetEnterpriseUserIdsByCode not implemented")
}
func (UnimplementedUserCenterServer) GetOfficialNumberTypeById(context.Context, *GetOfficialNumberTypeByIdRequest) (*GetOfficialNumberTypeByIdReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetOfficialNumberTypeById not implemented")
}
func (UnimplementedUserCenterServer) GetUserFollow(context.Context, *GetUserFollowRequest) (*GetUserFollowReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserFollow not implemented")
}
func (UnimplementedUserCenterServer) mustEmbedUnimplementedUserCenterServer() {}
func (UnimplementedUserCenterServer) testEmbeddedByValue()                    {}

// UnsafeUserCenterServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to UserCenterServer will
// result in compilation errors.
type UnsafeUserCenterServer interface {
	mustEmbedUnimplementedUserCenterServer()
}

func RegisterUserCenterServer(s grpc.ServiceRegistrar, srv UserCenterServer) {
	// If the following call pancis, it indicates UnimplementedUserCenterServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&UserCenter_ServiceDesc, srv)
}

func _UserCenter_GetUsersInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUsersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserCenterServer).GetUsersInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserCenter_GetUsersInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserCenterServer).GetUsersInfo(ctx, req.(*GetUsersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserCenter_GetEnterpriseUserIdsByCode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetEnterpriseUserIdsByCodeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserCenterServer).GetEnterpriseUserIdsByCode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserCenter_GetEnterpriseUserIdsByCode_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserCenterServer).GetEnterpriseUserIdsByCode(ctx, req.(*GetEnterpriseUserIdsByCodeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserCenter_GetOfficialNumberTypeById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOfficialNumberTypeByIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserCenterServer).GetOfficialNumberTypeById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserCenter_GetOfficialNumberTypeById_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserCenterServer).GetOfficialNumberTypeById(ctx, req.(*GetOfficialNumberTypeByIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserCenter_GetUserFollow_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserFollowRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserCenterServer).GetUserFollow(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserCenter_GetUserFollow_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserCenterServer).GetUserFollow(ctx, req.(*GetUserFollowRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// UserCenter_ServiceDesc is the grpc.ServiceDesc for UserCenter service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var UserCenter_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.user_center.v1.UserCenter",
	HandlerType: (*UserCenterServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetUsersInfo",
			Handler:    _UserCenter_GetUsersInfo_Handler,
		},
		{
			MethodName: "GetEnterpriseUserIdsByCode",
			Handler:    _UserCenter_GetEnterpriseUserIdsByCode_Handler,
		},
		{
			MethodName: "GetOfficialNumberTypeById",
			Handler:    _UserCenter_GetOfficialNumberTypeById_Handler,
		},
		{
			MethodName: "GetUserFollow",
			Handler:    _UserCenter_GetUserFollow_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "community/v1/usercenter.proto",
}
