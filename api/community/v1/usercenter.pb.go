// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v5.27.3
// source: community/v1/usercenter.proto

package v1

import (
	_ "github.com/grpc-ecosystem/grpc-gateway/v2/protoc-gen-openapiv2/options"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	_ "google.golang.org/protobuf/types/known/anypb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 官方号类型
type OfficialNumberType int32

const (
	OfficialNumberType_OfficialNumber_Unknown OfficialNumberType = 0 //未知
	OfficialNumberType_Trader                 OfficialNumberType = 1 // 交易商号
	OfficialNumberType_WikiFXMediate          OfficialNumberType = 2 // 天眼调解
	OfficialNumberType_WikiFXNews             OfficialNumberType = 3 // WikiFX-新闻
	OfficialNumberType_WikiFXExpress          OfficialNumberType = 4 // WikiFX-快讯
	OfficialNumberType_WikiFXSurvey           OfficialNumberType = 5 // WikiFX-实勘
	OfficialNumberType_ServiceProvider        OfficialNumberType = 6 // 服务商号
	OfficialNumberType_Regulator              OfficialNumberType = 7 // 监管机构号
	OfficialNumberType_User                   OfficialNumberType = 8 // 用户号
)

// Enum value maps for OfficialNumberType.
var (
	OfficialNumberType_name = map[int32]string{
		0: "OfficialNumber_Unknown",
		1: "Trader",
		2: "WikiFXMediate",
		3: "WikiFXNews",
		4: "WikiFXExpress",
		5: "WikiFXSurvey",
		6: "ServiceProvider",
		7: "Regulator",
		8: "User",
	}
	OfficialNumberType_value = map[string]int32{
		"OfficialNumber_Unknown": 0,
		"Trader":                 1,
		"WikiFXMediate":          2,
		"WikiFXNews":             3,
		"WikiFXExpress":          4,
		"WikiFXSurvey":           5,
		"ServiceProvider":        6,
		"Regulator":              7,
		"User":                   8,
	}
)

func (x OfficialNumberType) Enum() *OfficialNumberType {
	p := new(OfficialNumberType)
	*p = x
	return p
}

func (x OfficialNumberType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (OfficialNumberType) Descriptor() protoreflect.EnumDescriptor {
	return file_community_v1_usercenter_proto_enumTypes[0].Descriptor()
}

func (OfficialNumberType) Type() protoreflect.EnumType {
	return &file_community_v1_usercenter_proto_enumTypes[0]
}

func (x OfficialNumberType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use OfficialNumberType.Descriptor instead.
func (OfficialNumberType) EnumDescriptor() ([]byte, []int) {
	return file_community_v1_usercenter_proto_rawDescGZIP(), []int{0}
}

type GetUserFollowRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId string `protobuf:"bytes,1,opt,name=userId,json=userId,proto3" json:"userId"`
}

func (x *GetUserFollowRequest) Reset() {
	*x = GetUserFollowRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_community_v1_usercenter_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserFollowRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserFollowRequest) ProtoMessage() {}

func (x *GetUserFollowRequest) ProtoReflect() protoreflect.Message {
	mi := &file_community_v1_usercenter_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserFollowRequest.ProtoReflect.Descriptor instead.
func (*GetUserFollowRequest) Descriptor() ([]byte, []int) {
	return file_community_v1_usercenter_proto_rawDescGZIP(), []int{0}
}

func (x *GetUserFollowRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

type GetUserFollowReplyItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DataId   string             `protobuf:"bytes,1,opt,name=dataId,json=dataId,proto3" json:"dataId"`
	UserType OfficialNumberType `protobuf:"varint,2,opt,name=userType,json=userType,proto3,enum=api.user_center.v1.OfficialNumberType" json:"userType"`
}

func (x *GetUserFollowReplyItem) Reset() {
	*x = GetUserFollowReplyItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_community_v1_usercenter_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserFollowReplyItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserFollowReplyItem) ProtoMessage() {}

func (x *GetUserFollowReplyItem) ProtoReflect() protoreflect.Message {
	mi := &file_community_v1_usercenter_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserFollowReplyItem.ProtoReflect.Descriptor instead.
func (*GetUserFollowReplyItem) Descriptor() ([]byte, []int) {
	return file_community_v1_usercenter_proto_rawDescGZIP(), []int{1}
}

func (x *GetUserFollowReplyItem) GetDataId() string {
	if x != nil {
		return x.DataId
	}
	return ""
}

func (x *GetUserFollowReplyItem) GetUserType() OfficialNumberType {
	if x != nil {
		return x.UserType
	}
	return OfficialNumberType_OfficialNumber_Unknown
}

type GetUserFollowReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*GetUserFollowReplyItem `protobuf:"bytes,1,rep,name=list,json=list,proto3" json:"list"`
}

func (x *GetUserFollowReply) Reset() {
	*x = GetUserFollowReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_community_v1_usercenter_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserFollowReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserFollowReply) ProtoMessage() {}

func (x *GetUserFollowReply) ProtoReflect() protoreflect.Message {
	mi := &file_community_v1_usercenter_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserFollowReply.ProtoReflect.Descriptor instead.
func (*GetUserFollowReply) Descriptor() ([]byte, []int) {
	return file_community_v1_usercenter_proto_rawDescGZIP(), []int{2}
}

func (x *GetUserFollowReply) GetList() []*GetUserFollowReplyItem {
	if x != nil {
		return x.List
	}
	return nil
}

type GetOfficialNumberTypeByIdRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,json=id,proto3" json:"id"`
}

func (x *GetOfficialNumberTypeByIdRequest) Reset() {
	*x = GetOfficialNumberTypeByIdRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_community_v1_usercenter_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOfficialNumberTypeByIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOfficialNumberTypeByIdRequest) ProtoMessage() {}

func (x *GetOfficialNumberTypeByIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_community_v1_usercenter_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOfficialNumberTypeByIdRequest.ProtoReflect.Descriptor instead.
func (*GetOfficialNumberTypeByIdRequest) Descriptor() ([]byte, []int) {
	return file_community_v1_usercenter_proto_rawDescGZIP(), []int{3}
}

func (x *GetOfficialNumberTypeByIdRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type GetOfficialNumberTypeByIdReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserType OfficialNumberType `protobuf:"varint,1,opt,name=userType,json=userType,proto3,enum=api.user_center.v1.OfficialNumberType" json:"userType"`
}

func (x *GetOfficialNumberTypeByIdReply) Reset() {
	*x = GetOfficialNumberTypeByIdReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_community_v1_usercenter_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOfficialNumberTypeByIdReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOfficialNumberTypeByIdReply) ProtoMessage() {}

func (x *GetOfficialNumberTypeByIdReply) ProtoReflect() protoreflect.Message {
	mi := &file_community_v1_usercenter_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOfficialNumberTypeByIdReply.ProtoReflect.Descriptor instead.
func (*GetOfficialNumberTypeByIdReply) Descriptor() ([]byte, []int) {
	return file_community_v1_usercenter_proto_rawDescGZIP(), []int{4}
}

func (x *GetOfficialNumberTypeByIdReply) GetUserType() OfficialNumberType {
	if x != nil {
		return x.UserType
	}
	return OfficialNumberType_OfficialNumber_Unknown
}

type GetEnterpriseUserIdsByCodeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code string `protobuf:"bytes,1,opt,name=code,json=code,proto3" json:"code"`
}

func (x *GetEnterpriseUserIdsByCodeRequest) Reset() {
	*x = GetEnterpriseUserIdsByCodeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_community_v1_usercenter_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetEnterpriseUserIdsByCodeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEnterpriseUserIdsByCodeRequest) ProtoMessage() {}

func (x *GetEnterpriseUserIdsByCodeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_community_v1_usercenter_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEnterpriseUserIdsByCodeRequest.ProtoReflect.Descriptor instead.
func (*GetEnterpriseUserIdsByCodeRequest) Descriptor() ([]byte, []int) {
	return file_community_v1_usercenter_proto_rawDescGZIP(), []int{5}
}

func (x *GetEnterpriseUserIdsByCodeRequest) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

type GetEnterpriseUserIdsByCodeReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserIds []string `protobuf:"bytes,1,rep,name=userIds,json=userIds,proto3" json:"userIds"`
}

func (x *GetEnterpriseUserIdsByCodeReply) Reset() {
	*x = GetEnterpriseUserIdsByCodeReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_community_v1_usercenter_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetEnterpriseUserIdsByCodeReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEnterpriseUserIdsByCodeReply) ProtoMessage() {}

func (x *GetEnterpriseUserIdsByCodeReply) ProtoReflect() protoreflect.Message {
	mi := &file_community_v1_usercenter_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEnterpriseUserIdsByCodeReply.ProtoReflect.Descriptor instead.
func (*GetEnterpriseUserIdsByCodeReply) Descriptor() ([]byte, []int) {
	return file_community_v1_usercenter_proto_rawDescGZIP(), []int{6}
}

func (x *GetEnterpriseUserIdsByCodeReply) GetUserIds() []string {
	if x != nil {
		return x.UserIds
	}
	return nil
}

type GetUsersRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 登录用户id
	UserLoginId  string   `protobuf:"bytes,1,opt,name=userLoginId,json=userLoginId,proto3" json:"userLoginId"`
	LanguageCode string   `protobuf:"bytes,2,opt,name=languageCode,json=languageCode,proto3" json:"languageCode"`
	BusinessType int32    `protobuf:"varint,3,opt,name=businessType,json=businessType,proto3" json:"businessType"`
	UserIds      []string `protobuf:"bytes,4,rep,name=userIds,json=userIds,proto3" json:"userIds"`             //userid
	TraderCodes  []string `protobuf:"bytes,5,rep,name=traderCodes,json=traderCodes,proto3" json:"traderCodes"` //企业code
}

func (x *GetUsersRequest) Reset() {
	*x = GetUsersRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_community_v1_usercenter_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUsersRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUsersRequest) ProtoMessage() {}

func (x *GetUsersRequest) ProtoReflect() protoreflect.Message {
	mi := &file_community_v1_usercenter_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUsersRequest.ProtoReflect.Descriptor instead.
func (*GetUsersRequest) Descriptor() ([]byte, []int) {
	return file_community_v1_usercenter_proto_rawDescGZIP(), []int{7}
}

func (x *GetUsersRequest) GetUserLoginId() string {
	if x != nil {
		return x.UserLoginId
	}
	return ""
}

func (x *GetUsersRequest) GetLanguageCode() string {
	if x != nil {
		return x.LanguageCode
	}
	return ""
}

func (x *GetUsersRequest) GetBusinessType() int32 {
	if x != nil {
		return x.BusinessType
	}
	return 0
}

func (x *GetUsersRequest) GetUserIds() []string {
	if x != nil {
		return x.UserIds
	}
	return nil
}

func (x *GetUsersRequest) GetTraderCodes() []string {
	if x != nil {
		return x.TraderCodes
	}
	return nil
}

type UserInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NickName            string `protobuf:"bytes,1,opt,name=nickName,json=nickName,proto3" json:"nickName"`
	UserId              string `protobuf:"bytes,2,opt,name=userId,json=userId,proto3" json:"userId"`
	WikiFxNumber        string `protobuf:"bytes,3,opt,name=wikiFxNumber,json=wikiFxNumber,proto3" json:"wikiFxNumber"`                        //天眼号
	UserStatus          int32  `protobuf:"varint,4,opt,name=userStatus,json=userStatus,proto3" json:"userStatus"`                             // 用户状态 1企业号 2员工 3 个人 4 kol
	UserIdentityNew     int32  `protobuf:"varint,5,opt,name=userIdentityNew,json=userIdentityNew,proto3" json:"userIdentityNew"`              //用户身份 1普通用户 2个人投资者 3 Kol
	UserIdentityNewIcon string `protobuf:"bytes,6,opt,name=userIdentityNewIcon,json=userIdentityNewIcon,proto3" json:"userIdentityNewIcon"`   //用户身份icon
	IdentityType        int32  `protobuf:"varint,7,opt,name=identityType,json=identityType,proto3" json:"identityType"`                       //1个人 2企业
	EnterpriseVIcon     string `protobuf:"bytes,8,opt,name=enterpriseVIcon,json=enterpriseVIcon,proto3" json:"enterpriseVIcon"`               //企业号V标志 头像右下角
	EnterpriseVIcon2    string `protobuf:"bytes,9,opt,name=enterpriseVIcon2,json=enterpriseVIcon2,proto3" json:"enterpriseVIcon2"`            //企业号V标志 头像右下角新版
	TagIcon             string `protobuf:"bytes,10,opt,name=tagIcon,json=tagIcon,proto3" json:"tagIcon"`                                      /// 企业tag标志icon
	EnterpriseName      string `protobuf:"bytes,11,opt,name=enterpriseName,json=enterpriseName,proto3" json:"enterpriseName"`                 //服务商交易商名称
	EnterpriseCode      string `protobuf:"bytes,12,opt,name=enterpriseCode,json=enterpriseCode,proto3" json:"enterpriseCode"`                 //服务商交易商Code
	EnterpriseUserLevel int32  `protobuf:"varint,13,opt,name=enterpriseUserLevel,json=enterpriseUserLevel,proto3" json:"enterpriseUserLevel"` //2管理员 4员工
	EnterpriseLogo      string `protobuf:"bytes,14,opt,name=enterpriseLogo,json=enterpriseLogo,proto3" json:"enterpriseLogo"`
	EnterpriseIco       string `protobuf:"bytes,15,opt,name=enterpriseIco,json=enterpriseIco,proto3" json:"enterpriseIco"`
	AvatarAddress       string `protobuf:"bytes,16,opt,name=avatarAddress,json=avatarAddress,proto3" json:"avatarAddress"`                   // 头像
	OriginAvatarAddress string `protobuf:"bytes,17,opt,name=originAvatarAddress,json=originAvatarAddress,proto3" json:"originAvatarAddress"` //原始头像
	Position            string `protobuf:"bytes,18,opt,name=position,json=position,proto3" json:"position"`                                  //职位
	IsFollow            bool   `protobuf:"varint,19,opt,name=isFollow,json=isFollow,proto3" json:"isFollow"`                                 //是否关注
	EnterpriseType      int32  `protobuf:"varint,20,opt,name=enterpriseType,json=enterpriseType,proto3" json:"enterpriseType"`               // 1服务商 2 交易商
	AttentionStauts     int32  `protobuf:"varint,21,opt,name=attentionStauts,json=attentionStauts,proto3" json:"attentionStauts"`            //// 1未关注 2 已关注 3相互关注   4自己
	FollowCount         int32  `protobuf:"varint,22,opt,name=followCount,json=followCount,proto3" json:"followCount"`                        //关注数量
	FansCount           int32  `protobuf:"varint,23,opt,name=fansCount,json=fansCount,proto3" json:"fansCount"`                              //粉丝数量
	ApplaudCount        int32  `protobuf:"varint,24,opt,name=applaudCount,json=applaudCount,proto3" json:"applaudCount"`                     //点赞数量
	StaffTag            string `protobuf:"bytes,25,opt,name=staffTag,json=staffTag,proto3" json:"staffTag"`                                  //员工Tag
	DetailBgImg         string `protobuf:"bytes,26,opt,name=detailBgImg,json=detailBgImg,proto3" json:"detailBgImg"`                         //
	OriginNickName      string `protobuf:"bytes,27,opt,name=originNickName,json=originNickName,proto3" json:"originNickName"`
	RegistrationTime    string `protobuf:"bytes,28,opt,name=registrationTime,json=registrationTime,proto3" json:"registrationTime"` //注册时间
	CountryCode         string `protobuf:"bytes,29,opt,name=countryCode,json=countryCode,proto3" json:"countryCode"`
	AreaCode            string `protobuf:"bytes,30,opt,name=areaCode,json=areaCode,proto3" json:"areaCode"`          //手机区号
	PhoneNumber         string `protobuf:"bytes,31,opt,name=phoneNumber,json=phoneNumber,proto3" json:"phoneNumber"` //手机号
	Email               string `protobuf:"bytes,32,opt,name=email,json=email,proto3" json:"email"`                   //邮箱
	IsAuth              bool   `protobuf:"varint,33,opt,name=isAuth,json=isAuth,proto3" json:"isAuth"`               // 是否认证
	KolIcon             string `protobuf:"bytes,34,opt,name=kolIcon,json=kolIcon,proto3" json:"kolIcon"`
	RegisterLong        string `protobuf:"bytes,35,opt,name=registerLong,json=registerLong,proto3" json:"registerLong"`                    //注册时间
	IsFirmoffer         bool   `protobuf:"varint,36,opt,name=isFirmoffer,json=isFirmoffer,proto3" json:"isFirmoffer"`                      //是否是实盘用户
	DarenIcon           string `protobuf:"bytes,37,opt,name=darenIcon,json=darenIcon,proto3" json:"darenIcon"`                             //达人icon
	Official            string `protobuf:"bytes,38,opt,name=official,json=official,proto3" json:"official"`                                //官方
	OfficialColor       string `protobuf:"bytes,39,opt,name=officialColor,json=officialColor,proto3" json:"officialColor"`                 // 官方颜色
	TagWords            string `protobuf:"bytes,40,opt,name=TagWords,json=TagWords,proto3" json:"TagWords"`                                //显示用户身份
	NickNameColor       string `protobuf:"bytes,41,opt,name=NickNameColor,json=NickNameColor,proto3" json:"NickNameColor"`                 //昵称颜色
	OriginId            string `protobuf:"bytes,42,opt,name=OriginId,json=OriginId,proto3" json:"OriginId"`                                //原始Id
	OfficialNumberType  int32  `protobuf:"varint,43,opt,name=officialNumberType,json=officialNumberType,proto3" json:"officialNumberType"` //类型 1 交易商号 6 服务商号 2 天眼调解   3 WikiFX-新闻  4; WikiFX-快讯    5 WikiFX-实勘 7 监管机构号 8个人
}

func (x *UserInfo) Reset() {
	*x = UserInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_community_v1_usercenter_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserInfo) ProtoMessage() {}

func (x *UserInfo) ProtoReflect() protoreflect.Message {
	mi := &file_community_v1_usercenter_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserInfo.ProtoReflect.Descriptor instead.
func (*UserInfo) Descriptor() ([]byte, []int) {
	return file_community_v1_usercenter_proto_rawDescGZIP(), []int{8}
}

func (x *UserInfo) GetNickName() string {
	if x != nil {
		return x.NickName
	}
	return ""
}

func (x *UserInfo) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *UserInfo) GetWikiFxNumber() string {
	if x != nil {
		return x.WikiFxNumber
	}
	return ""
}

func (x *UserInfo) GetUserStatus() int32 {
	if x != nil {
		return x.UserStatus
	}
	return 0
}

func (x *UserInfo) GetUserIdentityNew() int32 {
	if x != nil {
		return x.UserIdentityNew
	}
	return 0
}

func (x *UserInfo) GetUserIdentityNewIcon() string {
	if x != nil {
		return x.UserIdentityNewIcon
	}
	return ""
}

func (x *UserInfo) GetIdentityType() int32 {
	if x != nil {
		return x.IdentityType
	}
	return 0
}

func (x *UserInfo) GetEnterpriseVIcon() string {
	if x != nil {
		return x.EnterpriseVIcon
	}
	return ""
}

func (x *UserInfo) GetEnterpriseVIcon2() string {
	if x != nil {
		return x.EnterpriseVIcon2
	}
	return ""
}

func (x *UserInfo) GetTagIcon() string {
	if x != nil {
		return x.TagIcon
	}
	return ""
}

func (x *UserInfo) GetEnterpriseName() string {
	if x != nil {
		return x.EnterpriseName
	}
	return ""
}

func (x *UserInfo) GetEnterpriseCode() string {
	if x != nil {
		return x.EnterpriseCode
	}
	return ""
}

func (x *UserInfo) GetEnterpriseUserLevel() int32 {
	if x != nil {
		return x.EnterpriseUserLevel
	}
	return 0
}

func (x *UserInfo) GetEnterpriseLogo() string {
	if x != nil {
		return x.EnterpriseLogo
	}
	return ""
}

func (x *UserInfo) GetEnterpriseIco() string {
	if x != nil {
		return x.EnterpriseIco
	}
	return ""
}

func (x *UserInfo) GetAvatarAddress() string {
	if x != nil {
		return x.AvatarAddress
	}
	return ""
}

func (x *UserInfo) GetOriginAvatarAddress() string {
	if x != nil {
		return x.OriginAvatarAddress
	}
	return ""
}

func (x *UserInfo) GetPosition() string {
	if x != nil {
		return x.Position
	}
	return ""
}

func (x *UserInfo) GetIsFollow() bool {
	if x != nil {
		return x.IsFollow
	}
	return false
}

func (x *UserInfo) GetEnterpriseType() int32 {
	if x != nil {
		return x.EnterpriseType
	}
	return 0
}

func (x *UserInfo) GetAttentionStauts() int32 {
	if x != nil {
		return x.AttentionStauts
	}
	return 0
}

func (x *UserInfo) GetFollowCount() int32 {
	if x != nil {
		return x.FollowCount
	}
	return 0
}

func (x *UserInfo) GetFansCount() int32 {
	if x != nil {
		return x.FansCount
	}
	return 0
}

func (x *UserInfo) GetApplaudCount() int32 {
	if x != nil {
		return x.ApplaudCount
	}
	return 0
}

func (x *UserInfo) GetStaffTag() string {
	if x != nil {
		return x.StaffTag
	}
	return ""
}

func (x *UserInfo) GetDetailBgImg() string {
	if x != nil {
		return x.DetailBgImg
	}
	return ""
}

func (x *UserInfo) GetOriginNickName() string {
	if x != nil {
		return x.OriginNickName
	}
	return ""
}

func (x *UserInfo) GetRegistrationTime() string {
	if x != nil {
		return x.RegistrationTime
	}
	return ""
}

func (x *UserInfo) GetCountryCode() string {
	if x != nil {
		return x.CountryCode
	}
	return ""
}

func (x *UserInfo) GetAreaCode() string {
	if x != nil {
		return x.AreaCode
	}
	return ""
}

func (x *UserInfo) GetPhoneNumber() string {
	if x != nil {
		return x.PhoneNumber
	}
	return ""
}

func (x *UserInfo) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *UserInfo) GetIsAuth() bool {
	if x != nil {
		return x.IsAuth
	}
	return false
}

func (x *UserInfo) GetKolIcon() string {
	if x != nil {
		return x.KolIcon
	}
	return ""
}

func (x *UserInfo) GetRegisterLong() string {
	if x != nil {
		return x.RegisterLong
	}
	return ""
}

func (x *UserInfo) GetIsFirmoffer() bool {
	if x != nil {
		return x.IsFirmoffer
	}
	return false
}

func (x *UserInfo) GetDarenIcon() string {
	if x != nil {
		return x.DarenIcon
	}
	return ""
}

func (x *UserInfo) GetOfficial() string {
	if x != nil {
		return x.Official
	}
	return ""
}

func (x *UserInfo) GetOfficialColor() string {
	if x != nil {
		return x.OfficialColor
	}
	return ""
}

func (x *UserInfo) GetTagWords() string {
	if x != nil {
		return x.TagWords
	}
	return ""
}

func (x *UserInfo) GetNickNameColor() string {
	if x != nil {
		return x.NickNameColor
	}
	return ""
}

func (x *UserInfo) GetOriginId() string {
	if x != nil {
		return x.OriginId
	}
	return ""
}

func (x *UserInfo) GetOfficialNumberType() int32 {
	if x != nil {
		return x.OfficialNumberType
	}
	return 0
}

type GetUsersReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Message []*UserInfo `protobuf:"bytes,1,rep,name=message,json=message,proto3" json:"message"`
}

func (x *GetUsersReply) Reset() {
	*x = GetUsersReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_community_v1_usercenter_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUsersReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUsersReply) ProtoMessage() {}

func (x *GetUsersReply) ProtoReflect() protoreflect.Message {
	mi := &file_community_v1_usercenter_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUsersReply.ProtoReflect.Descriptor instead.
func (*GetUsersReply) Descriptor() ([]byte, []int) {
	return file_community_v1_usercenter_proto_rawDescGZIP(), []int{9}
}

func (x *GetUsersReply) GetMessage() []*UserInfo {
	if x != nil {
		return x.Message
	}
	return nil
}

var File_community_v1_usercenter_proto protoreflect.FileDescriptor

var file_community_v1_usercenter_proto_rawDesc = []byte{
	0x0a, 0x1d, 0x63, 0x6f, 0x6d, 0x6d, 0x75, 0x6e, 0x69, 0x74, 0x79, 0x2f, 0x76, 0x31, 0x2f, 0x75,
	0x73, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x12, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x2e, 0x76, 0x31, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f,
	0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x2d, 0x67, 0x65, 0x6e, 0x2d, 0x6f, 0x70,
	0x65, 0x6e, 0x61, 0x70, 0x69, 0x76, 0x32, 0x2f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f,
	0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x19, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2f, 0x61, 0x6e, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x2e, 0x0a, 0x14,
	0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x46, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x22, 0x74, 0x0a, 0x16,
	0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x46, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x61, 0x74, 0x61, 0x49, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x61, 0x74, 0x61, 0x49, 0x64, 0x12, 0x42,
	0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x63, 0x65, 0x6e, 0x74,
	0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x66, 0x66, 0x69, 0x63, 0x69, 0x61, 0x6c, 0x4e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x54, 0x79,
	0x70, 0x65, 0x22, 0x54, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x46, 0x6f, 0x6c,
	0x6c, 0x6f, 0x77, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x3e, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65,
	0x72, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x55,
	0x73, 0x65, 0x72, 0x46, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x49, 0x74,
	0x65, 0x6d, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x32, 0x0a, 0x20, 0x47, 0x65, 0x74, 0x4f,
	0x66, 0x66, 0x69, 0x63, 0x69, 0x61, 0x6c, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x54, 0x79, 0x70,
	0x65, 0x42, 0x79, 0x49, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x22, 0x64, 0x0a, 0x1e,
	0x47, 0x65, 0x74, 0x4f, 0x66, 0x66, 0x69, 0x63, 0x69, 0x61, 0x6c, 0x4e, 0x75, 0x6d, 0x62, 0x65,
	0x72, 0x54, 0x79, 0x70, 0x65, 0x42, 0x79, 0x49, 0x64, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x42,
	0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x63, 0x65, 0x6e, 0x74,
	0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x66, 0x66, 0x69, 0x63, 0x69, 0x61, 0x6c, 0x4e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x54, 0x79,
	0x70, 0x65, 0x22, 0x37, 0x0a, 0x21, 0x47, 0x65, 0x74, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72,
	0x69, 0x73, 0x65, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x73, 0x42, 0x79, 0x43, 0x6f, 0x64, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x22, 0x3b, 0x0a, 0x1f, 0x47,
	0x65, 0x74, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x55, 0x73, 0x65, 0x72,
	0x49, 0x64, 0x73, 0x42, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x18,
	0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x07, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x73, 0x22, 0xb7, 0x01, 0x0a, 0x0f, 0x47, 0x65, 0x74,
	0x55, 0x73, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x20, 0x0a, 0x0b,
	0x75, 0x73, 0x65, 0x72, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x75, 0x73, 0x65, 0x72, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x49, 0x64, 0x12, 0x22,
	0x0a, 0x0c, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x43, 0x6f,
	0x64, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x54, 0x79,
	0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x54, 0x79, 0x70, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64,
	0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x73,
	0x12, 0x20, 0x0a, 0x0b, 0x74, 0x72, 0x61, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x73, 0x18,
	0x05, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0b, 0x74, 0x72, 0x61, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x64,
	0x65, 0x73, 0x22, 0xf6, 0x0b, 0x0a, 0x08, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x1a, 0x0a, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x75,
	0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65,
	0x72, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x0c, 0x77, 0x69, 0x6b, 0x69, 0x46, 0x78, 0x4e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x77, 0x69, 0x6b, 0x69, 0x46,
	0x78, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x1e, 0x0a, 0x0a, 0x75, 0x73, 0x65, 0x72, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x75, 0x73, 0x65,
	0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x28, 0x0a, 0x0f, 0x75, 0x73, 0x65, 0x72, 0x49,
	0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x4e, 0x65, 0x77, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0f, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x4e, 0x65,
	0x77, 0x12, 0x30, 0x0a, 0x13, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74,
	0x79, 0x4e, 0x65, 0x77, 0x49, 0x63, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13,
	0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x4e, 0x65, 0x77, 0x49,
	0x63, 0x6f, 0x6e, 0x12, 0x22, 0x0a, 0x0c, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x54,
	0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x69, 0x64, 0x65, 0x6e, 0x74,
	0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x28, 0x0a, 0x0f, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x70, 0x72, 0x69, 0x73, 0x65, 0x56, 0x49, 0x63, 0x6f, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0f, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x56, 0x49, 0x63, 0x6f,
	0x6e, 0x12, 0x2a, 0x0a, 0x10, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x56,
	0x49, 0x63, 0x6f, 0x6e, 0x32, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x65, 0x6e, 0x74,
	0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x56, 0x49, 0x63, 0x6f, 0x6e, 0x32, 0x12, 0x18, 0x0a,
	0x07, 0x74, 0x61, 0x67, 0x49, 0x63, 0x6f, 0x6e, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x74, 0x61, 0x67, 0x49, 0x63, 0x6f, 0x6e, 0x12, 0x26, 0x0a, 0x0e, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x70, 0x72, 0x69, 0x73, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x26, 0x0a, 0x0e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x43, 0x6f, 0x64,
	0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72,
	0x69, 0x73, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x30, 0x0a, 0x13, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x70, 0x72, 0x69, 0x73, 0x65, 0x55, 0x73, 0x65, 0x72, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x0d,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x13, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65,
	0x55, 0x73, 0x65, 0x72, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x26, 0x0a, 0x0e, 0x65, 0x6e, 0x74,
	0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x4c, 0x6f, 0x67, 0x6f, 0x18, 0x0e, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x4c, 0x6f, 0x67,
	0x6f, 0x12, 0x24, 0x0a, 0x0d, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x49,
	0x63, 0x6f, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70,
	0x72, 0x69, 0x73, 0x65, 0x49, 0x63, 0x6f, 0x12, 0x24, 0x0a, 0x0d, 0x61, 0x76, 0x61, 0x74, 0x61,
	0x72, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d,
	0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x30, 0x0a,
	0x13, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72, 0x41, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x6f, 0x72, 0x69, 0x67,
	0x69, 0x6e, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12,
	0x1a, 0x0a, 0x08, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x12, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x69,
	0x73, 0x46, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x18, 0x13, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x69,
	0x73, 0x46, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x12, 0x26, 0x0a, 0x0e, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x70, 0x72, 0x69, 0x73, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x14, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x28, 0x0a, 0x0f, 0x61, 0x74, 0x74, 0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x75,
	0x74, 0x73, 0x18, 0x15, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0f, 0x61, 0x74, 0x74, 0x65, 0x6e, 0x74,
	0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x75, 0x74, 0x73, 0x12, 0x20, 0x0a, 0x0b, 0x66, 0x6f, 0x6c,
	0x6c, 0x6f, 0x77, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x16, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b,
	0x66, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x66,
	0x61, 0x6e, 0x73, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x17, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09,
	0x66, 0x61, 0x6e, 0x73, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x22, 0x0a, 0x0c, 0x61, 0x70, 0x70,
	0x6c, 0x61, 0x75, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x18, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0c, 0x61, 0x70, 0x70, 0x6c, 0x61, 0x75, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1a, 0x0a,
	0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x54, 0x61, 0x67, 0x18, 0x19, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x54, 0x61, 0x67, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x42, 0x67, 0x49, 0x6d, 0x67, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x42, 0x67, 0x49, 0x6d, 0x67, 0x12, 0x26, 0x0a, 0x0e, 0x6f,
	0x72, 0x69, 0x67, 0x69, 0x6e, 0x4e, 0x69, 0x63, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x1b, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0e, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x4e, 0x69, 0x63, 0x6b, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x2a, 0x0a, 0x10, 0x72, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x72,
	0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x12,
	0x20, 0x0a, 0x0b, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x1d,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x43, 0x6f, 0x64,
	0x65, 0x12, 0x1a, 0x0a, 0x08, 0x61, 0x72, 0x65, 0x61, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x1e, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x61, 0x72, 0x65, 0x61, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x20, 0x0a,
	0x0b, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x1f, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12,
	0x14, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x20, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x65, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x16, 0x0a, 0x06, 0x69, 0x73, 0x41, 0x75, 0x74, 0x68, 0x18,
	0x21, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x69, 0x73, 0x41, 0x75, 0x74, 0x68, 0x12, 0x18, 0x0a,
	0x07, 0x6b, 0x6f, 0x6c, 0x49, 0x63, 0x6f, 0x6e, 0x18, 0x22, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x6b, 0x6f, 0x6c, 0x49, 0x63, 0x6f, 0x6e, 0x12, 0x22, 0x0a, 0x0c, 0x72, 0x65, 0x67, 0x69, 0x73,
	0x74, 0x65, 0x72, 0x4c, 0x6f, 0x6e, 0x67, 0x18, 0x23, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x72,
	0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x4c, 0x6f, 0x6e, 0x67, 0x12, 0x20, 0x0a, 0x0b, 0x69,
	0x73, 0x46, 0x69, 0x72, 0x6d, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x18, 0x24, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x0b, 0x69, 0x73, 0x46, 0x69, 0x72, 0x6d, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x12, 0x1c, 0x0a,
	0x09, 0x64, 0x61, 0x72, 0x65, 0x6e, 0x49, 0x63, 0x6f, 0x6e, 0x18, 0x25, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x64, 0x61, 0x72, 0x65, 0x6e, 0x49, 0x63, 0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x6f,
	0x66, 0x66, 0x69, 0x63, 0x69, 0x61, 0x6c, 0x18, 0x26, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6f,
	0x66, 0x66, 0x69, 0x63, 0x69, 0x61, 0x6c, 0x12, 0x24, 0x0a, 0x0d, 0x6f, 0x66, 0x66, 0x69, 0x63,
	0x69, 0x61, 0x6c, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x27, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d,
	0x6f, 0x66, 0x66, 0x69, 0x63, 0x69, 0x61, 0x6c, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x1a, 0x0a,
	0x08, 0x54, 0x61, 0x67, 0x57, 0x6f, 0x72, 0x64, 0x73, 0x18, 0x28, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x54, 0x61, 0x67, 0x57, 0x6f, 0x72, 0x64, 0x73, 0x12, 0x24, 0x0a, 0x0d, 0x4e, 0x69, 0x63,
	0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x29, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0d, 0x4e, 0x69, 0x63, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12,
	0x1a, 0x0a, 0x08, 0x4f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x49, 0x64, 0x18, 0x2a, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x4f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x49, 0x64, 0x12, 0x2e, 0x0a, 0x12, 0x6f,
	0x66, 0x66, 0x69, 0x63, 0x69, 0x61, 0x6c, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x54, 0x79, 0x70,
	0x65, 0x18, 0x2b, 0x20, 0x01, 0x28, 0x05, 0x52, 0x12, 0x6f, 0x66, 0x66, 0x69, 0x63, 0x69, 0x61,
	0x6c, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x22, 0x47, 0x0a, 0x0d, 0x47,
	0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x36, 0x0a, 0x07,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e,
	0x76, 0x31, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x07, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x2a, 0xb2, 0x01, 0x0a, 0x12, 0x4f, 0x66, 0x66, 0x69, 0x63, 0x69, 0x61,
	0x6c, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x16, 0x4f,
	0x66, 0x66, 0x69, 0x63, 0x69, 0x61, 0x6c, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x5f, 0x55, 0x6e,
	0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x54, 0x72, 0x61, 0x64, 0x65,
	0x72, 0x10, 0x01, 0x12, 0x11, 0x0a, 0x0d, 0x57, 0x69, 0x6b, 0x69, 0x46, 0x58, 0x4d, 0x65, 0x64,
	0x69, 0x61, 0x74, 0x65, 0x10, 0x02, 0x12, 0x0e, 0x0a, 0x0a, 0x57, 0x69, 0x6b, 0x69, 0x46, 0x58,
	0x4e, 0x65, 0x77, 0x73, 0x10, 0x03, 0x12, 0x11, 0x0a, 0x0d, 0x57, 0x69, 0x6b, 0x69, 0x46, 0x58,
	0x45, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x10, 0x04, 0x12, 0x10, 0x0a, 0x0c, 0x57, 0x69, 0x6b,
	0x69, 0x46, 0x58, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x10, 0x05, 0x12, 0x13, 0x0a, 0x0f, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x50, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x10, 0x06,
	0x12, 0x0d, 0x0a, 0x09, 0x52, 0x65, 0x67, 0x75, 0x6c, 0x61, 0x74, 0x6f, 0x72, 0x10, 0x07, 0x12,
	0x08, 0x0a, 0x04, 0x55, 0x73, 0x65, 0x72, 0x10, 0x08, 0x32, 0xa9, 0x05, 0x0a, 0x0a, 0x55, 0x73,
	0x65, 0x72, 0x43, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x12, 0x89, 0x01, 0x0a, 0x0c, 0x47, 0x65, 0x74,
	0x55, 0x73, 0x65, 0x72, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x75, 0x73, 0x65, 0x72, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47,
	0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x21,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x73, 0x52, 0x65, 0x70, 0x6c,
	0x79, 0x22, 0x31, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x2b, 0x3a, 0x01, 0x2a, 0x22, 0x26, 0x2f, 0x76,
	0x31, 0x2f, 0x61, 0x70, 0x70, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x2f, 0x67, 0x65, 0x74, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x63, 0x61, 0x72, 0x64,
	0x6c, 0x69, 0x73, 0x74, 0x12, 0xc2, 0x01, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x45, 0x6e, 0x74, 0x65,
	0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x73, 0x42, 0x79, 0x43,
	0x6f, 0x64, 0x65, 0x12, 0x35, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x63,
	0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x6e, 0x74, 0x65,
	0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x73, 0x42, 0x79, 0x43,
	0x6f, 0x64, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x33, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e,
	0x47, 0x65, 0x74, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x55, 0x73, 0x65,
	0x72, 0x49, 0x64, 0x73, 0x42, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22,
	0x38, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x32, 0x3a, 0x01, 0x2a, 0x22, 0x2d, 0x2f, 0x76, 0x31, 0x2f,
	0x61, 0x70, 0x70, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2f, 0x67,
	0x65, 0x74, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x75, 0x73, 0x65, 0x72,
	0x69, 0x64, 0x73, 0x62, 0x79, 0x63, 0x6f, 0x64, 0x65, 0x12, 0xbb, 0x01, 0x0a, 0x19, 0x47, 0x65,
	0x74, 0x4f, 0x66, 0x66, 0x69, 0x63, 0x69, 0x61, 0x6c, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x54,
	0x79, 0x70, 0x65, 0x42, 0x79, 0x49, 0x64, 0x12, 0x34, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73,
	0x65, 0x72, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74,
	0x4f, 0x66, 0x66, 0x69, 0x63, 0x69, 0x61, 0x6c, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x54, 0x79,
	0x70, 0x65, 0x42, 0x79, 0x49, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x32, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e,
	0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4f, 0x66, 0x66, 0x69, 0x63, 0x69, 0x61, 0x6c, 0x4e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x42, 0x79, 0x49, 0x64, 0x52, 0x65, 0x70, 0x6c,
	0x79, 0x22, 0x34, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x2e, 0x12, 0x2c, 0x2f, 0x76, 0x31, 0x2f, 0x61,
	0x70, 0x70, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2f, 0x67, 0x65,
	0x74, 0x6f, 0x66, 0x66, 0x69, 0x63, 0x69, 0x61, 0x6c, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x74,
	0x79, 0x70, 0x65, 0x62, 0x79, 0x69, 0x64, 0x12, 0x8b, 0x01, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x55,
	0x73, 0x65, 0x72, 0x46, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x12, 0x28, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x75, 0x73, 0x65, 0x72, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47,
	0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x46, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x63,
	0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72,
	0x46, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x28, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x22, 0x12, 0x20, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x70, 0x70, 0x2f, 0x75, 0x73, 0x65,
	0x72, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2f, 0x67, 0x65, 0x74, 0x75, 0x73, 0x65, 0x72, 0x66,
	0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x42, 0x23, 0x5a, 0x21, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x63, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x63, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x3b, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_community_v1_usercenter_proto_rawDescOnce sync.Once
	file_community_v1_usercenter_proto_rawDescData = file_community_v1_usercenter_proto_rawDesc
)

func file_community_v1_usercenter_proto_rawDescGZIP() []byte {
	file_community_v1_usercenter_proto_rawDescOnce.Do(func() {
		file_community_v1_usercenter_proto_rawDescData = protoimpl.X.CompressGZIP(file_community_v1_usercenter_proto_rawDescData)
	})
	return file_community_v1_usercenter_proto_rawDescData
}

var file_community_v1_usercenter_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_community_v1_usercenter_proto_msgTypes = make([]protoimpl.MessageInfo, 10)
var file_community_v1_usercenter_proto_goTypes = []interface{}{
	(OfficialNumberType)(0),                   // 0: api.user_center.v1.OfficialNumberType
	(*GetUserFollowRequest)(nil),              // 1: api.user_center.v1.GetUserFollowRequest
	(*GetUserFollowReplyItem)(nil),            // 2: api.user_center.v1.GetUserFollowReplyItem
	(*GetUserFollowReply)(nil),                // 3: api.user_center.v1.GetUserFollowReply
	(*GetOfficialNumberTypeByIdRequest)(nil),  // 4: api.user_center.v1.GetOfficialNumberTypeByIdRequest
	(*GetOfficialNumberTypeByIdReply)(nil),    // 5: api.user_center.v1.GetOfficialNumberTypeByIdReply
	(*GetEnterpriseUserIdsByCodeRequest)(nil), // 6: api.user_center.v1.GetEnterpriseUserIdsByCodeRequest
	(*GetEnterpriseUserIdsByCodeReply)(nil),   // 7: api.user_center.v1.GetEnterpriseUserIdsByCodeReply
	(*GetUsersRequest)(nil),                   // 8: api.user_center.v1.GetUsersRequest
	(*UserInfo)(nil),                          // 9: api.user_center.v1.UserInfo
	(*GetUsersReply)(nil),                     // 10: api.user_center.v1.GetUsersReply
}
var file_community_v1_usercenter_proto_depIdxs = []int32{
	0,  // 0: api.user_center.v1.GetUserFollowReplyItem.userType:type_name -> api.user_center.v1.OfficialNumberType
	2,  // 1: api.user_center.v1.GetUserFollowReply.list:type_name -> api.user_center.v1.GetUserFollowReplyItem
	0,  // 2: api.user_center.v1.GetOfficialNumberTypeByIdReply.userType:type_name -> api.user_center.v1.OfficialNumberType
	9,  // 3: api.user_center.v1.GetUsersReply.message:type_name -> api.user_center.v1.UserInfo
	8,  // 4: api.user_center.v1.UserCenter.GetUsersInfo:input_type -> api.user_center.v1.GetUsersRequest
	6,  // 5: api.user_center.v1.UserCenter.GetEnterpriseUserIdsByCode:input_type -> api.user_center.v1.GetEnterpriseUserIdsByCodeRequest
	4,  // 6: api.user_center.v1.UserCenter.GetOfficialNumberTypeById:input_type -> api.user_center.v1.GetOfficialNumberTypeByIdRequest
	1,  // 7: api.user_center.v1.UserCenter.GetUserFollow:input_type -> api.user_center.v1.GetUserFollowRequest
	10, // 8: api.user_center.v1.UserCenter.GetUsersInfo:output_type -> api.user_center.v1.GetUsersReply
	7,  // 9: api.user_center.v1.UserCenter.GetEnterpriseUserIdsByCode:output_type -> api.user_center.v1.GetEnterpriseUserIdsByCodeReply
	5,  // 10: api.user_center.v1.UserCenter.GetOfficialNumberTypeById:output_type -> api.user_center.v1.GetOfficialNumberTypeByIdReply
	3,  // 11: api.user_center.v1.UserCenter.GetUserFollow:output_type -> api.user_center.v1.GetUserFollowReply
	8,  // [8:12] is the sub-list for method output_type
	4,  // [4:8] is the sub-list for method input_type
	4,  // [4:4] is the sub-list for extension type_name
	4,  // [4:4] is the sub-list for extension extendee
	0,  // [0:4] is the sub-list for field type_name
}

func init() { file_community_v1_usercenter_proto_init() }
func file_community_v1_usercenter_proto_init() {
	if File_community_v1_usercenter_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_community_v1_usercenter_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserFollowRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_community_v1_usercenter_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserFollowReplyItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_community_v1_usercenter_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserFollowReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_community_v1_usercenter_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetOfficialNumberTypeByIdRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_community_v1_usercenter_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetOfficialNumberTypeByIdReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_community_v1_usercenter_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetEnterpriseUserIdsByCodeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_community_v1_usercenter_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetEnterpriseUserIdsByCodeReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_community_v1_usercenter_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUsersRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_community_v1_usercenter_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_community_v1_usercenter_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUsersReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_community_v1_usercenter_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   10,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_community_v1_usercenter_proto_goTypes,
		DependencyIndexes: file_community_v1_usercenter_proto_depIdxs,
		EnumInfos:         file_community_v1_usercenter_proto_enumTypes,
		MessageInfos:      file_community_v1_usercenter_proto_msgTypes,
	}.Build()
	File_community_v1_usercenter_proto = out.File
	file_community_v1_usercenter_proto_rawDesc = nil
	file_community_v1_usercenter_proto_goTypes = nil
	file_community_v1_usercenter_proto_depIdxs = nil
}
