// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.0
// - protoc             v5.27.3
// source: community/v1/usercenter.proto

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationUserCenterGetEnterpriseUserIdsByCode = "/api.user_center.v1.UserCenter/GetEnterpriseUserIdsByCode"
const OperationUserCenterGetOfficialNumberTypeById = "/api.user_center.v1.UserCenter/GetOfficialNumberTypeById"
const OperationUserCenterGetUserFollow = "/api.user_center.v1.UserCenter/GetUserFollow"
const OperationUserCenterGetUsersInfo = "/api.user_center.v1.UserCenter/GetUsersInfo"

type UserCenterHTTPServer interface {
	// GetEnterpriseUserIdsByCode根据企业code获取 企业员工和管理员userid
	GetEnterpriseUserIdsByCode(context.Context, *GetEnterpriseUserIdsByCodeRequest) (*GetEnterpriseUserIdsByCodeReply, error)
	// GetOfficialNumberTypeById 根据id获取企业类型
	GetOfficialNumberTypeById(context.Context, *GetOfficialNumberTypeByIdRequest) (*GetOfficialNumberTypeByIdReply, error)
	// GetUserFollow 获取用户关注对象
	GetUserFollow(context.Context, *GetUserFollowRequest) (*GetUserFollowReply, error)
	GetUsersInfo(context.Context, *GetUsersRequest) (*GetUsersReply, error)
}

func RegisterUserCenterHTTPServer(s *http.Server, srv UserCenterHTTPServer) {
	r := s.Route("/")
	r.POST("/v1/app/usercenter/getbusinesscardlist", _UserCenter_GetUsersInfo0_HTTP_Handler(srv))
	r.POST("/v1/app/usercenter/getenterpriseuseridsbycode", _UserCenter_GetEnterpriseUserIdsByCode0_HTTP_Handler(srv))
	r.GET("/v1/app/usercenter/getofficialnumbertypebyid", _UserCenter_GetOfficialNumberTypeById0_HTTP_Handler(srv))
	r.GET("/v1/app/usercenter/getuserfollow", _UserCenter_GetUserFollow0_HTTP_Handler(srv))
}

func _UserCenter_GetUsersInfo0_HTTP_Handler(srv UserCenterHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetUsersRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUserCenterGetUsersInfo)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetUsersInfo(ctx, req.(*GetUsersRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetUsersReply)
		return ctx.Result(200, reply)
	}
}

func _UserCenter_GetEnterpriseUserIdsByCode0_HTTP_Handler(srv UserCenterHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetEnterpriseUserIdsByCodeRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUserCenterGetEnterpriseUserIdsByCode)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetEnterpriseUserIdsByCode(ctx, req.(*GetEnterpriseUserIdsByCodeRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetEnterpriseUserIdsByCodeReply)
		return ctx.Result(200, reply)
	}
}

func _UserCenter_GetOfficialNumberTypeById0_HTTP_Handler(srv UserCenterHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetOfficialNumberTypeByIdRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUserCenterGetOfficialNumberTypeById)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetOfficialNumberTypeById(ctx, req.(*GetOfficialNumberTypeByIdRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetOfficialNumberTypeByIdReply)
		return ctx.Result(200, reply)
	}
}

func _UserCenter_GetUserFollow0_HTTP_Handler(srv UserCenterHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetUserFollowRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUserCenterGetUserFollow)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetUserFollow(ctx, req.(*GetUserFollowRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetUserFollowReply)
		return ctx.Result(200, reply)
	}
}

type UserCenterHTTPClient interface {
	GetEnterpriseUserIdsByCode(ctx context.Context, req *GetEnterpriseUserIdsByCodeRequest, opts ...http.CallOption) (rsp *GetEnterpriseUserIdsByCodeReply, err error)
	GetOfficialNumberTypeById(ctx context.Context, req *GetOfficialNumberTypeByIdRequest, opts ...http.CallOption) (rsp *GetOfficialNumberTypeByIdReply, err error)
	GetUserFollow(ctx context.Context, req *GetUserFollowRequest, opts ...http.CallOption) (rsp *GetUserFollowReply, err error)
	GetUsersInfo(ctx context.Context, req *GetUsersRequest, opts ...http.CallOption) (rsp *GetUsersReply, err error)
}

type UserCenterHTTPClientImpl struct {
	cc *http.Client
}

func NewUserCenterHTTPClient(client *http.Client) UserCenterHTTPClient {
	return &UserCenterHTTPClientImpl{client}
}

func (c *UserCenterHTTPClientImpl) GetEnterpriseUserIdsByCode(ctx context.Context, in *GetEnterpriseUserIdsByCodeRequest, opts ...http.CallOption) (*GetEnterpriseUserIdsByCodeReply, error) {
	var out GetEnterpriseUserIdsByCodeReply
	pattern := "/v1/app/usercenter/getenterpriseuseridsbycode"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationUserCenterGetEnterpriseUserIdsByCode))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *UserCenterHTTPClientImpl) GetOfficialNumberTypeById(ctx context.Context, in *GetOfficialNumberTypeByIdRequest, opts ...http.CallOption) (*GetOfficialNumberTypeByIdReply, error) {
	var out GetOfficialNumberTypeByIdReply
	pattern := "/v1/app/usercenter/getofficialnumbertypebyid"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationUserCenterGetOfficialNumberTypeById))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *UserCenterHTTPClientImpl) GetUserFollow(ctx context.Context, in *GetUserFollowRequest, opts ...http.CallOption) (*GetUserFollowReply, error) {
	var out GetUserFollowReply
	pattern := "/v1/app/usercenter/getuserfollow"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationUserCenterGetUserFollow))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *UserCenterHTTPClientImpl) GetUsersInfo(ctx context.Context, in *GetUsersRequest, opts ...http.CallOption) (*GetUsersReply, error) {
	var out GetUsersReply
	pattern := "/v1/app/usercenter/getbusinesscardlist"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationUserCenterGetUsersInfo))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
