// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: community/v1/models.proto

package v1

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on GetUserInfoRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetUserInfoRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetUserInfoRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetUserInfoRequestMultiError, or nil if none found.
func (m *GetUserInfoRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetUserInfoRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return GetUserInfoRequestMultiError(errors)
	}

	return nil
}

// GetUserInfoRequestMultiError is an error wrapping multiple validation errors
// returned by GetUserInfoRequest.ValidateAll() if the designated constraints
// aren't met.
type GetUserInfoRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetUserInfoRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetUserInfoRequestMultiError) AllErrors() []error { return m }

// GetUserInfoRequestValidationError is the validation error returned by
// GetUserInfoRequest.Validate if the designated constraints aren't met.
type GetUserInfoRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetUserInfoRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetUserInfoRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetUserInfoRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetUserInfoRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetUserInfoRequestValidationError) ErrorName() string {
	return "GetUserInfoRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetUserInfoRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetUserInfoRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetUserInfoRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetUserInfoRequestValidationError{}

// Validate checks the field values on GetUserInfoReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetUserInfoReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetUserInfoReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetUserInfoReplyMultiError, or nil if none found.
func (m *GetUserInfoReply) ValidateAll() error {
	return m.validate(true)
}

func (m *GetUserInfoReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for UserId

	if len(errors) > 0 {
		return GetUserInfoReplyMultiError(errors)
	}

	return nil
}

// GetUserInfoReplyMultiError is an error wrapping multiple validation errors
// returned by GetUserInfoReply.ValidateAll() if the designated constraints
// aren't met.
type GetUserInfoReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetUserInfoReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetUserInfoReplyMultiError) AllErrors() []error { return m }

// GetUserInfoReplyValidationError is the validation error returned by
// GetUserInfoReply.Validate if the designated constraints aren't met.
type GetUserInfoReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetUserInfoReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetUserInfoReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetUserInfoReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetUserInfoReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetUserInfoReplyValidationError) ErrorName() string { return "GetUserInfoReplyValidationError" }

// Error satisfies the builtin error interface
func (e GetUserInfoReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetUserInfoReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetUserInfoReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetUserInfoReplyValidationError{}

// Validate checks the field values on UpdateUserRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *UpdateUserRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateUserRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateUserRequestMultiError, or nil if none found.
func (m *UpdateUserRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateUserRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return UpdateUserRequestMultiError(errors)
	}

	return nil
}

// UpdateUserRequestMultiError is an error wrapping multiple validation errors
// returned by UpdateUserRequest.ValidateAll() if the designated constraints
// aren't met.
type UpdateUserRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateUserRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateUserRequestMultiError) AllErrors() []error { return m }

// UpdateUserRequestValidationError is the validation error returned by
// UpdateUserRequest.Validate if the designated constraints aren't met.
type UpdateUserRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateUserRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateUserRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateUserRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateUserRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateUserRequestValidationError) ErrorName() string {
	return "UpdateUserRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateUserRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateUserRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateUserRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateUserRequestValidationError{}

// Validate checks the field values on UpdateUserReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *UpdateUserReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateUserReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateUserReplyMultiError, or nil if none found.
func (m *UpdateUserReply) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateUserReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return UpdateUserReplyMultiError(errors)
	}

	return nil
}

// UpdateUserReplyMultiError is an error wrapping multiple validation errors
// returned by UpdateUserReply.ValidateAll() if the designated constraints
// aren't met.
type UpdateUserReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateUserReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateUserReplyMultiError) AllErrors() []error { return m }

// UpdateUserReplyValidationError is the validation error returned by
// UpdateUserReply.Validate if the designated constraints aren't met.
type UpdateUserReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateUserReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateUserReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateUserReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateUserReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateUserReplyValidationError) ErrorName() string { return "UpdateUserReplyValidationError" }

// Error satisfies the builtin error interface
func (e UpdateUserReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateUserReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateUserReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateUserReplyValidationError{}

// Validate checks the field values on StringReplyRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *StringReplyRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StringReplyRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// StringReplyRequestMultiError, or nil if none found.
func (m *StringReplyRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *StringReplyRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return StringReplyRequestMultiError(errors)
	}

	return nil
}

// StringReplyRequestMultiError is an error wrapping multiple validation errors
// returned by StringReplyRequest.ValidateAll() if the designated constraints
// aren't met.
type StringReplyRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StringReplyRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StringReplyRequestMultiError) AllErrors() []error { return m }

// StringReplyRequestValidationError is the validation error returned by
// StringReplyRequest.Validate if the designated constraints aren't met.
type StringReplyRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StringReplyRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StringReplyRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StringReplyRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StringReplyRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StringReplyRequestValidationError) ErrorName() string {
	return "StringReplyRequestValidationError"
}

// Error satisfies the builtin error interface
func (e StringReplyRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStringReplyRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StringReplyRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StringReplyRequestValidationError{}

// Validate checks the field values on StringReplyReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *StringReplyReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StringReplyReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// StringReplyReplyMultiError, or nil if none found.
func (m *StringReplyReply) ValidateAll() error {
	return m.validate(true)
}

func (m *StringReplyReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return StringReplyReplyMultiError(errors)
	}

	return nil
}

// StringReplyReplyMultiError is an error wrapping multiple validation errors
// returned by StringReplyReply.ValidateAll() if the designated constraints
// aren't met.
type StringReplyReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StringReplyReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StringReplyReplyMultiError) AllErrors() []error { return m }

// StringReplyReplyValidationError is the validation error returned by
// StringReplyReply.Validate if the designated constraints aren't met.
type StringReplyReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StringReplyReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StringReplyReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StringReplyReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StringReplyReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StringReplyReplyValidationError) ErrorName() string { return "StringReplyReplyValidationError" }

// Error satisfies the builtin error interface
func (e StringReplyReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStringReplyReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StringReplyReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StringReplyReplyValidationError{}
