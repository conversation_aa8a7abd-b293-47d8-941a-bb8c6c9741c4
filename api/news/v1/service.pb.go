// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.25.3
// source: news/v1/service.proto

package v1

import (
	common "api-community/api/common"
	_ "github.com/grpc-ecosystem/grpc-gateway/v2/protoc-gen-openapiv2/options"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

var File_news_v1_service_proto protoreflect.FileDescriptor

var file_news_v1_service_proto_rawDesc = []byte{
	0x0a, 0x15, 0x6e, 0x65, 0x77, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0b, 0x61, 0x70, 0x69, 0x2e, 0x6e, 0x65, 0x77,
	0x73, 0x2e, 0x76, 0x31, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x2d, 0x67, 0x65, 0x6e, 0x2d, 0x6f,
	0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x76, 0x32, 0x2f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x13, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x14, 0x6e, 0x65, 0x77, 0x73, 0x2f, 0x76, 0x31,
	0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0xdc, 0x03,
	0x0a, 0x07, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x47, 0x0a, 0x07, 0x48, 0x65, 0x61,
	0x6c, 0x74, 0x68, 0x79, 0x12, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x6d,
	0x70, 0x74, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x14, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x48, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x79, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x22, 0x10, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x0a, 0x12, 0x08, 0x2f, 0x68, 0x65, 0x61, 0x6c, 0x74,
	0x68, 0x7a, 0x12, 0x76, 0x0a, 0x0b, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x77, 0x73, 0x42, 0x79, 0x49,
	0x64, 0x12, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6e, 0x65, 0x77, 0x73, 0x2e, 0x76, 0x31, 0x2e,
	0x47, 0x65, 0x74, 0x4e, 0x65, 0x77, 0x73, 0x42, 0x79, 0x49, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x15, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6e, 0x65, 0x77, 0x73, 0x2e, 0x76, 0x31,
	0x2e, 0x4e, 0x65, 0x77, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x2f, 0x92, 0x41, 0x1c, 0x0a, 0x06,
	0xe5, 0xbf, 0xab, 0xe8, 0xae, 0xaf, 0x12, 0x12, 0xe8, 0x8e, 0xb7, 0xe5, 0x8f, 0x96, 0xe5, 0xbf,
	0xab, 0xe8, 0xae, 0xaf, 0xe8, 0xaf, 0xa6, 0xe6, 0x83, 0x85, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x0a,
	0x12, 0x08, 0x2f, 0x76, 0x31, 0x2f, 0x6e, 0x65, 0x77, 0x73, 0x12, 0x7a, 0x0a, 0x08, 0x46, 0x69,
	0x6e, 0x64, 0x4e, 0x65, 0x77, 0x73, 0x12, 0x1c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6e, 0x65, 0x77,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x69, 0x6e, 0x64, 0x4e, 0x65, 0x77, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6e, 0x65, 0x77, 0x73, 0x2e,
	0x76, 0x31, 0x2e, 0x46, 0x69, 0x6e, 0x64, 0x4e, 0x65, 0x77, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x22, 0x34, 0x92, 0x41, 0x1c, 0x0a, 0x06, 0xe5, 0xbf, 0xab, 0xe8, 0xae, 0xaf, 0x12, 0x12, 0xe8,
	0x8e, 0xb7, 0xe5, 0x8f, 0x96, 0xe5, 0xbf, 0xab, 0xe8, 0xae, 0xaf, 0xe5, 0x88, 0x97, 0xe8, 0xa1,
	0xa8, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x0f, 0x12, 0x0d, 0x2f, 0x76, 0x31, 0x2f, 0x6e, 0x65, 0x77,
	0x73, 0x2f, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x93, 0x01, 0x0a, 0x0d, 0x46, 0x69, 0x6e, 0x64, 0x4e,
	0x65, 0x77, 0x73, 0x42, 0x79, 0x49, 0x64, 0x73, 0x12, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6e,
	0x65, 0x77, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x69, 0x6e, 0x64, 0x4e, 0x65, 0x77, 0x73, 0x42,
	0x79, 0x49, 0x64, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x6e, 0x65, 0x77, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x69, 0x6e, 0x64, 0x4e, 0x65,
	0x77, 0x73, 0x42, 0x79, 0x49, 0x64, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x3e, 0x92, 0x41,
	0x22, 0x0a, 0x06, 0xe5, 0xbf, 0xab, 0xe8, 0xae, 0xaf, 0x12, 0x18, 0xe6, 0x89, 0xb9, 0xe9, 0x87,
	0x8f, 0xe8, 0x8e, 0xb7, 0xe5, 0x8f, 0x96, 0xe5, 0xbf, 0xab, 0xe8, 0xae, 0xaf, 0xe5, 0x88, 0x97,
	0xe8, 0xa1, 0xa8, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x13, 0x3a, 0x01, 0x2a, 0x22, 0x0e, 0x2f, 0x76,
	0x31, 0x2f, 0x6e, 0x65, 0x77, 0x73, 0x2f, 0x62, 0x61, 0x74, 0x63, 0x68, 0x42, 0x10, 0x5a, 0x0e,
	0x61, 0x70, 0x69, 0x2f, 0x6e, 0x65, 0x77, 0x73, 0x2f, 0x76, 0x31, 0x3b, 0x76, 0x31, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var file_news_v1_service_proto_goTypes = []interface{}{
	(*common.EmptyRequest)(nil),  // 0: common.EmptyRequest
	(*GetNewsByIdRequest)(nil),   // 1: api.news.v1.GetNewsByIdRequest
	(*FindNewsRequest)(nil),      // 2: api.news.v1.FindNewsRequest
	(*FindNewsByIdsRequest)(nil), // 3: api.news.v1.FindNewsByIdsRequest
	(*common.HealthyReply)(nil),  // 4: common.HealthyReply
	(*NewsInfo)(nil),             // 5: api.news.v1.NewsInfo
	(*FindNewsReply)(nil),        // 6: api.news.v1.FindNewsReply
	(*FindNewsByIdsReply)(nil),   // 7: api.news.v1.FindNewsByIdsReply
}
var file_news_v1_service_proto_depIdxs = []int32{
	0, // 0: api.news.v1.Service.Healthy:input_type -> common.EmptyRequest
	1, // 1: api.news.v1.Service.GetNewsById:input_type -> api.news.v1.GetNewsByIdRequest
	2, // 2: api.news.v1.Service.FindNews:input_type -> api.news.v1.FindNewsRequest
	3, // 3: api.news.v1.Service.FindNewsByIds:input_type -> api.news.v1.FindNewsByIdsRequest
	4, // 4: api.news.v1.Service.Healthy:output_type -> common.HealthyReply
	5, // 5: api.news.v1.Service.GetNewsById:output_type -> api.news.v1.NewsInfo
	6, // 6: api.news.v1.Service.FindNews:output_type -> api.news.v1.FindNewsReply
	7, // 7: api.news.v1.Service.FindNewsByIds:output_type -> api.news.v1.FindNewsByIdsReply
	4, // [4:8] is the sub-list for method output_type
	0, // [0:4] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_news_v1_service_proto_init() }
func file_news_v1_service_proto_init() {
	if File_news_v1_service_proto != nil {
		return
	}
	file_news_v1_models_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_news_v1_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_news_v1_service_proto_goTypes,
		DependencyIndexes: file_news_v1_service_proto_depIdxs,
	}.Build()
	File_news_v1_service_proto = out.File
	file_news_v1_service_proto_rawDesc = nil
	file_news_v1_service_proto_goTypes = nil
	file_news_v1_service_proto_depIdxs = nil
}
