#  项目开发规范

## 🚨 核心强制规范（AI 必须严格遵守）
1. **必须严格遵守 [Uber Go Style Guide](https://github.com/uber-go/guide/blob/master/style.md)**
2. **分层错误处理策略不可违反**
3. **函数返回值规范：禁止返回 (nil, nil)**
4. **新建函数必须添加详细注释，修改函数专注逻辑实现**

## 📋 项目技术栈
- **框架栈**：Kratos v2 + gRPC + Wire注入 + GORM v2  
- **项目结构**：cmd/启动 + internal/业务 + api/proto + configs/配置 + pkg/工具
- **关键依赖**：`github.com/airunny/wiki-go-tools/errors`, `ormhelper`, `icontext`, `igorm`

## 💡 AI 代码生成指导原则
1. **优先使用 Guard Clauses**：减少嵌套，提前返回
2. **遵循参数顺序**：`ctx context.Context` 在前，`opts ...Option` 在后
3. **预分配切片容量**：已知大小时使用 `make([]T, 0, size)`
4. **结构化日志**：使用 `log.Context(ctx)` 记录技术错误

## 核心架构

## Uber Go 编码规范要点

### 命名规范
- **接口命名**：使用 `-er` 后缀 (Reader, Writer, Formatter)
- **包命名**：简短、小写、单数形式，避免下划线
- **变量命名**：在小作用域使用短名称 (`i`, `r`, `w`)，大作用域使用描述性名称
- **函数命名**：动词开头，驼峰命名

### 声明与初始化
```go
// 优先使用 var 声明零值
var user User

// 使用短声明赋值非零值
name := "John"

// 结构体初始化使用字段名
user := User{
    Name: "John",
    Age:  30,
}

// 避免裸参数，使用结构体
// Bad
func process(enable bool, timeout int, retries int)

// Good
type Config struct {
    Enable  bool
    Timeout time.Duration
    Retries int
}
func process(cfg Config)
```

### 错误处理（分层策略）
- **HTTP层**：仅此层返回 `github.com/airunny/wiki-go-tools/errors` 包装错误
- **Service层**：业务错误用 `errors.WithMessage()` 包装，技术错误直接返回
- **DAO层**：必须用 `ormhelper.WrapErr()` 包装，在service层处理 `gorm.ErrRecordNotFound`
- **返回值规范**：所有函数不可以返回 (nil, nil)，当 err=nil 时返回值不能为 nil

### 错误处理最佳实践
```go
// 错误信息应该简洁明了
if err != nil {
    return fmt.Errorf("failed to read file %s: %w", filename, err)
}

// 在一个地方处理错误
if err := f(); err != nil {
    // 记录或返回，但不要两者都做
    return err
}
```

### 上下文与工具类
- **用户获取**：`icontext.UserIdFrom(ctx)`
- **日志记录**：`log.Context(ctx)` 结构化日志
- **国际化**：`i18n.GetWithDefaultEnglish()` 多语言
- **事务支持**：`igorm.GetSession(ctx, db, opts...)` 和 `igorm.Option`

### 结构体与接口
```go
// 结构体字段按重要性排序，相关字段分组
type User struct {
    // 身份标识
    ID   string
    Name string

    // 时间戳
    CreatedAt time.Time
    UpdatedAt time.Time

    // 状态
    IsActive bool
}

// 接口应该小而专注
type Reader interface {
    Read([]byte) (int, error)
}

// 在使用处定义接口，而不是实现处
```

### 函数设计原则
```go
// 函数参数顺序：context 在前，options 在后
func GetUser(ctx context.Context, id string, opts ...Option) (*User, error)

// 避免布尔参数，使用枚举或配置结构体
type CacheMode int

const (
    CacheModeEnabled CacheMode = iota
    CacheModeDisabled
)

// 返回具体类型，接受接口类型
func NewFormatter(w io.Writer) *Formatter
```

### 并发与性能
```go
// 使用 sync.Pool 复用对象
var bufferPool = sync.Pool{
    New: func() interface{} {
        return make([]byte, 0, 1024)
    },
}

// 避免在循环中分配内存
func processItems(items []Item) []Result {
    results := make([]Result, 0, len(items)) // 预分配容量
    for _, item := range items {
        results = append(results, process(item))
    }
    return results
}
```

### 代码风格约束
- **嵌套控制**：优先使用 guard clauses，减少嵌套
- **变量作用域**：尽可能缩小变量作用域
- **函数长度**：单个函数不超过 50 行，复杂逻辑分解为多个函数
- **包依赖**：避免循环依赖，依赖方向应该是单向的

### Guard Clauses 模式
```go
// Good - 使用 guard clauses
func (s *Service) ProcessUser(ctx context.Context, userID string) error {
    if userID == "" {
        return errors.New("user ID is required")
    }

    user, err := s.getUser(ctx, userID)
    if err != nil {
        return fmt.Errorf("failed to get user: %w", err)
    }

    if !user.IsActive {
        return errors.New("user is not active")
    }

    return s.processActiveUser(ctx, user)
}

// Bad - 过度嵌套
func (s *Service) ProcessUser(ctx context.Context, userID string) error {
    if userID != "" {
        if user, err := s.getUser(ctx, userID); err == nil {
            if user.IsActive {
                return s.processActiveUser(ctx, user)
            } else {
                return errors.New("user is not active")
            }
        } else {
            return fmt.Errorf("failed to get user: %w", err)
        }
    } else {
        return errors.New("user ID is required")
    }
}
```

### 函数注释规范
**注意**：详细注释仅在新建函数时添加，修改现有函数时无需增加注释，专注于逻辑实现即可。
```go
// CreateOrder 创建订单
// 执行步骤：
// 1. 验证用户权限和订单参数
// 2. 检查商品库存和价格
// 3. 创建订单记录和订单项
// 4. 扣减库存并发送通知
// 注意事项：
// - 需要在事务中执行库存扣减
// - 订单金额需要重新计算，不信任前端传参
// - 失败时需要回滚库存操作
func (s *Service) CreateOrder(ctx context.Context, req *CreateOrderRequest) (*CreateOrderResponse, error) {
    // ...
}
```

## 🔧 AI 快速决策表

### 函数类型识别
| 场景 | 层级 | 错误处理 | 必需模式 |
|------|------|----------|----------|
| gRPC/HTTP 接口 | HTTP层 | `errors.WithMessage()` | 获取用户ID + 业务调用 |
| 业务逻辑处理 | Service层 | 业务错误包装，技术错误直传 | Guard Clauses + 结构化日志 |
| 数据库操作 | DAO层 | `ormhelper.WrapErr()` | 预检查 + GORM操作 |

### 错误处理决策
```go
// ✅ HTTP层：业务错误必须包装
return nil, errors.WithMessage(errors.ErrBadRequest, "具体原因")

// ✅ Service层：记录技术错误
log.Context(ctx).Error("operation failed", "error", err)
return err

// ✅ DAO层：统一包装
return result, ormhelper.WrapErr(err)
```

### 常用代码片段模板
```go
// 🚀 HTTP接口标准开头
userID, ok := icontext.UserIdFrom(ctx)
if !ok {
    return nil, errors.WithMessage(errors.ErrAccessTokenExpired, "user not authenticated")
}

// 🚀 参数验证模板
if req.GetId() == "" {
    return nil, errors.WithMessage(errors.ErrBadRequest, "id is required")
}

// 🚀 DAO查询模板
var result EntityType
err := d.session(ctx, opts...).Where("field = ?", value).First(&result).Error
return &result, ormhelper.WrapErr(err)
```

## 必需代码模式

### HTTP接口模式
```go
func (s *Service) GetUser(ctx context.Context, req *v1.GetUserRequest) (*v1.GetUserResponse, error) {
    userID, ok := icontext.UserIdFrom(ctx)
    if !ok {
        return nil, errors.WithMessage(errors.ErrAccessTokenExpired, "user not authenticated")
    }

    user, err := s.getUserByID(ctx, userID, req.GetId())
    if err != nil {
        if isBizError(err) {
            return nil, err
        }
        log.Context(ctx).Error("system error", "error", err)
        return nil, errors.ErrInternalServer
    }

    return &v1.GetUserResponse{User: user}, nil
}
```

### Service业务模式
```go
func (s *Service) getUserByID(ctx context.Context, currentUserID, targetUserID string) (*User, error) {
    if targetUserID == "" {
        return nil, errors.WithMessage(errors.ErrBadRequest, "user ID is required")
    }

    l := log.Context(ctx)

    user, err := s.dao.GetUser(ctx, targetUserID)
    if err != nil {
        if errors.Is(err, gorm.ErrRecordNotFound) {
            return nil, errors.WithMessage(errors.ErrBadRequest, "user not found")
        }
        l.Error("failed to get user", "error", err, "userID", targetUserID)
        return nil, err
    }

    if user.Status != StatusActive {
        return nil, errors.WithMessage(errors.ErrBadRequest, "user is not active")
    }

    return user, nil
}
```

### DAO标准模式
```go
// GetUser 根据用户ID获取用户信息
// DAO层必须使用 ormhelper.WrapErr() 包装所有错误
// 不要在DAO层判断 gorm.ErrRecordNotFound，而是在Service层处理
func (d *dao) GetUser(ctx context.Context, id string, opts ...igorm.Option) (*User, error) {
    var user User
    err := d.session(ctx, opts...).Where("id = ?", id).First(&user).Error
    return &user, ormhelper.WrapErr(err)
}

// BatchGetUsers 批量获取用户信息，使用 IN 查询优化性能
func (d *dao) BatchGetUsers(ctx context.Context, ids []string, opts ...igorm.Option) ([]User, error) {
    if len(ids) == 0 {
        return []User{}, nil // 返回空切片，不返回 nil
    }

    var users []User
    err := d.session(ctx, opts...).Where("id IN ?", ids).Find(&users).Error
    return users, ormhelper.WrapErr(err)
}
```

## 关键约束与最佳实践

### 性能优化
- **预分配切片容量**：`make([]T, 0, expectedSize)`
- **使用 strings.Builder**：构建大字符串时
- **避免在循环中分配**：预分配或使用对象池
- **批量数据库操作**：避免 N+1 查询

### 测试规范
```go
func TestGetUser(t *testing.T) {
    tests := []struct {
        name    string
        userID  string
        want    *User
        wantErr bool
    }{
        {
            name:   "valid user",
            userID: "123",
            want:   &User{ID: "123", Name: "John"},
        },
        {
            name:    "user not found",
            userID:  "invalid",
            wantErr: true,
        },
    }

    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            got, err := service.GetUser(ctx, tt.userID)
            if (err != nil) != tt.wantErr {
                t.Errorf("GetUser() error = %v, wantErr %v", err, tt.wantErr)
                return
            }
            if !reflect.DeepEqual(got, tt.want) {
                t.Errorf("GetUser() = %v, want %v", got, tt.want)
            }
        })
    }
}
```

### 关键原则
- **错误信息**：HTTP层错误信息必须英文
- **日志规范**：使用结构化日志，技术错误必须记录
- **YAGNI原则**：不添加不必要的抽象层
- **代码自解释**：减少不必要的注释，关键逻辑必须注释
- **一致性**：项目内保持编码风格一致
- **可读性优于简洁性**：清晰的代码比聪明的代码更重要

## 禁止事项
- ❌ 使用 `init()` 函数（除非绝对必要）
- ❌ 全局变量（使用依赖注入）
- ❌ `panic()` 在业务逻辑中
- ❌ 空接口 `interface{}` （使用泛型或具体类型）
- ❌ 在循环中使用 `defer`
- ❌ 忽略错误返回值
- ❌ 生成示例文档，非要求不做单元测试，节约token
