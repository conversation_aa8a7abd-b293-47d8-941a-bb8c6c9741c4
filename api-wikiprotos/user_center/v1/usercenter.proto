syntax = "proto3";

package api.user_center.v1;
import "google/api/annotations.proto";
import "protoc-gen-openapiv2/options/annotations.proto";
import "google/protobuf/any.proto";

option go_package = "user_center/api/user_center/v1;v1";

service UserCenter {
  rpc GetUsersInfo (GetUsersRequest) returns (GetUsersReply){
    option (google.api.http)={post:"/v1/app/usercenter/getbusinesscardlist",body:"*"};
  }

  //根据企业code获取 企业员工和管理员userid
  rpc GetEnterpriseUserIdsByCode (GetEnterpriseUserIdsByCodeRequest) returns (GetEnterpriseUserIdsByCodeReply){
    option (google.api.http)={post:"/v1/app/usercenter/getenterpriseuseridsbycode",body:"*"};
  }
  // 根据id获取企业类型
  rpc GetOfficialNumberTypeById (GetOfficialNumberTypeByIdRequest) returns (GetOfficialNumberTypeByIdReply){
    option (google.api.http)={get:"/v1/app/usercenter/getofficialnumbertypebyid"};
  }

  // 获取用户关注对象
  rpc GetUserFollow(GetUserFollowRequest) returns (GetUserFollowReply){
    option (google.api.http)={get:"/v1/app/usercenter/getuserfollow"};
  }
  //获取用户粉丝并且没有相互关注的
  rpc  GetUserFansNoFollowUserIds(GetUserFansNoFollowRequest) returns (GetUserFansNoFollowReply){
    option (google.api.http)={get:"/v1/app/usercenter/getuserfansnofollowuserids"};
  }
   //获取用户粉丝数量
  rpc  GetUsersFansCount(GetUsersFansCountRequest) returns (GetUsersFansCountReply){
    option (google.api.http)={post:"/v1/app/usercenter/getusersfanscount",body:"*"};
  }

  rpc  GetUserAttentionsPageList(GetUserAttentionsPageListRequest) returns (GetUserAttentionsPageListReply){
    option (google.api.http)={get:"/v1/app/usercenter/getuserattentionspagelist"};
  }
  rpc  GetUserWikiNumbers(GetUserWikiNumbersRequest) returns (GetUserWikiNumbersReply){
    option (google.api.http)={post:"/v1/app/usercenter/getuserwikinumbers",body:"*"};
  }
  rpc  GetUserUserIdByWikiNumber(GetUserWikiNumbersRequest) returns (GetUserWikiNumbersReply){
    option (google.api.http)={post:"/v1/app/usercenter/getuseruseridbywikifxnumber",body:"*"};
  }

  rpc  GetBasicUserInfo(GetUserWikiNumbersRequest) returns (GetBasicUserInfoReply){
    option (google.api.http)={post:"/v1/app/usercenter/getbasicuserinfo",body:"*"};
  }

  rpc  GetUserByPhoneNumber(GetUserByPhoneNumberRequest) returns (GetUserByPhoneNumberReply){
    option (google.api.http)={post:"/v1/app/usercenter/getuserbyphonenumber",body:"*"};
  }

  //创建展会预注册
  rpc  CreateExpoPreUser(CreateExpoPreUserRequest) returns (CreateExpoPreUserReply){
    option (google.api.http)={post:"/v1/app/usercenter/createexpopreuser",body:"*"};
  }

  //获取用户粉丝和关注数量
  rpc  GetUserFollowAndFansCount(GetUserFollowAndFansCountRequest) returns (GetUserFollowAndFansCountReply){
    option (google.api.http)={get:"/v1/app/usercenter/getuserfollowandfanscount"};
  }

  //获取用户企业代码
  rpc GetUserEnterpriseCode (GetUserEnterpriseCodeRequest) returns (GetUserEnterpriseCodeReply){
    option (google.api.http)={get:"/v1/app/usercenter/getuserenterprisecode"};
  }


}

message GetUserAttentionsPageListRequest{
  string userId=1;
  int32 pageIndex=2;
  int32 pageSize=3;
}
message GetUserAttentionsPageListReply{
  repeated  GetUserAttentionsPageListReplyItem list=1;
}
message GetUserAttentionsPageListReplyItem{
  string userId=1;
  string nickName=2;//昵称
  string avatarAddress=3; //头像
  int32 UserType=4;
  string EnterpriseVIcon2=5;
  string darenIcon=6;
  string avatarFrame=7; // 相框
}

message GetUsersFansCountRequest{
  repeated string userIds=1;
}
message GetUsersFansCountReply{
  repeated GetUsersFansCountReplyItem Data=1;
}

message GetUsersFansCountReplyItem{
  string userId=1;
  int32  FansCount=2;

}


message GetUserFansNoFollowRequest{
     string userId=1;
}
message GetUserFansNoFollowReply{
  repeated string userId=1;
}

message GetUserFollowRequest{//用户关注请求
  string userId=1;
}
message  GetUserFollowReplyItem{
  string dataId=1;
  OfficialNumberType userType=2;
}

message GetUserFollowReply{ //用户关注返回
  repeated GetUserFollowReplyItem list=1;
}



message GetOfficialNumberTypeByIdRequest{
   string id=1;
}

message GetOfficialNumberTypeByIdReply{
  OfficialNumberType userType=1;

}
// 官方号类型
enum OfficialNumberType {
  OfficialNumber_Unknown=0; //未知
  Trader=1; // 交易商号
  WikiFXMediate=2; // 天眼调解
  WikiFXNews=3; // WikiFX-新闻
  WikiFXExpress=4; // WikiFX-快讯
  WikiFXSurvey=5; // WikiFX-实勘
  ServiceProvider=6; // 服务商号
  Regulator=7; // 监管机构号
  User=8; // 用户号
  WikiFXActivity=9;
  Lemonx=10;
  Expo=11;
  WikiFx=12;
  WikiFxEducation=13;
  WikiFXElitesClub=14;
  SkylineGuide=15;
}

message GetEnterpriseUserIdsByCodeRequest{
    string code=1;
}
message GetEnterpriseUserIdsByCodeReply{
  repeated string userIds=1;
}

message GetUsersRequest{
  //登录用户id
  string userLoginId=1;
  string languageCode=2;
  int32 businessType=3;
  repeated string userIds=4;//userid
  repeated  string traderCodes=5; //企业code


}
message UserInfo{
  string nickName=1;
  string userId=2;
  string wikiFxNumber=3;//天眼号
  int32  userStatus=4;// 用户状态 1企业号 2员工 3 个人 4 kol
  int32  userIdentityNew=5;//用户身份 1普通用户 2个人投资者 3 Kol
  string userIdentityNewIcon=6; //用户身份icon
  int32  identityType =7;//1个人 2企业
  string enterpriseVIcon=8;//企业号V标志 头像右下角
  string enterpriseVIcon2=9;//企业号V标志 头像右下角新版
  string tagIcon=10;  /// 企业tag标志icon
  string enterpriseName =11;//服务商交易商名称
  string enterpriseCode=12;//服务商交易商Code
  int32 enterpriseUserLevel=13;//2管理员 4员工
  string enterpriseLogo=14;
  string enterpriseIco=15;
  string avatarAddress=16;// 头像
  string originAvatarAddress=17;//原始头像
  string position=18;//职位
  bool isFollow=19;//是否关注
  int32 enterpriseType=20;// 1服务商 2 交易商
  int32 attentionStauts=21;//// 1未关注 2 已关注 3相互关注   4自己
  int32 followCount=22;//关注数量
  int32 fansCount=23;//粉丝数量
  int32 applaudCount=24;//点赞数量
  string staffTag=25;//员工Tag
  string detailBgImg=26;//
  string originNickName=27;
  string registrationTime=28;//注册时间
  string countryCode=29;
  string areaCode=30;//手机区号
  string phoneNumber=31;//手机号
  string email =32;//邮箱
  bool isAuth=33;// 是否认证
  string kolIcon=34;
  string registerLong=35; //注册时间
  bool isFirmoffer=36;//是否是实盘用户
  string darenIcon=37;//达人icon
  string official=38;//官方
  string officialColor=39;// 官方颜色
  string TagWords=40;//显示用户身份
  string NickNameColor=41;//昵称颜色
  string OriginId=42;//原始Id
  int32 officialNumberType=43;//类型 1 交易商号 6 服务商号 2 天眼调解   3 WikiFX-新闻  4; WikiFX-快讯    5 WikiFX-实勘 7 监管机构号 8个人
  string avatarFrame=44;// 头像
  }
message GetUsersReply{
 repeated  UserInfo message=1;
}


message  GetUserWikiNumbersRequest{
  repeated string userIds=1;
}
message  GetUserWikiNumbersReply{
  repeated GetUserWikiNumbersReplyItem list=1;
}

message  GetUserWikiNumbersReplyItem{
  string userId=1;
  string WikiNumber=2;
}

message GetBasicUserInfoReply{
repeated GetBasicUserInfoItem list=1;
}
message  GetBasicUserInfoItem{
  string userId=1;
  string nickName=2;
  string wikiNumber=3;
}

message GetUserByPhoneNumberRequest{
  string areaCode=1;
  string phoneNumber=2;
}

message GetUserByPhoneNumberReply{
  string userId=1;
  string nickName=2;
  string wikiNumber=3;
}
message CreateExpoPreUserRequest{
  int64 expoId=1;//展会Id
  int64 releaseId=2; //关联Id
  string AreaCode=3;//区号
  string PhoneNumber=4;//手机号
  string Email=5;
  string NickName=6;
}
message  CreateExpoPreUserReply{
  string userId=1;
}

message  GetUserFollowAndFansCountRequest{
  string userId=1;
}
message  GetUserFollowAndFansCountReply{
  int32 followCount=1;
  int32 fansCount=2;
}

message GetUserEnterpriseCodeRequest{
  string userId=1;
}
message  GetUserEnterpriseCodeReply{
  string enterpriseCode=1;
}