{

  "dataType":1, // 1 动态 2 商业 3 新闻 4 实勘 5 监管披露 6 快讯 7 调解
  "isAd": 0,
  "surveyData":{ // 实勘
    "seal":"", // 印章
    "surveyor":"", // 实勘人
    "flag":"", // 实勘到访地区旗帜
    "countryName":"",  // 当前实勘访问公司所在国家名称
    "evaluate":"", // 实勘评价
    "evaluateColor":"", // 评价颜色
    "baiduImg":"", // 百度坐标缩略图
    "googleImg":"" // 谷歌坐标缩略图
  },
  "regDisclosureData":{ // 监管披露
    "traderCol":[ // 披露交易商
      {
        "code":"", //交易商Code
        "name":"", // 交易商名称
      }
    ],
    "tagData":{ // 披露标签
      "tagName":"",  // 标签名称
      "color":"", // 颜色
      "tagCode":"", // code
    },
    "categoryData":{ // 披露分类
      "categoryName":"", // 分类名称
      "categoryId":"" // 分类id
    },
    "summaryData":{ // 摘要信息
      "rule":0, // 匹配规则
      "ruleName":"", // 规则名称
      "wiki_timestamp":17234234234,  // 披露时间戳
      "penaltyAmount":"", // 处罚金额(完整)
      "amountSymbol":"", // 处罚金额符号
      "reason":""  // 处罚原因
    },
    "content":"", // 内容
    "seal":"" // 章
  },
  "mediateData":{ // 调解
    "traderData": // 调解交易商
    {
      "code":"", //交易商Code
      "name":"", // 交易商名称
    },
    "object_label":"" , // 调解对象标签
    "category_label":"", // 调解问题标签
    "category":"", // 调解问题
    "requirement_label":"", // 调解要求标签
    "requirement":"", // 调解要求
    "amount_label":"", //调解金额标签
    "amount":"", // 维权金额
    "symbol":"" // 货币符号
  },
  "postsData":{ // 帖子 右下角角标
    "sign": {
      "isShow": 1,  //是否展示
      "bgColor": "", //背景颜色
      "word": "曝光",//显示文字
      "icon": ""//icon
    },
    "postsId": "string",  // 帖子ID
    "releaseType":"string",
    "title": "string", // 标题
    "titleNew": "爱的苦果，将在成熟时坠落。此时此刻，只要有落日为我们加冕，A随之而来的一切，又算得了什么。",//标题原文
    "content": "string", // 内容
    "shareUrl":"", // 分享地址
    "imagesv2": [
      {
        "list": "https://h8imgs.zy223.com/Bar/app/2023/01/2429319222/20230106_514824.jpg-list300", //列表图
        "detail": "https://h8imgs.zy223.com/Bar/app/2023/01/2429319222/20230106_514824.jpg-detail",//详情图
        "url": "null",
        "width": 1242.0,
        "height": 2208.0
      }
    ],
    "themeCode": "IBCode",
    "images": [ // 图片
      "string"
    ],
    "isApplaud": false, // 是否点赞
    "isCollect": false, // 是否收藏
    "pushlishTime": "1718112289", //发布时间戳
  },
  "userData":{  // 用户
    "nickName": "string", // 昵称
    "nickNameColor": "#1D2129", // 昵称颜色
    "avatarAddress": "string", // 头像地址
    "vipIcon": "https://img.fx696.com/WikiEnterprise/Cooperation/enterpriseicon.png_wiki-template-global", // VIP Icon
    "userId": "0139564850", // 用户ID
    "darenIcon":"", // 达人Icon
    "rightLableType":0  //用户类型 1企业号 2员工 3 个人 4 kol
  }
}