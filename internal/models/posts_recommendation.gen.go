// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package models

const TableNamePostsRecommendation = "posts_recommendation"

// PostsRecommendation mapped from table <posts_recommendation>
type PostsRecommendation struct {
	ID      string `gorm:"column:Id;primaryKey;comment:话题推荐" json:"Id"` // 话题推荐
	Content string `gorm:"column:Content" json:"Content"`
	Sort    int32  `gorm:"column:Sort;comment:排序" json:"Sort"`         // 排序
	Enabled int32  `gorm:"column:Enabled;comment:是否显示" json:"Enabled"` // 是否显示
}

// TableName PostsRecommendation's table name
func (*PostsRecommendation) TableName() string {
	return TableNamePostsRecommendation
}
