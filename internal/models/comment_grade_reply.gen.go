// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package models

import (
	"time"
)

const TableNameCommentGradeReply = "comment_grade_reply"

// CommentGradeReply mapped from table <comment_grade_reply>
type CommentGradeReply struct {
	ReplyID        string    `gorm:"column:ReplyId;primaryKey;comment:回复主键" json:"ReplyId"`       // 回复主键
	CommentGradeID string    `gorm:"column:CommentGradeId;comment:评论ID" json:"CommentGradeId"`    // 评论ID
	ReplyUserType  int32     `gorm:"column:ReplyUserType;comment:1用户 2官方回复" json:"ReplyUserType"` // 1用户 2官方回复
	UserID         string    `gorm:"column:UserId" json:"UserId"`
	ReplyNickName  string    `gorm:"column:ReplyNickName;comment:回复回复的昵称" json:"ReplyNickName"` // 回复回复的昵称
	NickName       string    `gorm:"column:NickName;comment:用户昵称" json:"NickName"`              // 用户昵称
	UserLabel      string    `gorm:"column:UserLabel;comment:标签" json:"UserLabel"`              // 标签
	UserFlag       string    `gorm:"column:UserFlag;comment:用户国旗" json:"UserFlag"`              // 用户国旗
	ParentID       string    `gorm:"column:ParentId" json:"ParentId"`
	RootParentID   string    `gorm:"column:RootParentId;comment:回复下的回复根id" json:"RootParentId"`                                                                                                                                            // 回复下的回复根id
	Title          string    `gorm:"column:Title;comment:标题" json:"Title"`                                                                                                                                                                 // 标题
	Content        string    `gorm:"column:Content;comment:回复内容" json:"Content"`                                                                                                                                                           // 回复内容
	LanguageCode   string    `gorm:"column:LanguageCode;comment:语言code" json:"LanguageCode"`                                                                                                                                               // 语言code
	IPCountryCode  string    `gorm:"column:IpCountryCode;comment:IP地址反转国家code" json:"IpCountryCode"`                                                                                                                                       // IP地址反转国家code
	CountryCode    string    `gorm:"column:CountryCode;comment:国家code" json:"CountryCode"`                                                                                                                                                 // 国家code
	ApplaudCount   int32     `gorm:"column:ApplaudCount;comment:点赞数量" json:"ApplaudCount"`                                                                                                                                                 // 点赞数量
	ForwardCount   int32     `gorm:"column:ForwardCount;comment:转发数量" json:"ForwardCount"`                                                                                                                                                 // 转发数量
	ReplyCount     int32     `gorm:"column:ReplyCount;comment:回复数量" json:"ReplyCount"`                                                                                                                                                     // 回复数量
	PublicTime     time.Time `gorm:"column:PublicTime;comment:发布时间" json:"PublicTime"`                                                                                                                                                     // 发布时间
	UpdateTime     time.Time `gorm:"column:UpdateTime;comment:更新时间" json:"UpdateTime"`                                                                                                                                                     // 更新时间
	ShowTime       time.Time `gorm:"column:ShowTime;comment:显示时间" json:"ShowTime"`                                                                                                                                                         // 显示时间
	Status         int32     `gorm:"column:Status;comment: 状态（100：未审核  200：审核通过 401：审核未通过; 499 ：待回复）" json:"Status"`                                                                                                                       //  状态（100：未审核  200：审核通过 401：审核未通过; 499 ：待回复）
	Auditor        string    `gorm:"column:Auditor;comment:审核人" json:"Auditor"`                                                                                                                                                            // 审核人
	AuditTime      time.Time `gorm:"column:AuditTime;comment:审核时间" json:"AuditTime"`                                                                                                                                                       // 审核时间
	FeedBack       string    `gorm:"column:FeedBack;comment:驳回原因" json:"FeedBack"`                                                                                                                                                         // 驳回原因
	SysType        int32     `gorm:"column:SysType;comment:设备系统类型（0：ios  1：android  3：pc客户端）" json:"SysType"`                                                                                                                              // 设备系统类型（0：ios  1：android  3：pc客户端）
	AppType        int32     `gorm:"column:AppType;comment:app类型（1：fxeye  2：wikifx 3：wikifx2.0  4：wikibit 5：fxeye_testflight版 6：fxeye_极速版  7：fxeye_维权版  8：wikibit_testflight版   9：wikibit_lite版  10：wikibit_合规版  999：web）" json:"AppType"` // app类型（1：fxeye  2：wikifx 3：wikifx2.0  4：wikibit 5：fxeye_testflight版 6：fxeye_极速版  7：fxeye_维权版  8：wikibit_testflight版   9：wikibit_lite版  10：wikibit_合规版  999：web）
	ReplyType      int32     `gorm:"column:ReplyType;comment:回复评价类型 1交易商 2通证 3项目 4交易所 5钱包 6 服务商7 券商 8 fx监管机构" json:"ReplyType"`                                                                                                            // 回复评价类型 1交易商 2通证 3项目 4交易所 5钱包 6 服务商7 券商 8 fx监管机构
	ReplyObjectID  string    `gorm:"column:ReplyObjectId;comment:回复code" json:"ReplyObjectId"`                                                                                                                                             // 回复code
}

// TableName CommentGradeReply's table name
func (*CommentGradeReply) TableName() string {
	return TableNameCommentGradeReply
}
