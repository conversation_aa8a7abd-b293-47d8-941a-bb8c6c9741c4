// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package models

import (
	"time"
)

const TableNameCommentGradeLabelLanguage = "comment_grade_label_language"

// CommentGradeLabelLanguage mapped from table <comment_grade_label_language>
type CommentGradeLabelLanguage struct {
	ID           string    `gorm:"column:Id;primaryKey;comment:评价标签翻译" json:"Id"` // 评价标签翻译
	LabelID      string    `gorm:"column:LabelId" json:"LabelId"`
	LanguageCode string    `gorm:"column:LanguageCode" json:"LanguageCode"`
	Name         string    `gorm:"column:Name" json:"Name"`
	CreateTime   time.Time `gorm:"column:CreateTime" json:"CreateTime"`
	CreateName   string    `gorm:"column:CreateName" json:"CreateName"`
}

// TableName CommentGradeLabelLanguage's table name
func (*CommentGradeLabelLanguage) TableName() string {
	return TableNameCommentGradeLabelLanguage
}
