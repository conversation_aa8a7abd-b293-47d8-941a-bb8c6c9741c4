// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package models

import (
	"time"
)

const TableNamePost = "posts"

type PostsUserCount struct {
	UserId     string
	PostsCount int
}

// Post mapped from table <posts>
type Post struct {
	PostsID              string    `gorm:"column:PostsId;primaryKey;default:0;comment:帖子Id" json:"PostsId"`    // 帖子Id
	PostsCode            string    `gorm:"column:PostsCode;comment:帖子code" json:"PostsCode"`                   // 帖子code
	UserID               string    `gorm:"column:UserId;not null;comment:用户ID" json:"UserId"`                  // 用户ID
	EnterpriseCode       string    `gorm:"column:EnterpriseCode;comment:企业code" json:"EnterpriseCode"`         // 企业code
	AffEnterpriseCode    string    `gorm:"column:AffEnterpriseCode;comment:所属企业code" json:"AffEnterpriseCode"` // 所属企业code
	ReleaseType          int32     `gorm:"column:ReleaseType;not null;comment:1 服务 2动态" json:"ReleaseType"`    // 1 服务 2动态
	ObjectID             string    `gorm:"column:ObjectId;comment:评论具体对象ID" json:"ObjectId"`                   // 评论具体对象ID
	JSONContentExcept    string    `gorm:"column:JsonContentExcept" json:"JsonContentExcept"`
	JSONContent          string    `gorm:"column:JsonContent" json:"JsonContent"`
	NewJSONContent       string    `gorm:"column:NewJsonContent;comment:动态字段json存储" json:"NewJsonContent"` // 动态字段json存储
	Title                string    `gorm:"column:Title" json:"Title"`
	Content              string    `gorm:"column:Content;not null;comment:评论内容" json:"Content"`                                                                                                                                                  // 评论内容
	LanguageCode         string    `gorm:"column:LanguageCode;not null;comment:语言code" json:"LanguageCode"`                                                                                                                                      // 语言code
	IPCountryCode        string    `gorm:"column:IpCountryCode;comment:IP地址反转国家code" json:"IpCountryCode"`                                                                                                                                       // IP地址反转国家code
	CountryCode          string    `gorm:"column:CountryCode;not null;comment:国家code" json:"CountryCode"`                                                                                                                                        // 国家code
	IPAddress            string    `gorm:"column:IpAddress;not null;comment:IP地址" json:"IpAddress"`                                                                                                                                              // IP地址
	Status               int32     `gorm:"column:Status;not null;comment:状态（100：未审核  200：审核通过 401：审核未通过; 499 ：待回复）" json:"Status"`                                                                                                               // 状态（100：未审核  200：审核通过 401：审核未通过; 499 ：待回复）
	Auditor              string    `gorm:"column:Auditor;comment:审核人" json:"Auditor"`                                                                                                                                                            // 审核人
	AuditTime            time.Time `gorm:"column:AuditTime;comment:审核时间" json:"AuditTime"`                                                                                                                                                       // 审核时间
	FeedBack             string    `gorm:"column:FeedBack;comment:驳回原因code" json:"FeedBack"`                                                                                                                                                     // 驳回原因code
	Tags                 string    `gorm:"column:Tags;comment:标签" json:"Tags"`                                                                                                                                                                   // 标签
	SysType              int32     `gorm:"column:SysType;comment:设备系统类型（0：ios  1：android  3：pc客户端）" json:"SysType"`                                                                                                                              // 设备系统类型（0：ios  1：android  3：pc客户端）
	AppType              int32     `gorm:"column:AppType;comment:app类型（1：fxeye  2：wikifx 3：wikifx2.0  4：wikibit 5：fxeye_testflight版 6：fxeye_极速版  7：fxeye_维权版  8：wikibit_testflight版   9：wikibit_lite版  10：wikibit_合规版  999：web）" json:"AppType"` // app类型（1：fxeye  2：wikifx 3：wikifx2.0  4：wikibit 5：fxeye_testflight版 6：fxeye_极速版  7：fxeye_维权版  8：wikibit_testflight版   9：wikibit_lite版  10：wikibit_合规版  999：web）
	PublicTime           time.Time `gorm:"column:PublicTime;comment:发布时间" json:"PublicTime"`                                                                                                                                                     // 发布时间
	UpdateTime           time.Time `gorm:"column:UpdateTime;comment:更新时间" json:"UpdateTime"`                                                                                                                                                     // 更新时间
	ShowTime             time.Time `gorm:"column:ShowTime;comment:显示时间" json:"ShowTime"`                                                                                                                                                         // 显示时间
	IsTopping            bool      `gorm:"column:IsTopping;comment:是否置顶（0：不置顶 1：置顶）" json:"IsTopping"`                                                                                                                                           // 是否置顶（0：不置顶 1：置顶）
	ServiceContactUserID string    `gorm:"column:ServiceContactUserId;comment:服务联系人" json:"ServiceContactUserId"`                                                                                                                                // 服务联系人
	IsReply              int32     `gorm:"column:IsReply;comment:是否回复" json:"IsReply"`                                                                                                                                                           // 是否回复
	UserCoefficient      float64   `gorm:"column:UserCoefficient;comment:用户系数" json:"UserCoefficient"`                                                                                                                                           // 用户系数
	IsEnbaled            bool      `gorm:"column:IsEnbaled;comment:是否是有效的" json:"IsEnbaled"`                                                                                                                                                     // 是否是有效的
	CurrentIdentity      int32     `gorm:"column:CurrentIdentity;default:1;comment:当时身份 1个人 2企业" json:"CurrentIdentity"`                                                                                                                         // 当时身份 1个人 2企业
	/*


	 */
	EnterpriseInfo         string  `gorm:"column:EnterpriseInfo;comment:\n" json:"EnterpriseInfo"`
	OriginUserID           string  `gorm:"column:OriginUserId" json:"OriginUserId"`
	BuinsessCardID         string  `gorm:"column:BuinsessCardId;comment:名片ID" json:"BuinsessCardId"`                                  // 名片ID
	OldContactway          string  `gorm:"column:OldContactway;comment:老的联系方式" json:"OldContactway"`                                  // 老的联系方式
	ObjectType             int32   `gorm:"column:ObjectType;comment:评价类型 1交易商 2通证 3项目 4交易所 5钱包 6 服务商7 券商 8 fx监管机构" json:"ObjectType"` // 评价类型 1交易商 2通证 3项目 4交易所 5钱包 6 服务商7 券商 8 fx监管机构
	FirstImage             string  `gorm:"column:FirstImage;comment:第一张图片" json:"FirstImage"`                                         // 第一张图片
	BasicData              string  `gorm:"column:BasicData;comment:bascidata" json:"BasicData"`                                       // bascidata
	RelationEnterpriseCode string  `gorm:"column:RelationEnterpriseCode;comment:关联企业" json:"RelationEnterpriseCode"`                  // 关联企业
	Similarity             float64 `gorm:"column:Similarity;default:0.00;comment:相似度" json:"Similarity"`                              // 相似度
	SimilarityCode         string  `gorm:"column:SimilarityCode" json:"SimilarityCode"`
	IsShow                 int32   `gorm:"column:IsShow;comment:推荐最新是否展示  1不展示 0展示" json:"IsShow"`            // 推荐最新是否展示  1不展示 0展示
	CommentGrade           int32   `gorm:"column:CommentGrade;comment:评价等级 1好评 2中评 3差评" json:"CommentGrade"`  // 评价等级 1好评 2中评 3差评
	Notrecommendreason     string  `gorm:"column:notrecommendreason;comment:不推荐原因" json:"notrecommendreason"` // 不推荐原因
	UniqueId     string  `gorm:"column:UniqueId;comment:唯一id" json:"uniqueId"` // 唯一id
	VodFileId     string  `gorm:"column:VodFileId;comment:视频地址" json:"vodFileId"` // 视频地址
}

// TableName Post's table name
func (*Post) TableName() string {
	return TableNamePost
}
