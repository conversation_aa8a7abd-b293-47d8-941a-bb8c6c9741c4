// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package models

import (
	"time"
)

const TableNameCommentGradeBak = "comment_grade_bak"

// CommentGradeBak mapped from table <comment_grade_bak>
type CommentGradeBak struct {
	CommentGradeID    string    `gorm:"column:CommentGradeId;primaryKey;comment:口碑Id" json:"CommentGradeId"` // 口碑Id
	CommentGradeCode  string    `gorm:"column:CommentGradeCode" json:"CommentGradeCode"`
	ObjectType        int32     `gorm:"column:ObjectType;comment:1交易商 2通证 3项目 4交易所 5钱包 6 服务商" json:"ObjectType"` // 1交易商 2通证 3项目 4交易所 5钱包 6 服务商
	ObjectID          string    `gorm:"column:ObjectId;comment:评受平方Code" json:"ObjectId"`                        // 评受平方Code
	UserID            string    `gorm:"column:UserId" json:"UserId"`
	CreateTime        time.Time `gorm:"column:CreateTime" json:"CreateTime"`
	UpdateTime        time.Time `gorm:"column:UpdateTime" json:"UpdateTime"`
	Title             string    `gorm:"column:Title" json:"Title"`
	Content           string    `gorm:"column:Content" json:"Content"`
	LanguageCode      string    `gorm:"column:LanguageCode" json:"LanguageCode"`
	IPCountryCode     string    `gorm:"column:IpCountryCode" json:"IpCountryCode"`
	CountryCode       string    `gorm:"column:CountryCode" json:"CountryCode"`
	IPAddress         string    `gorm:"column:IpAddress" json:"IpAddress"`
	Status            int32     `gorm:"column:Status;comment:状态（100：未审核  200：审核通过 401：审核未通过; 499 ：待回复）" json:"Status"` // 状态（100：未审核  200：审核通过 401：审核未通过; 499 ：待回复）
	Auditor           string    `gorm:"column:Auditor" json:"Auditor"`
	AuditTime         time.Time `gorm:"column:AuditTime" json:"AuditTime"`
	FeedBack          string    `gorm:"column:FeedBack" json:"FeedBack"`
	SysType           int32     `gorm:"column:SysType;comment:设备系统类型（0：ios  1：android  3：pc客户端）" json:"SysType"`                                                                                                                              // 设备系统类型（0：ios  1：android  3：pc客户端）
	AppType           int32     `gorm:"column:AppType;comment:app类型（1：fxeye  2：wikifx 3：wikifx2.0  4：wikibit 5：fxeye_testflight版 6：fxeye_极速版  7：fxeye_维权版  8：wikibit_testflight版   9：wikibit_lite版  10：wikibit_合规版  999：web）" json:"AppType"` // app类型（1：fxeye  2：wikifx 3：wikifx2.0  4：wikibit 5：fxeye_testflight版 6：fxeye_极速版  7：fxeye_维权版  8：wikibit_testflight版   9：wikibit_lite版  10：wikibit_合规版  999：web）
	IsOfficialReply   int32     `gorm:"column:IsOfficialReply;comment:是否官方回复" json:"IsOfficialReply"`                                                                                                                                         // 是否官方回复
	OfficialReply     string    `gorm:"column:OfficialReply;comment:官方回复" json:"OfficialReply"`                                                                                                                                               // 官方回复
	IsEnbaled         int32     `gorm:"column:IsEnbaled;comment:是否有效" json:"IsEnbaled"`                                                                                                                                                       // 是否有效
	UserInfo          string    `gorm:"column:UserInfo;comment:详细信息" json:"UserInfo"`                                                                                                                                                         // 详细信息
	IsAnonymity       bool      `gorm:"column:IsAnonymity;comment:是否是匿名" json:"IsAnonymity"`                                                                                                                                                  // 是否是匿名
	IsExposure        bool      `gorm:"column:IsExposure;comment:是否曝光" json:"IsExposure"`                                                                                                                                                     // 是否曝光
	ProjectType       int32     `gorm:"column:ProjectType;comment:1 fx 2bit" json:"ProjectType"`                                                                                                                                              // 1 fx 2bit
	OfficialReplyTime time.Time `gorm:"column:OfficialReplyTime" json:"OfficialReplyTime"`
}

// TableName CommentGradeBak's table name
func (*CommentGradeBak) TableName() string {
	return TableNameCommentGradeBak
}
