// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package models

const TableNameScoreinfoBak = "scoreinfo_bak"

// ScoreinfoBak mapped from table <scoreinfo_bak>
type ScoreinfoBak struct {
	ID             string  `gorm:"column:Id;primaryKey" json:"Id"`
	ScoreInfo      string  `gorm:"column:ScoreInfo;comment:[{"code1":4.5},{"code2":3.5}]" json:"ScoreInfo"` // [{"code1":4.5},{"code2":3.5}]
	TotalScore     float64 `gorm:"column:TotalScore;comment:总分" json:"TotalScore"`                          // 总分
	CommentGradeID string  `gorm:"column:CommentGradeId" json:"CommentGradeId"`
	CalculateScore float64 `gorm:"column:CalculateScore;default:0.000000000" json:"CalculateScore"`
}

// TableName ScoreinfoBak's table name
func (*ScoreinfoBak) TableName() string {
	return TableNameScoreinfoBak
}
