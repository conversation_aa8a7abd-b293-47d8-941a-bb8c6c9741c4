// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package models

import (
	"time"
)

const TableNameDwdPostsStatistic = "dwd_posts_statistics"

// DwdPostsStatistic 帖子总计数据
type DwdPostsStatistic struct {
	PostsID      string    `gorm:"column:PostsId;primaryKey;comment:帖子Id" json:"PostsId"`                                              // 帖子Id
	PostsCode    string    `gorm:"column:PostsCode;comment:帖子code" json:"PostsCode"`                                                   // 帖子code
	UserID       string    `gorm:"column:UserId;not null;comment:用户ID" json:"UserId"`                                                  // 用户ID
	ReleaseType  int32     `gorm:"column:ReleaseType;not null;comment:1 服务 2动态" json:"ReleaseType"`                                    // 1 服务 2动态
	Status       int32     `gorm:"column:Status;not null;default:100;comment:状态（100：未审核  200：审核通过 401：审核未通过; 499 ：待回复）" json:"Status"` // 状态（100：未审核  200：审核通过 401：审核未通过; 499 ：待回复）
	PublicTime   time.Time `gorm:"column:PublicTime;comment:发布时间" json:"PublicTime"`                                                   // 发布时间
	LanguageCode string    `gorm:"column:LanguageCode;not null;comment:语言code" json:"LanguageCode"`                                    // 语言code
	ApplaudCount int32     `gorm:"column:ApplaudCount;not null;comment:点赞数量" json:"ApplaudCount"`                                      // 点赞数量
	ForwardCount int32     `gorm:"column:ForwardCount;not null;comment:分享数量" json:"ForwardCount"`                                      // 分享数量
	ReplyCount   int32     `gorm:"column:ReplyCount;not null;comment:评论数量" json:"ReplyCount"`                                          // 评论数量
	CollectCount int32     `gorm:"column:CollectCount;not null;comment:收藏数量" json:"CollectCount"`                                      // 收藏数量
	ViewCount    int32     `gorm:"column:ViewCount;not null;comment:浏览数量" json:"ViewCount"`                                            // 浏览数量
	Score        float64   `gorm:"column:Score;not null;default:0.00;comment:分数" json:"Score"`                                         // 分数
	IsShow       int32     `gorm:"column:IsShow" json:"IsShow"`
	ObjectType   int32     `gorm:"column:ObjectType" json:"ObjectType"`
}

// TableName DwdPostsStatistic's table name
func (*DwdPostsStatistic) TableName() string {
	return TableNameDwdPostsStatistic
}
