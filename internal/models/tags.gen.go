// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package models

import (
	"time"
)

const TableNameTag = "tags"

// Tag mapped from table <tags>
type Tag struct {
	TagID       string    `gorm:"column:tagId;primaryKey" json:"tagId"`
	Translation string    `gorm:"column:translation;comment:翻译" json:"translation"` // 翻译
	Updatetime  time.Time `gorm:"column:updatetime" json:"updatetime"`
	Projectid   int32     `gorm:"column:projectid;default:1;comment:项目" json:"projectid"` // 项目
	Moduleid    int32     `gorm:"column:moduleid;default:1;comment:模块" json:"moduleid"`   // 模块
}

// TableName Tag's table name
func (*Tag) TableName() string {
	return TableNameTag
}
