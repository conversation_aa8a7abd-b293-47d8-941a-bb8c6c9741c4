// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package models

const TableNamePostsStatistic = "posts_statistics"

// PostsStatistic mapped from table <posts_statistics>
type PostsStatistic struct {
	PostsID      string `gorm:"column:PostsId;primaryKey" json:"PostsId"`
	ApplaudCount int32  `gorm:"column:ApplaudCount;comment:点赞数量" json:"ApplaudCount"`       // 点赞数量
	ForwardCount int32  `gorm:"column:ForwardCount;comment:分享数量" json:"ForwardCount"`       // 分享数量
	ReplyCount   int32  `gorm:"column:ReplyCount;comment:回复数量" json:"ReplyCount"`           // 回复数量
	ViewCount    int32  `gorm:"column:ViewCount;comment:浏览数量" json:"ViewCount"`             // 浏览数量
	ObjectID     string `gorm:"column:ObjectId;comment:评论对象code 通证 服务商 等" json:"ObjectId"`  // 评论对象code 通证 服务商 等
	CollectCount int32  `gorm:"column:CollectCount;comment:收藏总数" json:"CollectCount"`       // 收藏总数
	PublicUserID string `gorm:"column:PublicUserId;comment:发布用户userid" json:"PublicUserId"` // 发布用户userid
}

// TableName PostsStatistic's table name
func (*PostsStatistic) TableName() string {
	return TableNamePostsStatistic
}
