// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package models

import (
	"time"
)

const TableNameMessageComment = "message_comment"

// MessageComment mapped from table <message_comment>
type MessageComment struct {
	MessageID      string    `gorm:"column:MessageId;primaryKey;comment:评论消息" json:"MessageId"` // 评论消息
	UserID         string    `gorm:"column:UserId;comment:收到评论的用户Id" json:"UserId"`             // 收到评论的用户Id
	CommentUserID  string    `gorm:"column:CommentUserId;comment:评论用户Id" json:"CommentUserId"`  // 评论用户Id
	CommentType    int32     `gorm:"column:CommentType;comment:1回复 2评论" json:"CommentType"`     // 1回复 2评论
	ContentID      string    `gorm:"column:ContentId;comment:内容主键" json:"ContentId"`            // 内容主键
	ObjectID       string    `gorm:"column:ObjectId;comment:回复  评论的主键" json:"ObjectId"`         // 回复  评论的主键
	CreateTime     time.Time `gorm:"column:CreateTime" json:"CreateTime"`
	MessageContent string    `gorm:"column:MessageContent;comment:回复内容" json:"MessageContent"` // 回复内容
}

// TableName MessageComment's table name
func (*MessageComment) TableName() string {
	return TableNameMessageComment
}
