// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package models

import (
	"time"
)

const TableNameCmsCommgradeSearchlog = "cms_commgrade_searchlog"

// CmsCommgradeSearchlog mapped from table <cms_commgrade_searchlog>
type CmsCommgradeSearchlog struct {
	ID         string    `gorm:"column:Id;primaryKey" json:"Id"`
	Name       string    `gorm:"column:Name" json:"Name"`
	Code       string    `gorm:"column:Code" json:"Code"`
	CreateTime time.Time `gorm:"column:CreateTime" json:"CreateTime"`
}

// TableName CmsCommgradeSearchlog's table name
func (*CmsCommgradeSearchlog) TableName() string {
	return TableNameCmsCommgradeSearchlog
}
