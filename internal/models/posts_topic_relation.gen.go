// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package models

import (
	"time"
)

const TableNamePostsTopicRelation = "posts_topic_relation"

// PostsTopicRelation mapped from table <posts_topic_relation>
type PostsTopicRelation struct {
	ID        string    `gorm:"column:Id;primaryKey;comment:商业关联话题" json:"Id"`           // 商业关联话题
	Sort      int32     `gorm:"column:Sort;comment:排序" json:"Sort"`                      // 排序
	PostsID   string    `gorm:"column:PostsId;comment:商业Id" json:"PostsId"`              // 商业Id
	TopicID   string    `gorm:"column:TopicId;comment:话题Id" json:"TopicId"`              // 话题Id
	TopicName string    `gorm:"column:TopicName;comment:话题内容" json:"TopicName"`          // 话题内容
	Creator   string    `gorm:"column:Creator;not null;comment:创建人" json:"Creator"`      // 创建人
	CreatedAt time.Time `gorm:"column:CreatedAt;not null;comment:创建时间" json:"CreatedAt"` // 创建时间
}

// TableName PostsTopicRelation's table name
func (*PostsTopicRelation) TableName() string {
	return TableNamePostsTopicRelation
}
