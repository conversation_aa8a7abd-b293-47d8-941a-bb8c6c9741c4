// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package models

const TableNameScoresetting = "scoresetting"

// Scoresetting mapped from table <scoresetting>
type Scoresetting struct {
	ScoreSettingID string  `gorm:"column:ScoreSettingId;primaryKey;comment:评分小项目配置" json:"ScoreSettingId"`         // 评分小项目配置
	ProjectType    int32   `gorm:"column:ProjectType;comment:项目类型 1交易商 2通证 3项目 4交易所 5钱包 6 服务商" json:"ProjectType"` // 项目类型 1交易商 2通证 3项目 4交易所 5钱包 6 服务商
	ScoreCode      string  `gorm:"column:ScoreCode;comment:小分项Code" json:"ScoreCode"`                              // 小分项Code
	ScoreContent   string  `gorm:"column:ScoreContent;comment:小分项中文描述" json:"ScoreContent"`                        // 小分项中文描述
	Weight         float64 `gorm:"column:Weight;comment:权重" json:"Weight"`                                         // 权重
	Sort           int32   `gorm:"column:Sort;comment:排序" json:"Sort"`                                             // 排序
	Enabled        int32   `gorm:"column:Enabled;comment:是否开启" json:"Enabled"`                                     // 是否开启
}

// TableName Scoresetting's table name
func (*Scoresetting) TableName() string {
	return TableNameScoresetting
}
