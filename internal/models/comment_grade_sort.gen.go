// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package models

import (
	"time"
)

const TableNameCommentGradeSort = "comment_grade_sort"

// CommentGradeSort mapped from table <comment_grade_sort>
type CommentGradeSort struct {
	CommentGradeID string    `gorm:"column:CommentGradeId;primaryKey;comment:评价排序" json:"CommentGradeId"` // 评价排序
	EnterpriseCode string    `gorm:"column:EnterpriseCode;comment:交易商code" json:"EnterpriseCode"`         // 交易商code
	Score          float64   `gorm:"column:Score;comment:算分" json:"Score"`                                // 算分
	UpdateTime     time.Time `gorm:"column:update_time;default:CURRENT_TIMESTAMP" json:"update_time"`
}

// TableName CommentGradeSort's table name
func (*CommentGradeSort) TableName() string {
	return TableNameCommentGradeSort
}
