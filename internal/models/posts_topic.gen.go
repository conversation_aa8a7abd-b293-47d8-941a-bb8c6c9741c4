// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package models

import (
	"time"
)

const TableNamePostsTopic = "posts_topic"

// PostsTopic mapped from table <posts_topic>
type PostsTopic struct {
	TopicID           string    `gorm:"column:TopicId;primaryKey;comment:话题主键" json:"TopicId"`              // 话题主键
	Content           string    `gorm:"column:Content;comment:话题内容" json:"Content"`                         // 话题内容
	BgImage           string    `gorm:"column:BgImage;comment:背景图片" json:"BgImage"`                         // 背景图片
	Intro             string    `gorm:"column:Intro;comment:话题介绍" json:"Intro"`                             // 话题介绍
	CreateUser        string    `gorm:"column:CreateUser;comment:创建人" json:"CreateUser"`                    // 创建人
	CreateCountry     string    `gorm:"column:CreateCountry;comment:创建人国家" json:"CreateCountry"`            // 创建人国家
	TopicStatus       int32     `gorm:"column:TopicStatus;comment:状态 0未审核 1 已审核" json:"TopicStatus"`        // 状态 0未审核 1 已审核
	CreateTime        time.Time `gorm:"column:CreateTime;comment:创建人" json:"CreateTime"`                    // 创建人
	IsDelete          int32     `gorm:"column:IsDelete;comment:是否删除1删除 0未删除" json:"IsDelete"`               // 是否删除1删除 0未删除
	UserID            string    `gorm:"column:UserId;comment:创建人编号" json:"UserId"`                          // 创建人编号
	LanguageCode      string    `gorm:"column:LanguageCode;not null;comment:语言" json:"LanguageCode"`        // 语言
	Auditor           string    `gorm:"column:Auditor;comment:审核人" json:"Auditor"`                          // 审核人
	AuditedAt         time.Time `gorm:"column:AuditedAt;comment:审核时间" json:"AuditedAt"`                     // 审核时间
	Modifier          string    `gorm:"column:Modifier;comment:修改人" json:"Modifier"`                        // 修改人
	ModifiedAt        time.Time `gorm:"column:ModifiedAt;comment:修改时间" json:"ModifiedAt"`                   // 修改时间
	ViewCountSettings int32     `gorm:"column:ViewCountSettings;comment:话题查看数量配置" json:"ViewCountSettings"` // 话题查看数量配置
}

// TableName PostsTopic's table name
func (*PostsTopic) TableName() string {
	return TableNamePostsTopic
}
