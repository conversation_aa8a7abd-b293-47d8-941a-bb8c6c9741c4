// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package models

import (
	"time"
)

const TableNameMessageLike = "message_like"

// MessageLike mapped from table <message_like>
type MessageLike struct {
	MessageLikeID string    `gorm:"column:MessageLikeId;primaryKey;comment:消息点赞" json:"MessageLikeId"` // 消息点赞
	UserID        string    `gorm:"column:UserId;comment:获赞的用户Id" json:"UserId"`                       // 获赞的用户Id
	LikeType      int32     `gorm:"column:LikeType;comment:点赞类型 1内容 2评论 3回复" json:"LikeType"`          // 点赞类型 1内容 2评论 3回复
	LikeUserID    string    `gorm:"column:LikeUserId;comment:点赞用户Id" json:"LikeUserId"`                // 点赞用户Id
	CreateTime    time.Time `gorm:"column:CreateTime;comment:点赞时间" json:"CreateTime"`                  // 点赞时间
	ContentID     string    `gorm:"column:ContentId;comment:点赞所属内容Id" json:"ContentId"`                // 点赞所属内容Id
	ObjectID      string    `gorm:"column:ObjectId;comment:点赞详细 内容 评论 回复Id" json:"ObjectId"`           // 点赞详细 内容 评论 回复Id
}

// TableName MessageLike's table name
func (*MessageLike) TableName() string {
	return TableNameMessageLike
}
