// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package models

import (
	"time"
)

const TableNameReplyTranslate = "reply_translate"

// ReplyTranslate mapped from table <reply_translate>
type ReplyTranslate struct {
	TranslateID     string    `gorm:"column:TranslateId;primaryKey;comment:评论翻译ID" json:"TranslateId"` // 评论翻译ID
	CommentID       string    `gorm:"column:CommentId;not null;comment:评论ID" json:"CommentId"`         // 评论ID
	LanguageCode    string    `gorm:"column:LanguageCode;comment:语言code" json:"LanguageCode"`          // 语言code
	Title           string    `gorm:"column:Title;comment:标题" json:"Title"`                            // 标题
	Content         string    `gorm:"column:Content;comment:评论内容" json:"Content"`                      // 评论内容
	Translator      string    `gorm:"column:Translator;comment:翻译人" json:"Translator"`                 // 翻译人
	TranslateTime   time.Time `gorm:"column:TranslateTime;comment:翻译时间" json:"TranslateTime"`          // 翻译时间
	UpdateTime      time.Time `gorm:"column:UpdateTime;comment:更新时间" json:"UpdateTime"`                // 更新时间
	TranslateFrom   string    `gorm:"column:TranslateFrom" json:"TranslateFrom"`
	TranslateMethod int32     `gorm:"column:TranslateMethod;comment:翻译主体" json:"TranslateMethod"` // 翻译主体
}

// TableName ReplyTranslate's table name
func (*ReplyTranslate) TableName() string {
	return TableNameReplyTranslate
}
