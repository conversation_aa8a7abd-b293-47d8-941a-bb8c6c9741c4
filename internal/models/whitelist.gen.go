// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package models

import (
	"time"
)

const TableNameWhitelist = "whitelist"

// Whitelist mapped from table <whitelist>
type Whitelist struct {
	ID         string    `gorm:"column:Id;primaryKey" json:"Id"`
	TraderCode string    `gorm:"column:TraderCode" json:"TraderCode"`
	AreaCode   string    `gorm:"column:AreaCode" json:"AreaCode"`
	UpdateTime time.Time `gorm:"column:UpdateTime" json:"UpdateTime"`
}

// TableName Whitelist's table name
func (*Whitelist) TableName() string {
	return TableNameWhitelist
}
