// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package models

import (
	"time"
)

const TableNameServiceproviderservicetype = "serviceproviderservicetype"

type ServiceProviderName struct {
	Code         string
	Name         string
	LanguageCode string
}

// Serviceproviderservicetype 服务商服务类型
type Serviceproviderservicetype struct {
	ServiceTypeCode  string    `gorm:"column:ServiceTypeCode;primaryKey;comment:主键" json:"ServiceTypeCode"`         // 主键
	ProjectType      int32     `gorm:"column:ProjectType;not null;comment:系统类型(1:WikiFX,2:Bit)" json:"ProjectType"` // 系统类型(1:WikiFX,2:Bit)
	Name             string    `gorm:"column:Name;not null;comment:类型名称" json:"Name"`                               // 类型名称
	Color            string    `gorm:"column:Color;comment:色值" json:"Color"`                                        // 色值
	Icon             string    `gorm:"column:Icon;comment:icon" json:"Icon"`                                        // icon
	IconGray         string    `gorm:"column:IconGray;comment:暗色icon" json:"IconGray"`                              // 暗色icon
	IsEnabled        bool      `gorm:"column:IsEnabled;not null;comment:是否启用" json:"IsEnabled"`                     // 是否启用
	Order            int32     `gorm:"column:Order;not null;comment:排序" json:"Order"`                               // 排序
	IsOnlyUseService bool      `gorm:"column:IsOnlyUseService;not null;comment:是否仅用于服务分类" json:"IsOnlyUseService"`  // 是否仅用于服务分类
	CreatorName      string    `gorm:"column:CreatorName;not null;comment:创建者" json:"CreatorName"`                  // 创建者
	CreatedDate      time.Time `gorm:"column:CreatedDate;not null;comment:创建时间" json:"CreatedDate"`                 // 创建时间
	UpdatorName      string    `gorm:"column:UpdatorName;not null;comment:修改者" json:"UpdatorName"`                  // 修改者
	UpdatedDate      time.Time `gorm:"column:UpdatedDate;not null;comment:修改时间" json:"UpdatedDate"`                 // 修改时间
	IconExpand       string    `gorm:"column:IconExpand;comment:拓展icon" json:"IconExpand"`                          // 拓展icon
	IconGrayExpand   string    `gorm:"column:IconGrayExpand;comment:拓展暗色icon" json:"IconGrayExpand"`                // 拓展暗色icon
	/*
		面向对象类型
		1：面向投资者的服务商
		2：面向外汇行业的服务商
	*/
	ObjectOrientedType int32  `gorm:"column:ObjectOrientedType;comment:面向对象类型\n1：面向投资者的服务商\n2：面向外汇行业的服务商" json:"ObjectOrientedType"`
	ListImage          string `gorm:"column:ListImage;comment:列表图片" json:"ListImage"`             // 列表图片
	ListImageColor     string `gorm:"column:ListImageColor;comment:列表图片色值" json:"ListImageColor"` // 列表图片色值
	IconWeb            string `gorm:"column:IconWeb;comment:web端icon" json:"IconWeb"`             // web端icon
}

// TableName Serviceproviderservicetype's table name
func (*Serviceproviderservicetype) TableName() string {
	return TableNameServiceproviderservicetype
}
