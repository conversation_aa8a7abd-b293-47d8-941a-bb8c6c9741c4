// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package models

const TableNameCommentGradeStatistic = "comment_grade_statistics"

// CommentGradeStatistic mapped from table <comment_grade_statistics>
type CommentGradeStatistic struct {
	CommentGradeID string `gorm:"column:CommentGradeId;primaryKey" json:"CommentGradeId"`
	ApplaudCount   int32  `gorm:"column:ApplaudCount;comment:点赞数量" json:"ApplaudCount"`       // 点赞数量
	ForwardCount   int32  `gorm:"column:ForwardCount;comment:分享数量" json:"ForwardCount"`       // 分享数量
	ReplyCount     int32  `gorm:"column:ReplyCount;comment:回复数量" json:"ReplyCount"`           // 回复数量
	ViewCount      int32  `gorm:"column:ViewCount;comment:浏览数量" json:"ViewCount"`             // 浏览数量
	ObjectID       string `gorm:"column:ObjectId;comment:评论对象code 通证 服务商 等" json:"ObjectId"`  // 评论对象code 通证 服务商 等
	PublicUserID   string `gorm:"column:PublicUserId;comment:发布用户userid" json:"PublicUserId"` // 发布用户userid
}

// TableName CommentGradeStatistic's table name
func (*CommentGradeStatistic) TableName() string {
	return TableNameCommentGradeStatistic
}
