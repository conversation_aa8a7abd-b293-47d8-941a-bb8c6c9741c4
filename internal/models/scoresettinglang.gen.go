// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package models

const TableNameScoresettinglang = "scoresettinglang"

// Scoresettinglang mapped from table <scoresettinglang>
type Scoresettinglang struct {
	ScoreSettingID string `gorm:"column:ScoreSettingId;primaryKey;comment: 主键" json:"ScoreSettingId"` //  主键
	LanguageCode   string `gorm:"column:LanguageCode" json:"LanguageCode"`
	ScoreContent   string `gorm:"column:ScoreContent;comment:内容翻译" json:"ScoreContent"` // 内容翻译
}

// TableName Scoresettinglang's table name
func (*Scoresettinglang) TableName() string {
	return TableNameScoresettinglang
}
