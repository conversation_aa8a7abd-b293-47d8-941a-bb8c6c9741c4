// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package models

import (
	"time"
)

const TableNamePostsActivity = "posts_activity"

// PostsActivity mapped from table <posts_activity>
type PostsActivity struct {
	ID                string    `gorm:"column:Id;primaryKey;comment:商业活动表" json:"Id"`                                 // 商业活动表
	ActivityTitle     string    `gorm:"column:ActivityTitle;comment:活动名称" json:"ActivityTitle"`                       // 活动名称
	ImageURL          string    `gorm:"column:ImageUrl;comment:活动封面" json:"ImageUrl"`                                 // 活动封面
	Intro             string    `gorm:"column:Intro;comment:活动简介" json:"Intro"`                                       // 活动简介
	StartTime         time.Time `gorm:"column:StartTime;comment:开始时间" json:"StartTime"`                               // 开始时间
	EndTime           time.Time `gorm:"column:EndTime;comment:结束时间" json:"EndTime"`                                   // 结束时间
	LanguageCode      string    `gorm:"column:LanguageCode;comment:语言" json:"LanguageCode"`                           // 语言
	AreaCode          string    `gorm:"column:AreaCode;comment:区域Code" json:"AreaCode"`                               // 区域Code
	Content           string    `gorm:"column:Content;comment:活动内容" json:"Content"`                                   // 活动内容
	TopicID           string    `gorm:"column:TopicId;comment:话题Id" json:"TopicId"`                                   // 话题Id
	LinkType          int32     `gorm:"column:LinkType;comment:跳转类型 1发布帖子 2外链 3内链 4交易商详情页5直播6排行榜7实盘" json:"LinkType"` // 跳转类型 1发布帖子 2外链 3内链 4交易商详情页5直播6排行榜7实盘
	Status            int32     `gorm:"column:Status;comment:0关闭 1开启" json:"Status"`                                  // 0关闭 1开启
	LinkContent       string    `gorm:"column:LinkContent;comment:跳转内容" json:"LinkContent"`                           // 跳转内容
	CreateTime        time.Time `gorm:"column:CreateTime" json:"CreateTime"`
	UpdateTime        time.Time `gorm:"column:UpdateTime" json:"UpdateTime"`
	UpdateUser        string    `gorm:"column:UpdateUser;comment:操作人" json:"UpdateUser"`                  // 操作人
	JoinUserCount     int32     `gorm:"column:JoinUserCount;comment:参与用户数量" json:"JoinUserCount"`         // 参与用户数量
	JoinUserCountShow int32     `gorm:"column:JoinUserCountShow;comment:参与用户数量" json:"JoinUserCountShow"` // 参与用户数量
	StartStatus       int32     `gorm:"column:StartStatus;comment:开始状态" json:"StartStatus"`               // 1进行中 2未开始 3已结束
	LinkAddress       string    `gorm:"column:LinkAddress;comment:内链地址" json:"LinkAddress"`               //内链地址
	IsWonderful       int32     `gorm:"column:IsWonderful;comment:是否是精彩活动" json:"IsWonderful"`            // 是否是精彩活动
	WonderfulTopic    string    `gorm:"column:WonderfulTopic;comment:开始状态" json:"WonderfulTopic"`         // 精彩话题关联话题
	LinkAddressType   int32     `gorm:"column:LinkAddressType;comment:类型 1内链 2外链" json:"LinkAddressType"`
}

// TableName PostsActivity's table name
func (*PostsActivity) TableName() string {
	return TableNamePostsActivity
}
