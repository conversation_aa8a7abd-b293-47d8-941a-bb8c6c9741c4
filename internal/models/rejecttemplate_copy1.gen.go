// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package models

import (
	"time"
)

const TableNameRejecttemplateCopy1 = "rejecttemplate_copy1"

// RejecttemplateCopy1 mapped from table <rejecttemplate_copy1>
type RejecttemplateCopy1 struct {
	TemplateID   string    `gorm:"column:TemplateId;primaryKey" json:"TemplateId"`
	Code         string    `gorm:"column:Code" json:"Code"`
	Type         int32     `gorm:"column:Type" json:"Type"`
	LanguageCode string    `gorm:"column:LanguageCode" json:"LanguageCode"`
	Content      string    `gorm:"column:Content" json:"Content"`
	Creator      string    `gorm:"column:Creator" json:"Creator"`
	CreateTime   time.Time `gorm:"column:CreateTime" json:"CreateTime"`
	Updator      string    `gorm:"column:Updator" json:"Updator"`
	UpdateTime   time.Time `gorm:"column:UpdateTime" json:"UpdateTime"`
	Sequence     int32     `gorm:"column:Sequence" json:"Sequence"`
	Enabled      int32     `gorm:"column:Enabled" json:"Enabled"`
}

// TableName RejecttemplateCopy1's table name
func (*RejecttemplateCopy1) TableName() string {
	return TableNameRejecttemplateCopy1
}
