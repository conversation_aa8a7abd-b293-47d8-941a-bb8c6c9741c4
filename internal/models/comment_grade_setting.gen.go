// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package models

import (
	"time"
)

const TableNameCommentGradeSetting = "comment_grade_setting"

// CommentGradeSetting mapped from table <comment_grade_setting>
type CommentGradeSetting struct {
	CommentGradeSettingID string    `gorm:"column:CommentGradeSettingId;primaryKey;comment:评级类型配置" json:"CommentGradeSettingId"`  // 评级类型配置
	SettingType           int32     `gorm:"column:SettingType;comment:1通用配置 2单一配置" json:"SettingType"`                            // 1通用配置 2单一配置
	AreaCode              string    `gorm:"column:AreaCode;comment:区域Code" json:"AreaCode"`                                       // 区域Code
	EnterpriseCode        string    `gorm:"column:EnterpriseCode;comment:单个配置code" json:"EnterpriseCode"`                         // 单个配置code
	ObjectType            int32     `gorm:"column:ObjectType;comment:1交易商 2通证 3项目 4交易所 5钱包 6 服务商7 券商 8 fx监管机构" json:"ObjectType"` // 1交易商 2通证 3项目 4交易所 5钱包 6 服务商7 券商 8 fx监管机构
	WayType               int32     `gorm:"column:WayType;comment:限制方式类型 1天眼评分 2汇总状态 3监管评分" json:"WayType"`                       // 限制方式类型 1天眼评分 2汇总状态 3监管评分
	Regulation            string    `gorm:"column:Regulation;comment:监管状态详情" json:"Regulation"`                                   // 监管状态详情
	StartType             int32     `gorm:"column:StartType;comment:1大于 2小于 3等于 4大于等于 5小于等于 0空" json:"StartType"`                 // 1大于 2小于 3等于 4大于等于 5小于等于 0空
	StartValue            float64   `gorm:"column:StartValue;comment:条件1的值" json:"StartValue"`                                    // 条件1的值
	EndType               int32     `gorm:"column:EndType;comment:1大于 2小于 3等于 4大于等于 5小于等于 0空" json:"EndType"`                     // 1大于 2小于 3等于 4大于等于 5小于等于 0空
	EndValue              float64   `gorm:"column:EndValue;comment:条件2的值" json:"EndValue"`                                        // 条件2的值
	GradeValues           string    `gorm:"column:GradeValues;comment:1好评 2中评 3差评 逗号隔开" json:"GradeValues"`                       // 1好评 2中评 3差评 逗号隔开
	CreateTime            time.Time `gorm:"column:CreateTime" json:"CreateTime"`
	Enabled               int32     `gorm:"column:Enabled" json:"Enabled"`
	UpdateTime            time.Time `gorm:"column:UpdateTime" json:"UpdateTime"`
	CreateName            string    `gorm:"column:CreateName" json:"CreateName"`
	IsDelete              int32     `gorm:"column:IsDelete;comment:逻辑删除 1删除" json:"IsDelete"` // 逻辑删除 1删除
	UpdateName            string    `gorm:"column:UpdateName" json:"UpdateName"`
}

// TableName CommentGradeSetting's table name
func (*CommentGradeSetting) TableName() string {
	return TableNameCommentGradeSetting
}
