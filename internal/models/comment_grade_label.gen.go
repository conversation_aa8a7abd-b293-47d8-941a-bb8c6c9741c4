// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package models

import (
	"time"
)

const TableNameCommentGradeLabel = "comment_grade_label"

// CommentGradeLabel mapped from table <comment_grade_label>
type CommentGradeLabel struct {
	ID         string    `gorm:"column:Id;primaryKey;comment:评价标签管理" json:"Id"` // 评价标签管理
	Name       string    `gorm:"column:Name" json:"Name"`
	ObjectType int32     `gorm:"column:ObjectType;comment:标签类型 1交易商 2通证 3项目 4交易所 5钱包 6 服务商7 券商 8 fx监管机构" json:"ObjectType"` // 标签类型 1交易商 2通证 3项目 4交易所 5钱包 6 服务商7 券商 8 fx监管机构
	Enabled    int32     `gorm:"column:Enabled;comment:启用禁用" json:"Enabled"`                                                // 启用禁用
	CreateTime time.Time `gorm:"column:CreateTime;comment:创建时间" json:"CreateTime"`                                          // 创建时间
	UpdateName string    `gorm:"column:UpdateName" json:"UpdateName"`
	UpdateTime time.Time `gorm:"column:UpdateTime;comment:修改时间" json:"UpdateTime"` // 修改时间
	IsDelete   int32     `gorm:"column:IsDelete;comment:是否删除" json:"IsDelete"`     // 是否删除
}

// TableName CommentGradeLabel's table name
func (*CommentGradeLabel) TableName() string {
	return TableNameCommentGradeLabel
}
