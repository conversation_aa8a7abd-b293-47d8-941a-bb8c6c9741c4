// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package models

import (
	"time"
)

const TableNameCommentGrade = "comment_grade"

// CommentGrade mapped from table <comment_grade>
type CommentGrade struct {
	CommentGradeID     string    `gorm:"column:CommentGradeId;primaryKey;comment:口碑Id" json:"CommentGradeId"` // 口碑Id
	CommentGradeCode   string    `gorm:"column:CommentGradeCode" json:"CommentGradeCode"`
	ObjectType         int32     `gorm:"column:ObjectType;comment:1交易商 2通证 3项目 4交易所 5钱包 6 服务商7 券商 8 fx监管机构" json:"ObjectType"` // 1交易商 2通证 3项目 4交易所 5钱包 6 服务商7 券商 8 fx监管机构
	ObjectID           string    `gorm:"column:ObjectId;comment:评受平方Code" json:"ObjectId"`                                     // 评受平方Code
	UserID             string    `gorm:"column:UserId" json:"UserId"`
	CreateTime         time.Time `gorm:"column:CreateTime" json:"CreateTime"`
	UpdateTime         time.Time `gorm:"column:UpdateTime" json:"UpdateTime"`
	Title              string    `gorm:"column:Title" json:"Title"`
	Content            string    `gorm:"column:Content" json:"Content"`
	LanguageCode       string    `gorm:"column:LanguageCode" json:"LanguageCode"`
	IPCountryCode      string    `gorm:"column:IpCountryCode" json:"IpCountryCode"`
	CountryCode        string    `gorm:"column:CountryCode" json:"CountryCode"`
	IPAddress          string    `gorm:"column:IpAddress" json:"IpAddress"`
	Status             int32     `gorm:"column:Status;comment:状态（100：未审核  200：审核通过 401：审核未通过; 499 ：待回复）" json:"Status"` // 状态（100：未审核  200：审核通过 401：审核未通过; 499 ：待回复）
	Auditor            string    `gorm:"column:Auditor" json:"Auditor"`
	AuditTime          time.Time `gorm:"column:AuditTime" json:"AuditTime"`
	FeedBack           string    `gorm:"column:FeedBack" json:"FeedBack"`
	SysType            int32     `gorm:"column:SysType;comment:设备系统类型（0：ios  1：android  3：pc客户端）" json:"SysType"`                                                                                                                              // 设备系统类型（0：ios  1：android  3：pc客户端）
	AppType            int32     `gorm:"column:AppType;comment:app类型（1：fxeye  2：wikifx 3：wikifx2.0  4：wikibit 5：fxeye_testflight版 6：fxeye_极速版  7：fxeye_维权版  8：wikibit_testflight版   9：wikibit_lite版  10：wikibit_合规版  999：web）" json:"AppType"` // app类型（1：fxeye  2：wikifx 3：wikifx2.0  4：wikibit 5：fxeye_testflight版 6：fxeye_极速版  7：fxeye_维权版  8：wikibit_testflight版   9：wikibit_lite版  10：wikibit_合规版  999：web）
	IsOfficialReply    int32     `gorm:"column:IsOfficialReply;comment:是否官方回复" json:"IsOfficialReply"`                                                                                                                                         // 是否官方回复
	OfficialReply      string    `gorm:"column:OfficialReply;comment:官方回复" json:"OfficialReply"`                                                                                                                                               // 官方回复
	OfficialReplyTime  time.Time `gorm:"column:OfficialReplyTime;comment:回复时间" json:"OfficialReplyTime"`                                                                                                                                       // 回复时间
	IsEnbaled          int32     `gorm:"column:IsEnbaled;comment:是否有效" json:"IsEnbaled"`                                                                                                                                                       // 是否有效
	UserInfo           string    `gorm:"column:UserInfo;comment:详细信息" json:"UserInfo"`                                                                                                                                                         // 详细信息
	IsAnonymity        bool      `gorm:"column:IsAnonymity;comment:是否是匿名" json:"IsAnonymity"`                                                                                                                                                  // 是否是匿名
	IsExposure         bool      `gorm:"column:IsExposure;comment:是否曝光" json:"IsExposure"`                                                                                                                                                     // 是否曝光
	ProjectType        int32     `gorm:"column:ProjectType;default:2;comment:1 fx 2bit 3 stock 4 fx监管机构" json:"ProjectType"`                                                                                                                   // 1 fx 2bit 3 stock 4 fx监管机构
	IsAi               int32     `gorm:"column:IsAi;comment:是否是ai" json:"IsAi"`                                                                                                                                                                // 是否是ai
	CurrentIdentity    int32     `gorm:"column:CurrentIdentity;comment:当前身份 1个人 2企业" json:"CurrentIdentity"`                                                                                                                                   // 当前身份 1个人 2企业
	EnterpriseInfo     string    `gorm:"column:EnterpriseInfo" json:"EnterpriseInfo"`
	OriginUserID       string    `gorm:"column:OriginUserId;comment:原始userid" json:"OriginUserId"`                         // 原始userid
	Score              float64   `gorm:"column:Score;default:1.000000000;comment:评分" json:"Score"`                         // 评分
	Grade              int32     `gorm:"column:grade;comment:评论等级 好评 2中评 3差评4 调解成功" json:"grade"`                          // 评论等级 好评 2中评 3差评4 调解成功
	IsMediationSuccess int32     `gorm:"column:IsMediationSuccess;comment:调解状态成功 -1 调解关闭 1调解成功" json:"IsMediationSuccess"` // 调解状态成功 -1 调解关闭 1调解成功
	NegativeReason     int32     `gorm:"column:NegativeReason;comment:差评原因 1无法出金 2滑点严重 3诱导欺诈 其他类型" json:"NegativeReason"`  // 差评原因 1无法出金 2滑点严重 3诱导欺诈 其他类型
	Origingrade        int32     `gorm:"column:origingrade;comment:原始评价类型" json:"origingrade"`                             // 原始评价类型
	Isupdategrade      int32     `gorm:"column:isupdategrade;comment:是否可以修改评价类型" json:"isupdategrade"`                     // 是否可以修改评价类型
	IsTrans            int32     `gorm:"column:IsTrans;comment:是否翻译" json:"IsTrans"`                                       // 是否翻译
	IsTransDoDateTime  time.Time `gorm:"column:IsTransDoDateTime;comment:翻译执行时间" json:"IsTransDoDateTime"`                 // 翻译执行时间
	IsResolved         int32     `gorm:"column:IsResolved;comment:是否完成" json:"IsResolved"`                                 // 是否完成
	IsSync             int32     `gorm:"column:IsSync;comment:是否同步到动态" json:"IsSync"`                                      // 是否同步到动态
}

// TableName CommentGrade's table name
func (*CommentGrade) TableName() string {
	return TableNameCommentGrade
}
