#!/usr/bin/env bash
 
# 使用方法：
# ./genModel.sh usercenter user
# ./genModel.sh usercenter user_auth
# 再将./genModel下的文件剪切到对应服务的model目录里面，记得改package
 
 
#生成的表名
#tables=$1
#包名
modelPkgName=models
#表生成的genmodel目录
outPath="./internal/models/a1"
# 数据库配置
host="testdb-mysql.fxeyeinterface.com"
port=3306
dbname="wikicommunity"
username=wikifx
passwd=Wikifx2023
 
#echo "开始创建库：$dbname 的表：$1"
gentool -dsn "${username}:${passwd}@tcp(${host}:${port})/${dbname}?charset=utf8mb4&parseTime=True&loc=Local" -tables "posts_activity_posts" -onlyModel -modelPkgName="${modelPkgName}" -outPath="${outPath}"
 
 