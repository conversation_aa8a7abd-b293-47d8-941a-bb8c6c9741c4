// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package models

import (
	"time"
)

const TableNamePostsTopicCollect = "posts_topic_collect"

// PostsTopicCollect mapped from table <posts_topic_collect>
type PostsTopicCollect struct {
	ID        string    `gorm:"column:Id;primaryKey" json:"Id"`
	TopicID   string    `gorm:"column:TopicId;not null" json:"TopicId"`
	UserID    string    `gorm:"column:UserId;not null" json:"UserId"`
	CreatedAt time.Time `gorm:"column:CreatedAt;not null" json:"CreatedAt"`
}

// TableName PostsTopicCollect's table name
func (*PostsTopicCollect) TableName() string {
	return TableNamePostsTopicCollect
}

type PostsTopicCollectView struct {
	PostsTopicCollect
	TopicName string
}
