// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package models

import (
	"time"
)

const TableNameAttachment = "attachment"

// Attachment mapped from table <attachment>
type Attachment struct {
	AttachID    string    `gorm:"column:AttachId;primaryKey;comment:附件ID" json:"AttachId"`                     // 附件ID
	RelatedID   string    `gorm:"column:RelatedId;not null;comment:关联ID" json:"RelatedId"`                     // 关联ID
	Content     string    `gorm:"column:Content;not null;comment:关联内容" json:"Content"`                         // 关联内容
	Sequence    int32     `gorm:"column:Sequence;not null;comment:排序" json:"Sequence"`                         // 排序
	CreateTime  time.Time `gorm:"column:CreateTime;not null;comment:附件上传时间" json:"CreateTime"`                 // 附件上传时间
	DataSource  int32     `gorm:"column:DataSource;comment:标记数据来源1:eco 2:集市 3:曝光 4:评论 0:社区" json:"DataSource"` // 标记数据来源1:eco 2:集市 3:曝光 4:评论 0:社区
	Width       int32     `gorm:"column:Width;comment:图片宽度" json:"Width"`                                      // 图片宽度
	Height      int32     `gorm:"column:Height;comment:图片高度" json:"Height"`                                    // 图片高度
	ContentLang string    `gorm:"column:ContentLang;comment:多语言图片" json:"ContentLang"`                         // 多语言图片
	Translation string    `gorm:"column:Translation;comment:多语言图片" json:"Translation"`                         // 多图片翻译
}

// TableName Attachment's table name
func (*Attachment) TableName() string {
	return TableNameAttachment
}
