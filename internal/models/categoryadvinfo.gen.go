// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package models

import (
	"time"
)

const TableNameCategoryadvinfo = "categoryadvinfo"

// Categoryadvinfo mapped from table <categoryadvinfo>
type Categoryadvinfo struct {
	ID                string    `gorm:"column:id;primaryKey" json:"id"`
	CategoryID        string    `gorm:"column:CategoryId" json:"CategoryId"`
	Title             string    `gorm:"column:Title" json:"Title"`
	TitleLanguageCode string    `gorm:"column:TitleLanguageCode" json:"TitleLanguageCode"`
	Paperwork         string    `gorm:"column:Paperwork" json:"Paperwork"`
	PwLanguageCode    string    `gorm:"column:PwLanguageCode" json:"PwLanguageCode"`
	Color             string    `gorm:"column:Color" json:"Color"`
	Background        string    `gorm:"column:Background" json:"Background"`
	OrderNum          int32     `gorm:"column:OrderNum" json:"OrderNum"`
	IsEnabled         int32     `gorm:"column:IsEnabled" json:"IsEnabled"`
	UpdateTime        time.Time `gorm:"column:UpdateTime" json:"UpdateTime"`
	Updator           string    `gorm:"column:Updator" json:"Updator"`
	IsDel             int32     `gorm:"column:IsDel" json:"IsDel"`
	BackgroundAR      string    `gorm:"column:BackgroundAR" json:"BackgroundAR"`
}

// TableName Categoryadvinfo's table name
func (*Categoryadvinfo) TableName() string {
	return TableNameCategoryadvinfo
}
