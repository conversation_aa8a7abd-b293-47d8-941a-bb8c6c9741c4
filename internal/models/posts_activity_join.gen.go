// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package models

import (
	"time"
)

const TableNamePostsActivityJoin = "posts_activity_join"

// PostsActivityJoin mapped from table <posts_activity_join>
type PostsActivityJoin struct {
	ID            string    `gorm:"column:Id;primaryKey;comment:活动用户参数Id" json:"Id"` // 活动用户参数Id
	ActivityID    string    `gorm:"column:ActivityId" json:"ActivityId"`
	UserID        string    `gorm:"column:UserId" json:"UserId"`
	CreateTime    time.Time `gorm:"column:CreateTime" json:"CreateTime"`
	AvatarAddress string    `gorm:"column:AvatarAddress" json:"AvatarAddress"`
}

// TableName PostsActivityJoin's table name
func (*PostsActivityJoin) TableName() string {
	return TableNamePostsActivityJoin
}
