// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package models

import (
	"time"
)

const TableNameCommentGradeUpdatetransrecord = "comment_grade_updatetransrecord"

// CommentGradeUpdatetransrecord mapped from table <comment_grade_updatetransrecord>
type CommentGradeUpdatetransrecord struct {
	ID             string    `gorm:"column:Id;primaryKey;comment:评价翻译修改记录" json:"Id"`           // 评价翻译修改记录
	CommentGradeID string    `gorm:"column:CommentGradeId;comment:评价Id" json:"CommentGradeId"`  // 评价Id
	LanguageCode   string    `gorm:"column:LanguageCode;comment:语言" json:"LanguageCode"`        // 语言
	UpdateTime     time.Time `gorm:"column:UpdateTime;comment:修改时间" json:"UpdateTime"`          // 修改时间
	UpdateName     string    `gorm:"column:UpdateName;comment:修改名字" json:"UpdateName"`          // 修改名字
	BeforeContent  string    `gorm:"column:BeforeContent;comment:修改之前的内容" json:"BeforeContent"` // 修改之前的内容
	Content        string    `gorm:"column:Content;comment:修改内容" json:"Content"`                // 修改内容
}

// TableName CommentGradeUpdatetransrecord's table name
func (*CommentGradeUpdatetransrecord) TableName() string {
	return TableNameCommentGradeUpdatetransrecord
}
