// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package models

import (
	"time"
)

const TableNameTopic = "topic"

// Topic mapped from table <topic>
type Topic struct {
	TopicID           string    `gorm:"column:TopicId;primaryKey" json:"TopicId"`
	TopicCode         string    `gorm:"column:TopicCode" json:"TopicCode"`
	UserID            string    `gorm:"column:UserId;comment:集市服务关联用户" json:"UserId"` // 集市服务关联用户
	CategoryID        string    `gorm:"column:CategoryId" json:"CategoryId"`
	TraderCode        string    `gorm:"column:TraderCode;comment:交易商/服务商code" json:"TraderCode"` // 交易商/服务商code
	LanguageCode      string    `gorm:"column:LanguageCode;comment:语言code" json:"LanguageCode"`  // 语言code
	Title             string    `gorm:"column:Title" json:"Title"`
	CountryCode       string    `gorm:"column:CountryCode" json:"CountryCode"`
	CreateTime        time.Time `gorm:"column:CreateTime" json:"CreateTime"`
	ShowTime          time.Time `gorm:"column:ShowTime" json:"ShowTime"`
	IPAddress         string    `gorm:"column:IpAddress" json:"IpAddress"`
	Location          string    `gorm:"column:Location" json:"Location"`
	Status            int32     `gorm:"column:Status;comment:100:未审核;200:审核通过;401:审核未通过;407:隐藏;408:后台隐藏;555待删除556已删除" json:"Status"` // 100:未审核;200:审核通过;401:审核未通过;407:隐藏;408:后台隐藏;555待删除556已删除
	Auditor           string    `gorm:"column:Auditor" json:"Auditor"`
	AuditTime         time.Time `gorm:"column:AuditTime" json:"AuditTime"`
	Content           string    `gorm:"column:Content" json:"Content"`
	HTMLContent       string    `gorm:"column:HtmlContent;comment:html内容" json:"HtmlContent"` // html内容
	Nationalflag      string    `gorm:"column:Nationalflag" json:"Nationalflag"`
	PhoneArea         string    `gorm:"column:PhoneArea" json:"PhoneArea"`
	Phone             string    `gorm:"column:Phone" json:"Phone"`
	Email             string    `gorm:"column:Email" json:"Email"`
	Ins               string    `gorm:"column:Ins" json:"Ins"`
	WeChat            string    `gorm:"column:WeChat" json:"WeChat"`
	Facebook          string    `gorm:"column:Facebook" json:"Facebook"`
	Twitter           string    `gorm:"column:Twitter" json:"Twitter"`
	QQ                string    `gorm:"column:QQ" json:"QQ"`
	OrginType         int32     `gorm:"column:OrginType" json:"OrginType"`
	JSONContent       string    `gorm:"column:JsonContent" json:"JsonContent"`
	NewJSONContent    string    `gorm:"column:NewJsonContent;comment:动态字段json存储" json:"NewJsonContent"`           // 动态字段json存储
	RepetitiveID      string    `gorm:"column:RepetitiveId;comment:与当前集市重复率最高的一篇集市的唯一标识ID" json:"RepetitiveId"`   // 与当前集市重复率最高的一篇集市的唯一标识ID
	RepetitiveRate    float64   `gorm:"column:RepetitiveRate;comment:最高重复率" json:"RepetitiveRate"`                // 最高重复率
	RateValue         float64   `gorm:"column:RateValue;comment:集市相似度" json:"RateValue"`                          // 集市相似度
	TopicType         int32     `gorm:"column:TopicType;default:1;comment:集市类型 1集市2服务商服务3交易商服务" json:"TopicType"` // 集市类型 1集市2服务商服务3交易商服务
	BelongCode        string    `gorm:"column:BelongCode;comment:所属Code" json:"BelongCode"`                       // 所属Code
	Whatsapp          string    `gorm:"column:whatsapp" json:"whatsapp"`
	SiteURL           string    `gorm:"column:SiteUrl" json:"SiteUrl"`
	BelongUserID      string    `gorm:"column:BelongUserId" json:"BelongUserId"`
	FeedBack          string    `gorm:"column:FeedBack" json:"FeedBack"`
	DataSource        int32     `gorm:"column:DataSource;comment:1 老库集市2 新库eco" json:"DataSource"` // 1 老库集市2 新库eco
	JSONContentExcept string    `gorm:"column:JsonContentExcept" json:"JsonContentExcept"`
	BackgroudImage    string    `gorm:"column:BackgroudImage" json:"BackgroudImage"`
	IsDeleted         bool      `gorm:"column:IsDeleted;not null" json:"IsDeleted"`
	ClientPort        string    `gorm:"column:ClientPort" json:"ClientPort"`
	ClientMac         string    `gorm:"column:ClientMac" json:"ClientMac"`
	Tags              string    `gorm:"column:Tags" json:"Tags"`
	CurrentIdentity   int32     `gorm:"column:CurrentIdentity;default:1;comment:1 个人 2 企业" json:"CurrentIdentity"` // 1 个人 2 企业
	/*
		服务商交易商详情
		服务商交易商详情
	*/
	EnterpriseInfo  string `gorm:"column:EnterpriseInfo;comment:服务商交易商详情\n服务商交易商详情" json:"EnterpriseInfo"`
	PubBelongUserID string `gorm:"column:PubBelongUserId" json:"PubBelongUserId"`
	BusCard         string `gorm:"column:BusCard;comment:名片" json:"BusCard"` // 名片
}

// TableName Topic's table name
func (*Topic) TableName() string {
	return TableNameTopic
}
