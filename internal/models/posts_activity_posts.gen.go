// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package models

const TableNamePostsActivityPost = "posts_activity_posts"

// PostsActivityPost mapped from table <posts_activity_posts>
type PostsActivityPost struct {
	ID         string `gorm:"column:Id;primaryKey;comment:活动帖子关联表" json:"Id"` // 活动帖子关联表
	PostsID    string `gorm:"column:PostsId" json:"PostsId"`
	ActivityID string `gorm:"column:ActivityId;comment:活动Id" json:"ActivityId"` // 活动Id
}

// TableName PostsActivityPost's table name
func (*PostsActivityPost) TableName() string {
	return TableNamePostsActivityPost
}
