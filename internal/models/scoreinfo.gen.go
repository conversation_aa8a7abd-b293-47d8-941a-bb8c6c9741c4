// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package models

const TableNameScoreinfo = "scoreinfo"

// Scoreinfo mapped from table <scoreinfo>
type Scoreinfo struct {
	ID             string  `gorm:"column:Id;primaryKey" json:"Id"`
	ScoreInfo      string  `gorm:"column:ScoreInfo;comment:[{"code1":4.5},{"code2":3.5}]" json:"ScoreInfo"` // [{"code1":4.5},{"code2":3.5}]
	TotalScore     float64 `gorm:"column:TotalScore;comment:总分" json:"TotalScore"`                          // 总分
	CommentGradeID string  `gorm:"column:CommentGradeId;not null" json:"CommentGradeId"`
	CalculateScore float64 `gorm:"column:CalculateScore;default:0.000000000" json:"CalculateScore"`
}

// TableName Scoreinfo's table name
func (*Scoreinfo) TableName() string {
	return TableNameScoreinfo
}
