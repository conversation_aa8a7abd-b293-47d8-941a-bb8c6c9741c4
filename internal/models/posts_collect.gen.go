// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package models

import (
	"time"
)

const TableNamePostsCollect = "posts_collect"

// PostsCollect mapped from table <posts_collect>
type PostsCollect struct {
	ID          string    `gorm:"column:Id;primaryKey;comment:用户收藏表主键" json:"Id"`                    // 用户收藏表主键
	UserID      string    `gorm:"column:UserId;not null;comment:用户" json:"UserId"`                   // 用户
	CreateTime  time.Time `gorm:"column:CreateTime;not null;comment:创建时间" json:"CreateTime"`         // 创建时间
	PostsID     string    `gorm:"column:PostsId;not null;comment:帖子Id" json:"PostsId"`               // 帖子Id
	ObjectType  int32     `gorm:"column:ObjectType;not null;comment:收藏对象类型" json:"ObjectType"`       // 收藏对象类型 1:article(文章) 2:exposure(曝光) 3:discover(发现) 4:trader(交易商) 5:survey(实勘) 6:mediate(调解) 7:flash(快讯) 8:disclosure(披露) 9:comment(评价)
	ReleaseType int32     `gorm:"column:PostsId;not null;comment:帖子Id" json:"PostsId"`               // 收藏对象大概类型 1商业 2动态
	PostsUserId string    `gorm:"column:PostsUserId;not null;comment:发布用户userid" json:"PostsUserId"` // 发布用户userid
}

// TableName PostsCollect's table name
func (*PostsCollect) TableName() string {
	return TableNamePostsCollect
}
