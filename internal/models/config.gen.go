// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package models

import (
	"time"
)

const TableNameConfig = "config"

// Config mapped from table <config>
type Config struct {
	ID          int32     `gorm:"column:id;primaryKey;autoIncrement:true;comment:主键" json:"id"` // 主键
	ConfigType  string    `gorm:"column:config_type;not null;comment:配置键" json:"config_type"`   // 配置键
	ConfigValue string    `gorm:"column:config_value;not null;comment:配置值" json:"config_value"` // 配置值
	CreatedAt   time.Time `gorm:"column:created_at;not null;comment:创建时间" json:"created_at"`    // 创建时间
	Creator     string    `gorm:"column:creator;not null;comment:创建人" json:"creator"`           // 创建人
	ModifiedAt  time.Time `gorm:"column:modified_at;not null;comment:修改时间" json:"modified_at"`  // 修改时间
	Modifier    string    `gorm:"column:modifier;comment:修改人" json:"modifier"`                  // 修改人
}

// TableName Config's table name
func (*Config) TableName() string {
	return TableNameConfig
}
