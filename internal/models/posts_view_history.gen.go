// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package models

import (
	"time"
)

const TableNamePostsViewHistory = "posts_view_history"

// PostsViewHistory mapped from table <posts_view_history>
type PostsViewHistory struct {
	ID         string    `gorm:"column:Id;primaryKey;comment:用户浏览历史记录" json:"Id"` // 用户浏览历史记录
	PostsID    string    `gorm:"column:PostsId" json:"PostsId"`
	PostsType  int32     `gorm:"column:PostsType;comment:1商业2 动态" json:"PostsType"`                                                                                                                                  // 1商业2 动态
	ObjectType int32     `gorm:"column:ObjectType;comment:收藏对象类型 1:article(文章) 2:exposure(曝光) 3:discover(发现) 4:trader(交易商) 5:survey(实勘) 6:mediate(调解) 7:flash(快讯) 8:disclosure(披露) 9:comment(评价)" json:"ObjectType"` // 收藏对象类型 1:article(文章) 2:exposure(曝光) 3:discover(发现) 4:trader(交易商) 5:survey(实勘) 6:mediate(调解) 7:flash(快讯) 8:disclosure(披露) 9:comment(评价)
	UserID     string    `gorm:"column:UserId" json:"UserId"`
	CreateTime time.Time `gorm:"column:CreateTime;comment:创建时间" json:"CreateTime"` // 创建时间
	PostsUserId string `gorm:"column:PostsUserId;comment:发布用户userid" json:"PostsUserId"` // 发布用户userid
}

// TableName PostsViewHistory's table name
func (*PostsViewHistory) TableName() string {
	return TableNamePostsViewHistory
}
