// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package models

import (
	"time"
)

const TableNameServiceCount = "service_count"

// ServiceCount 商业语言数量统计表
type ServiceCount struct {
	ID           int32     `gorm:"column:id;primaryKey;autoIncrement:true;comment:主键" json:"id"`  // 主键
	ServiceType  string    `gorm:"column:service_type;not null;comment:商业类型" json:"service_type"` // 商业类型
	LanguageCode string    `gorm:"column:language_code;not null;comment:语言" json:"language_code"` // 语言
	Total        int32     `gorm:"column:total;not null" json:"total"`
	UpdatedAt    time.Time `gorm:"column:updated_at;comment:更新时间" json:"updated_at"` // 更新时间
}

// TableName ServiceCount's table name
func (*ServiceCount) TableName() string {
	return TableNameServiceCount
}
