// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package models

import (
	"time"
)

const TableNameRunscorelog = "runscorelog"

// Runscorelog mapped from table <runscorelog>
type Runscorelog struct {
	RunScoreLogID string    `gorm:"column:RunScoreLogId;primaryKey;comment:跑分主键" json:"RunScoreLogId"`   // 跑分主键
	Code          string    `gorm:"column:Code;not null;comment:code" json:"Code"`                       // code
	CodeType      int32     `gorm:"column:CodeType;comment:1交易商 2通证 3项目 4交易所 5钱包 6 服务商" json:"CodeType"` // 1交易商 2通证 3项目 4交易所 5钱包 6 服务商
	CreateTime    time.Time `gorm:"column:CreateTime" json:"CreateTime"`
	StartTime     time.Time `gorm:"column:StartTime" json:"StartTime"`
	EndTime       time.Time `gorm:"column:EndTime;comment:完成时间" json:"EndTime"` // 完成时间
	CreateName    string    `gorm:"column:CreateName;not null" json:"CreateName"`
	Status        int32     `gorm:"column:Status;comment:状态 0进行中 1完成 2失败" json:"Status"` // 状态 0进行中 1完成 2失败
}

// TableName Runscorelog's table name
func (*Runscorelog) TableName() string {
	return TableNameRunscorelog
}
