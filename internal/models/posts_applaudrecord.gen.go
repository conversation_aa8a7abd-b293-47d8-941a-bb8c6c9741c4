// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package models

import (
	"time"
)

const TableNamePostsApplaudrecord = "posts_applaudrecord"

// PostsApplaudrecord mapped from table <posts_applaudrecord>
type PostsApplaudrecord struct {
	ApplaudRecordID string    `gorm:"column:ApplaudRecordId;primaryKey;comment:动态点赞记录表" json:"ApplaudRecordId"` // 动态点赞记录表
	UserID          string    `gorm:"column:UserId;comment:点赞用户" json:"UserId"`                                 // 点赞用户
	CreateTime      time.Time `gorm:"column:CreateTime" json:"CreateTime"`
	PostsID         string    `gorm:"column:PostsId" json:"PostsId"`
	PostsUserId string `gorm:"column:PostsUserId;comment:发布用户userid" json:"PostsUserId"` // 发布用户userid
}

// TableName PostsApplaudrecord's table name
func (*PostsApplaudrecord) TableName() string {
	return TableNamePostsApplaudrecord
}

type ApplaudRecordModel struct {
	PostsId string //帖子Id
}
