// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package models

import (
	"time"
)

const TableNameTemplatedynamicvalue = "templatedynamicvalues"

// Templatedynamicvalue mapped from table <templatedynamicvalues>
type Templatedynamicvalue struct {
	ID           string    `gorm:"column:id;primaryKey" json:"id"`
	Fieldvalue   string    `gorm:"column:fieldvalue" json:"fieldvalue"`
	Status       int32     `gorm:"column:status" json:"status"`
	Createuser   string    `gorm:"column:createuser" json:"createuser"`
	Enumvalue    int32     `gorm:"column:enumvalue" json:"enumvalue"`
	Languagelist string    `gorm:"column:languagelist" json:"languagelist"`
	Datasource   int32     `gorm:"column:datasource;comment:1.老系统2.新eco" json:"datasource"` // 1.老系统2.新eco
	Createtime   time.Time `gorm:"column:createtime" json:"createtime"`
	Ordernum     int32     `gorm:"column:ordernum" json:"ordernum"`
}

// TableName Templatedynamicvalue's table name
func (*Templatedynamicvalue) TableName() string {
	return TableNameTemplatedynamicvalue
}
