// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package models

import (
	"time"
)

const TableNamePostsTopicStatistic = "posts_topic_statistics"

type UserTopicStatisticView struct {
	UserId       string
	ApplaudCount int
}

// PostsTopicStatistic mapped from table <posts_topic_statistics>
type PostsTopicStatistic struct {
	TopicID          string    `gorm:"column:TopicId;primaryKey;comment:商业话题统计" json:"TopicId"`        // 商业话题统计
	ViewCount        int32     `gorm:"column:ViewCount;comment:访问时间" json:"ViewCount"`                 // 访问时间
	PostsCount       int32     `gorm:"column:PostsCount;comment:引用话题帖子数量" json:"PostsCount"`           // 引用话题帖子数量
	ParticipantCount int32     `gorm:"column:ParticipantCount;comment:话题参与数量" json:"ParticipantCount"` // 话题参与数量
	TopicViewCount   int32     `gorm:"column:TopicViewCount;comment:话题加帖子浏览量" json:"TopicViewCount"`   // 话题加帖子浏览量
	LastUpdateTime   time.Time `gorm:"column:LastUpdateTime" json:"LastUpdateTime"`
	CollectCount     int32     `gorm:"column:CollectCount;comment:帖子收藏数量" json:"CollectCount"` // 话题加帖子浏览量
}

// TableName PostsTopicStatistic's table name
func (*PostsTopicStatistic) TableName() string {
	return TableNamePostsTopicStatistic
}
