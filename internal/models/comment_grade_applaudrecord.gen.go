// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package models

import (
	"time"
)

const TableNameCommentGradeApplaudrecord = "comment_grade_applaudrecord"

// CommentGradeApplaudrecord mapped from table <comment_grade_applaudrecord>
type CommentGradeApplaudrecord struct {
	ApplaudRecordID string    `gorm:"column:ApplaudRecordId;primaryKey;comment:动态点赞记录表" json:"ApplaudRecordId"` // 动态点赞记录表
	UserID          string    `gorm:"column:UserId;comment:点赞用户" json:"UserId"`                                 // 点赞用户
	CreateTime      time.Time `gorm:"column:CreateTime" json:"CreateTime"`
	CommentGradeID  string    `gorm:"column:CommentGradeId" json:"CommentGradeId"`
	PublishUserID   string    `gorm:"column:PublishUserId;comment:发布动态的用户" json:"PublishUserId"` // 发布动态的用户
}

// TableName CommentGradeApplaudrecord's table name
func (*CommentGradeApplaudrecord) TableName() string {
	return TableNameCommentGradeApplaudrecord
}
