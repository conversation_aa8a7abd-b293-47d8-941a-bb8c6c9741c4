// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package models

import (
	"time"
)

const TableNameTemplatedynamicfield = "templatedynamicfields"

// Templatedynamicfield mapped from table <templatedynamicfields>
type Templatedynamicfield struct {
	ID              string    `gorm:"column:id;primaryKey" json:"id"`
	Name            string    `gorm:"column:name" json:"name"`
	Type            int32     `gorm:"column:type;not null;comment:1 输入 2 单选 3 多选" json:"type"` // 1 输入 2 单选 3 多选
	Isrequired      int32     `gorm:"column:isrequired" json:"isrequired"`
	Inputvaluetype  int32     `gorm:"column:Inputvaluetype;comment:1 数字 2 文本" json:"Inputvaluetype"` // 1 数字 2 文本
	Languagelist    string    `gorm:"column:languagelist" json:"languagelist"`
	Barinfoid       string    `gorm:"column:barinfoid" json:"barinfoid"`
	Status          int32     `gorm:"column:status" json:"status"`
	Createuser      string    `gorm:"column:createuser" json:"createuser"`
	Createtime      time.Time `gorm:"column:createtime" json:"createtime"`
	Dynamicvalueids string    `gorm:"column:dynamicvalueids" json:"dynamicvalueids"`
	Updateuser      string    `gorm:"column:updateuser" json:"updateuser"`
	Updatetime      time.Time `gorm:"column:updatetime" json:"updatetime"`
	Datasource      int32     `gorm:"column:datasource;comment:1.老系统2.新eco" json:"datasource"` // 1.老系统2.新eco
	Ordernum        int32     `gorm:"column:ordernum" json:"ordernum"`
	Icon            string    `gorm:"column:Icon" json:"Icon"`
}

// TableName Templatedynamicfield's table name
func (*Templatedynamicfield) TableName() string {
	return TableNameTemplatedynamicfield
}
