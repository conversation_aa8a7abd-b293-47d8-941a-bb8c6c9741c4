// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package models

import (
	"time"
)

const TableNamePostsUserRelation = "posts_user_relation"

// PostsUserRelation mapped from table <posts_user_relation>
type PostsUserRelation struct {
	ID        string    `gorm:"column:Id;primaryKey;comment:商业@用户表" json:"Id"` // 商业@用户表
	PostsID   string    `gorm:"column:PostsId;comment:帖子Id" json:"PostsId"`    // 帖子Id
	Sort      int32     `gorm:"column:Sort" json:"Sort"`
	UserID    string    `gorm:"column:UserId" json:"UserId"`
	NickName  string    `gorm:"column:NickName" json:"NickName"`
	Creator   string    `gorm:"column:Creator;not null;comment:创建人" json:"Creator"`      // 创建人
	CreatedAt time.Time `gorm:"column:CreatedAt;not null;comment:创建时间" json:"CreatedAt"` // 创建时间
}

// TableName PostsUserRelation's table name
func (*PostsUserRelation) TableName() string {
	return TableNamePostsUserRelation
}
