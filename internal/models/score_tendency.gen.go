// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package models

import (
	"time"
)

const TableNameScoreTendency = "score_tendency"

// ScoreTendency mapped from table <score_tendency>
type ScoreTendency struct {
	ID         string    `gorm:"column:Id;primaryKey;comment:分数趋势 记录服务商评分增长记录" json:"Id"` // 分数趋势 记录服务商评分增长记录
	Code       string    `gorm:"column:Code;comment:受平方Code" json:"Code"`                 // 受平方Code
	CodeType   int32     `gorm:"column:CodeType;comment:受平方类型" json:"CodeType"`           // 受平方类型
	UpdateTime time.Time `gorm:"column:UpdateTime;comment:最后修改时间" json:"UpdateTime"`      // 最后修改时间
	Day        time.Time `gorm:"column:Day;comment:所属天" json:"Day"`                       // 所属天
	Score      float64   `gorm:"column:Score" json:"Score"`
}

// TableName ScoreTendency's table name
func (*ScoreTendency) TableName() string {
	return TableNameScoreTendency
}
