// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package models

import (
	"time"
)

const TableNameJobRecord = "job_record"

// JobRecord mapped from table <job_record>
type JobRecord struct {
	ID            string    `gorm:"column:Id;primaryKey" json:"Id"`
	LasteTime     time.Time `gorm:"column:LasteTime;comment:最后执行时间" json:"LasteTime"`                   // 最后执行时间
	BusinessTType int32     `gorm:"column:BusinessTType;comment:类型 1处理动态服务点赞修改时间" json:"BusinessTType"` // 类型 1处理动态服务点赞修改时间
}

// TableName JobRecord's table name
func (*JobRecord) TableName() string {
	return TableNameJobRecord
}
