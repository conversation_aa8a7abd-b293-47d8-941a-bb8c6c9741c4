// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package models

import (
	"time"
)

const TableNameServiceproviderservicetypelang = "serviceproviderservicetypelang"

// Serviceproviderservicetypelang mapped from table <serviceproviderservicetypelang>
type Serviceproviderservicetypelang struct {
	ServiceTypeCode string    `gorm:"column:ServiceTypeCode;primaryKey;comment:主键" json:"ServiceTypeCode"` // 主键
	LanguageCode    string    `gorm:"column:LanguageCode;primaryKey;comment:语言Code" json:"LanguageCode"`   // 语言Code
	Name            string    `gorm:"column:Name;comment:翻译内容" json:"Name"`                                // 翻译内容
	CreatorName     string    `gorm:"column:CreatorName;not null;comment:创建者" json:"CreatorName"`          // 创建者
	CreatedDate     time.Time `gorm:"column:CreatedDate;not null;comment:创建时间" json:"CreatedDate"`         // 创建时间
	UpdatorName     string    `gorm:"column:UpdatorName;not null;comment:修改者" json:"UpdatorName"`          // 修改者
	UpdatedDate     time.Time `gorm:"column:UpdatedDate;not null;comment:修改时间" json:"UpdatedDate"`         // 修改时间
}

// TableName Serviceproviderservicetypelang's table name
func (*Serviceproviderservicetypelang) TableName() string {
	return TableNameServiceproviderservicetypelang
}
