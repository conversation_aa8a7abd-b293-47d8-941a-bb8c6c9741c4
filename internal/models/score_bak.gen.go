// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package models

import (
	"time"
)

const TableNameScoreBak = "score_bak"

// ScoreBak mapped from table <score_bak>
type ScoreBak struct {

	/*
		通证 项目交易所Code

	*/
	Code       string    `gorm:"column:Code;primaryKey;comment:通证 项目交易所Code\n" json:"Code"`
	Score      float64   `gorm:"column:Score;comment:分数" json:"Score"`                                // 分数
	ScoreInfo  string    `gorm:"column:ScoreInfo;comment:小分" json:"ScoreInfo"`                        // 小分
	CodeType   int32     `gorm:"column:CodeType;comment:1交易商 2通证 3项目 4交易所 5钱包 6 服务商" json:"CodeType"` // 1交易商 2通证 3项目 4交易所 5钱包 6 服务商
	CreateTime time.Time `gorm:"column:CreateTime" json:"CreateTime"`
	UpdateTime time.Time `gorm:"column:UpdateTime" json:"UpdateTime"`
}

// TableName ScoreBak's table name
func (*ScoreBak) TableName() string {
	return TableNameScoreBak
}
