// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package models

import (
	"time"
)

const TableNamePostsAiaudit = "posts_aiaudit"

// PostsAiaudit mapped from table <posts_aiaudit>
type PostsAiaudit struct {
	PostsID      string    `gorm:"column:PostsId;primaryKey;comment:ai审核表 帖子id" json:"PostsId"`                          // ai审核表 帖子id
	PostsType    int32     `gorm:"column:PostsType;comment:类型 1商业 2评论" json:"PostsType"`                                 // 类型 1商业 2评论
	Status       int32     `gorm:"column:Status;comment:审核状态100待审核 200审核推荐 201审核不推荐 401拒绝" json:"Status"`                // 审核状态100待审核 200审核推荐 201审核不推荐 401拒绝
	FeedBack     string    `gorm:"column:FeedBack;comment:拒绝原因" json:"FeedBack"`                                         // 拒绝原因
	CreateTime   time.Time `gorm:"column:CreateTime;comment:创建时间" json:"CreateTime"`                                     // 创建时间
	PersonStatus int32     `gorm:"column:PersonStatus;comment:人工审核状态 100待审核 200审核推荐 201审核不推荐 401拒绝" json:"PersonStatus"` // 人工审核状态 100待审核 200审核推荐 201审核不推荐 401拒绝
	UpdateTime   time.Time `gorm:"column:UpdateTime;comment:修改时间" json:"UpdateTime"`                                     // 修改时间
}

// TableName PostsAiaudit's table name
func (*PostsAiaudit) TableName() string {
	return TableNamePostsAiaudit
}
