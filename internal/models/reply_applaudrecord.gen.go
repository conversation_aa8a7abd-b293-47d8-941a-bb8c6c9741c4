// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package models

import (
	"time"
)

const TableNameReplyApplaudrecord = "reply_applaudrecord"

// ReplyApplaudrecord mapped from table <reply_applaudrecord>
type ReplyApplaudrecord struct {
	RecordID   string    `gorm:"column:RecordId;primaryKey;comment:回复点赞记录" json:"RecordId"` // 回复点赞记录
	UserID     string    `gorm:"column:UserId" json:"UserId"`
	CreateTime time.Time `gorm:"column:CreateTime" json:"CreateTime"`
	ReplyID    string    `gorm:"column:ReplyId;comment:回复Id" json:"ReplyId"` // 回复Id
}

// TableName ReplyApplaudrecord's table name
func (*ReplyApplaudrecord) TableName() string {
	return TableNameReplyApplaudrecord
}
