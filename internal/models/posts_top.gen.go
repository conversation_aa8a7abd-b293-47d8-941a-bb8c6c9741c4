// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package models

import (
	"time"
)

const TableNamePostsTop = "posts_top"

// PostsTop mapped from table <posts_top>
type PostsTop struct {
	ID          string    `gorm:"column:Id;primaryKey" json:"Id"`
	PostsID     string    `gorm:"column:PostsId;comment:帖子Id" json:"PostsId"` // 帖子Id
	UserID      string    `gorm:"column:UserId" json:"UserId"`
	CreateTime  time.Time `gorm:"column:CreateTime" json:"CreateTime"`
	ReleaseType int32     `gorm:"column:ReleaseType;comment:置顶类型 1商业 2动态" json:"ReleaseType"` // 置顶类型 1商业 2动态
	Status      int32     `gorm:"column:Status;comment:置顶状态 0取消 1 置顶" json:"Status"`          // 置顶状态 0取消 1 置顶
	IP          string    `gorm:"column:Ip;comment:ip" json:"Ip"`                             // ip
	CountryCode string    `gorm:"column:CountryCode;comment:国家" json:"CountryCode"`           // 国家
}

// TableName PostsTop's table name
func (*PostsTop) TableName() string {
	return TableNamePostsTop
}
