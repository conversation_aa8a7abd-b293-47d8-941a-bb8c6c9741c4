// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package models

import (
	"time"
)

const TableNamePostsTopicRecommend = "posts_topic_recommend"

// PostsTopicRecommend mapped from table <posts_topic_recommend>
type PostsTopicRecommend struct {
	ID             string    `gorm:"column:Id;primaryKey;comment:话题推荐Id" json:"Id"`    // 话题推荐Id
	TopicID        string    `gorm:"column:TopicId;comment:话题Id" json:"TopicId"`       // 话题Id
	AreaCode       string    `gorm:"column:AreaCode;comment:推荐区域Code" json:"AreaCode"` // 推荐区域Code
	LastUpdateTime time.Time `gorm:"column:LastUpdateTime" json:"LastUpdateTime"`
	Creator        string    `gorm:"column:Creator;not null;comment:创建人" json:"Creator"` // 创建人
	TopicName      string    `gorm:"-" json:"TopicName"` // 创建人
}

// TableName PostsTopicRecommend's table name
func (*PostsTopicRecommend) TableName() string {
	return TableNamePostsTopicRecommend
}
