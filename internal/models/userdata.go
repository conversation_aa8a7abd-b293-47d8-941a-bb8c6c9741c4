package models

import (
	"time"
)

func (*UserData) TableName() string {
	return "user_data"
}
func (*UserDaren) TableName() string {
	return "user_daren"
}
func (*UserEnterpriseData) TableName() string {
	return "user_enterprise_data"
}

type UserData struct {
	UserId           string    `json:"userId" gorm:"column:UserId"` // id
	NickName         string    `json:"NickName" gorm:"column:NickName"`
	PhoneNumber      string    `json:"PhoneNumber" gorm:"column:PhoneNumber"`
	Email            string    `json:"Email" gorm:"column:Email"`
	AvatarAddress    string    `json:"AvatarAddress" gorm:"column:AvatarAddress"`
	CountryCode      string    `json:"CountryCode" gorm:"column:CountryCode"`
	LastUseDate      time.Time `json:"LastUseDate" gorm:"column:LastUseDate"`
	EnterpriseCode   string    `json:"EnterpriseCode" gorm:"column:EnterpriseCode"`
	PositionCode     string    `json:"PositionCode" gorm:"column:PositionCode"`
	WikiFXNumber     string    `json:"WikiFXNumber" gorm:"column:WikiFXNumber"`
	RegistrationTime time.Time `json:"RegistrationTime" gorm:"column:RegistrationTime"`
	WikiFx           int32     `json:"Wikifx" gorm:"column:Wikifx"` //是否是kol
	AreaCode         string    `json:"AreaCode" gorm:"column:AreaCode"`
	EnterpriseType   int32     `json:"EnterpriseType" gorm:"column:EnterpriseType"`
	Level            int32     `json:"Level" gorm:"column:Level"` //员工等级（1-超级管理员,2-管理员,3-高级员工,4-普通员工）
}

type UserDaren struct {
	UserId            string `json:"userId" gorm:"column:UserId"`                       // id
	WikiFx            int32  `json:"Wikifx" gorm:"column:Wikifx"`                       // 是否是fx达人
	IsOfficial        string `json:"IsOfficial" gorm:"column:IsOfficial"`               // 1官方0非官方
	DarenNickname     string `json:"DarenNickname" gorm:"column:DarenNickname"`         // id
	DarenAvatar       string `json:"DarenAvatar" gorm:"column:DarenAvatar"`             // id
	DarenDesc         string `json:"DarenDesc" gorm:"column:DarenDesc"`                 // id
	DarenEnabled      int32  `json:"DarenEnabled" gorm:"column:DarenEnabled"`           // id
	IsOfficialEnabled int32  `json:"IsOfficialEnabled" gorm:"column:IsOfficialEnabled"` // id
}

type UserEnterpriseData struct {
	EnterpriseCode      string `json:"EnterpriseCode" gorm:"column:EnterpriseCode"`
	EnterpriseType      int32  `json:"EnterpriseType" gorm:"column:EnterpriseType"`
	ProviderBasicColumn string `json:"ProviderBasicColumn" gorm:"column:ProviderBasicColumn"`
	TraderLogoColumn    string `json:"TraderLogoColumn" gorm:"column:TraderLogoColumn"`
	TraderNameColumn    string `json:"TraderNameColumn" gorm:"column:TraderNameColumn"`
	IsExistManager      int32  `json:"IsExistManager" gorm:"column:IsExistManager"`
	IsAuthenticated     int32  `json:"IsAuthenticated" gorm:"column:IsAuthenticated"`
}
type UserWikBitAttention struct {
	UserId            string
	AttentionedUserId string
	IsBothAtten       int32
}
