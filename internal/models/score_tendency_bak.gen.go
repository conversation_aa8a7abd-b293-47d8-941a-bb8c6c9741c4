// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package models

import (
	"time"
)

const TableNameScoreTendencyBak = "score_tendency_bak"

// ScoreTendencyBak mapped from table <score_tendency_bak>
type ScoreTendencyBak struct {
	ID         string    `gorm:"column:Id;primaryKey;comment:分数趋势 记录服务商评分增长记录" json:"Id"` // 分数趋势 记录服务商评分增长记录
	Code       string    `gorm:"column:Code;comment:受平方Code" json:"Code"`                 // 受平方Code
	CodeType   int32     `gorm:"column:CodeType;comment:受平方类型" json:"CodeType"`           // 受平方类型
	UpdateTime time.Time `gorm:"column:UpdateTime;comment:最后修改时间" json:"UpdateTime"`      // 最后修改时间
	Day        time.Time `gorm:"column:Day;comment:所属天数" json:"Day"`                      // 所属天数
	Score      float64   `gorm:"column:Score" json:"Score"`
}

// TableName ScoreTendencyBak's table name
func (*ScoreTendencyBak) TableName() string {
	return TableNameScoreTendencyBak
}
