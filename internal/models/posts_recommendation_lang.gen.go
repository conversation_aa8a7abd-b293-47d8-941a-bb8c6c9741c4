// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package models

const TableNamePostsRecommendationLang = "posts_recommendation_lang"

// PostsRecommendationLang mapped from table <posts_recommendation_lang>
type PostsRecommendationLang struct {
	ID               string `gorm:"column:Id;primaryKey;comment:话题推荐" json:"Id"` // 话题推荐
	Content          string `gorm:"column:Content" json:"Content"`
	LanguageCode     string `gorm:"column:LanguageCode" json:"LanguageCode"`
	RecommendationID string `gorm:"column:RecommendationId" json:"RecommendationId"`
}

// TableName PostsRecommendationLang's table name
func (*PostsRecommendationLang) TableName() string {
	return TableNamePostsRecommendationLang
}
