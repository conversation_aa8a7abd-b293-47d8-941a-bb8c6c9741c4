// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package models

import (
	"time"
)

const TableNameReport = "report"

// Report mapped from table <report>
type Report struct {
	ReportID    string    `gorm:"column:ReportId;primaryKey" json:"ReportId"`
	ConentID    string    `gorm:"column:ConentId;not null" json:"ConentId"`
	UserID      string    `gorm:"column:UserId;not null" json:"UserId"`
	Reason      string    `gorm:"column:Reason;not null" json:"Reason"`
	ReportTime  time.Time `gorm:"column:ReportTime;not null" json:"ReportTime"`
	Auditor     string    `gorm:"column:Auditor" json:"Auditor"`
	AuditTime   time.Time `gorm:"column:AuditTime" json:"AuditTime"`
	CountryCode string    `gorm:"column:CountryCode;not null" json:"CountryCode"`
	Status      int32     `gorm:"column:Status;not null;comment:状态（100：审核中 200：审核通过  401：审核驳回）" json:"Status"`                                                                                                                                           // 状态（100：审核中 200：审核通过  401：审核驳回）
	Origin      int32     `gorm:"column:Origin;not null;default:1;comment:app类型（1：fxeye  2：wikifx 3：wikifx2.0  4：wikibit 5：fxeye_testflight版 6：fxeye_极速版  7：fxeye_维权版  8：wikibit_testflight版   9：wikibit_lite版  10：wikibit_合规版  999：web）" json:"Origin"` // app类型（1：fxeye  2：wikifx 3：wikifx2.0  4：wikibit 5：fxeye_testflight版 6：fxeye_极速版  7：fxeye_维权版  8：wikibit_testflight版   9：wikibit_lite版  10：wikibit_合规版  999：web）
}

// TableName Report's table name
func (*Report) TableName() string {
	return TableNameReport
}
