package i18n

import (
	"archive/zip"
	_ "embed"
	"encoding/json"
	"fmt"
	"os"
	"strings"
	"sync"
)

const FILEPATH = "./data/languagepackage"

type Language map[string]string

var (
	mux                  = sync.Mutex{}
	languages            = map[string]Language{}
	languageNameReplacer = strings.NewReplacer("TXT_", "", ".json", "")
)

func SetLanguage(in map[string]Language) {
	mux.Lock()
	defer mux.Unlock()

	languages = make(map[string]Language, len(in))
	for k, v := range in {
		languages[strings.ToLower(k)] = v
	}
}

func init() {
	err := ReadFromDir(FILEPATH)
	if err != nil {
		panic(err)
	}

}

type Entry struct {
	Language string
	Values   map[string]string
}

func ReadFromZip(zipFilePath string) ([]*Entry, error) {
	r, err := zip.OpenReader(zipFilePath)
	if err != nil {
		return nil, err
	}
	defer r.Close()

	out := make([]*Entry, 0, len(r.File))
	for _, f := range r.File {
		if !strings.HasSuffix(f.Name, ".json") {
			continue
		}

		var values map[string]string
		values, err = readFile(f)
		if err != nil {
			return nil, err
		}

		languageName := languageNameReplacer.Replace(f.Name)
		mergeToLanguage(languageName, values)

		out = append(out, &Entry{
			Language: languageName,
			Values:   values,
		})
	}
	return out, nil
}

func ReadFromDir(FilePath string) error {
	fmt.Print(FilePath)
	r, err := os.ReadDir(FilePath)
	if err != nil {
		return err
	}
	//var out []*Entry
	for _, value := range r {
		filename := value.Name()
		var values map[string]string
		values, err = readJson(FILEPATH + "/" + filename)
		if err != nil {
			return err
		}
		languageName := languageNameReplacer.Replace(filename)
		mergeToLanguage(languageName, values)

		//out = append(out, &Entry{
		//	Language: languageName,
		//	Values:   values,
		//})
	}
	return nil
}

func mergeToLanguage(languageCode string, values map[string]string) {
	languageCode = strings.ToLower(languageCode)
	langValues, ok := languages[languageCode]
	if !ok {
		languages[languageCode] = values
		return
	}

	for name, value := range langValues {
		values[name] = value
	}

	languages[languageCode] = values
}

func readFile(f *zip.File) (map[string]string, error) {
	rc, err := f.Open()
	if err != nil {
		return nil, err
	}
	defer rc.Close()

	var data map[string]string
	err = json.NewDecoder(rc).Decode(&data)
	if err != nil {
		return nil, err
	}

	return data, nil
}
func readJson(filename string) (map[string]string, error) {
	var result = make(map[string]string)
	file, err := os.Open(filename)
	if err != nil {
		fmt.Println("Error opening JSON file:", err)
		return result, err
	}
	defer file.Close()

	// 读取文件内容到字节数组
	byteValue, _ := os.ReadFile(filename)

	// 解析JSON到map
	if err := json.Unmarshal(byteValue, &result); err != nil {
		fmt.Println("Error decoding JSON:", err)
	}
	return result, nil

}

func GetWithDefaultLanguage(key, languageCode, defaultLanguageCode string) string {
	out := GetLanguage(key, languageCode)
	if out != "" {
		return out
	}

	return GetLanguage(key, defaultLanguageCode)
}

func GetWithDefaultEnglish(key, languageCode string) string {
	return GetWithDefaultLanguage(key, languageCode, "en")
}

func GetLanguage(key, languageCode string) string {
	languageCode = strings.ToLower(languageCode)
	lan, ok := languages[languageCode]
	if !ok {
		return ""
	}

	return lan[key]
}

func GetWithTemplateDataDefault(key, languageCode, defaultValue string, data []string) string {
	lang := GetLanguage(key, languageCode)
	if lang == "" {
		lang = defaultValue
	}
	return genWithTemplate(lang, data)
}

func GetWithTemplateDataDefaultEnglish(key, languageCode string, data []string) string {
	lang := GetLanguage(key, languageCode)
	if lang == "" {
		lang = GetLanguage(key, "en")
	}
	return genWithTemplate(lang, data)
}

func GetWithChineseValueDefaultEnglish(languageCode, value string) string {
	values, ok := languages["zh-cn"]
	if !ok {
		return ""
	}

	var key string
	for k, v := range values {
		if strings.ToLower(v) == strings.ToLower(value) {
			key = k
			break
		}
	}

	return GetWithDefaultEnglish(key, languageCode)
}

func GetWithTemplateData(key, languageCode string, data []string) string {
	lang := GetLanguage(key, languageCode)
	return genWithTemplate(lang, data)
}

func genWithTemplate(content string, data []string) string {
	oldNews := make([]string, 0, len(data))
	for i, v := range data {
		oldNews = append(oldNews, fmt.Sprintf("{%v}", i), v)
	}

	return strings.NewReplacer(oldNews...).Replace(content)
}
