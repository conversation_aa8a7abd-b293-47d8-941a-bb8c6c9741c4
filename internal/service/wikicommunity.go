package service

import (
	pb "api-community/api/community/v1"
	"api-community/internal/conf"
	"api-community/internal/dao"
	"api-community/internal/models"
	"api-community/internal/remote"
	"api-community/internal/tool/country"
	"api-community/internal/tool/i18n"
	"api-community/internal/tool/icontext"
	utilstool "api-community/internal/utils"
	"api-community/pkg/upstream"
	"context"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/PuerkitoBio/goquery"
	innErr "github.com/airunny/wiki-go-tools/errors"

	"github.com/docker/go/canonical/json"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/metadata"
)

type WikiCommunityService struct {
	pb.UnimplementedWikiCommunityServer
	posts          *dao.Posts
	BusinessConfig *conf.Business
	UserClient     pb.UserCenterClient
	upstreamClient *upstream.Client // 新增：upstream客户端
}

func NewWikiCommunityServiceBak(posts *dao.Posts, businessConfig *conf.Business, userClient pb.UserCenterClient) *WikiCommunityService {
	return &WikiCommunityService{
		posts:          posts,
		BusinessConfig: businessConfig,
		UserClient:     userClient,
	}
}
func NewWikiCommunityService(posts *dao.Posts, businessConfig *conf.Business) *WikiCommunityService {
	// 创建upstream客户端配置
	upstreamConfig := &upstream.Config{
		Address2GeoDomain: businessConfig.RemoteUrl.Address2GeoApi,
	}

	// 创建upstream客户端
	upstreamClient, err := upstream.NewClient(upstreamConfig)
	if err != nil {
		// 如果创建失败，记录错误但不影响服务启动
		// 在实际调用时会有相应的错误处理
		upstreamClient = nil
	}

	return &WikiCommunityService{
		posts:          posts,
		BusinessConfig: businessConfig,
		upstreamClient: upstreamClient,
	}
}

type NewJsonContentModel struct {
	DynamicId    string
	DynamicValue string
}
type PostsSignSetting struct {
	BgColor string
	Color   string
	Word    string
}
type ImageTrans struct {
	Lang string
	Url  string
}

const (
	WikiFXActivityCode  = "582310937"
	LemonXCode          = "218302325"
	WikiFxCode          = "615765811"
	WikiFXEducationCode = "127834733"
	TopColor            = "#FFFFFF"
	TopBgColr           = "#4E5969"
	WikiFXElitesClub    = "933042519"
	SkylineGuide        = "243453620"
)

var OfficalCodes = []string{WikiFXActivityCode, LemonXCode, WikiFxCode, WikiFXEducationCode, WikiFXElitesClub, SkylineGuide}

var PostsSigns = []PostsSignSetting{
	{BgColor: "#4169E1", Color: "#FFFFFF", Word: "曝光"},
	{BgColor: "#FF5500", Color: "#FFFFFF", Word: "好评"},
	{BgColor: "#FF5500", Color: "#FFFFFF", Word: "中评"},
}

type PostsKeyValue struct {
	Key   string
	Value string
}

func GetmateData(ctx context.Context) map[string]string {
	var result = make(map[string]string)
	if md, ok := metadata.FromServerContext(ctx); ok {

		var userid = md.Get("X-User-Id")
		var languageCode = md.Get("languagecode")
		var countryCode = md.Get("countrycode")
		var preLanguageCode = md.Get("preferredlanguagecode")
		var basicData = md.Get("BasicData")
		if len(countryCode) == 0 {
			countryCode = "156"
		}
		if len(languageCode) == 0 {
			languageCode = "zh-cn"
		}
		if len(preLanguageCode) == 0 {
			preLanguageCode = languageCode
		}

		result["userid"] = userid
		result["languageCode"] = strings.ToLower(languageCode)
		result["countryCode"] = countryCode
		result["preLanguageCode"] = strings.ToLower(preLanguageCode)
		result["basicData"] = basicData
		result["areaCode"] = country.GetAreaCodeByCode(countryCode)
		if result["languageCode"] == "zh" {
			result["languageCode"] = "zh-hk"
		}

		result["AppType"] = "8"
		if len(result["basicData"]) > 0 {
			basicDataArr := strings.Split(basicData, ",")
			if len(basicDataArr) > 3 {
				result["AppType"] = basicDataArr[2]
			}
		}

	}
	return result

}

func (s *WikiCommunityService) GetPostsInfo(ctx context.Context, req *pb.GetPostsRequest) (*pb.GetPostsReply, error) {
	datamap := GetmateData(ctx)
	languageCode := strings.ToLower(datamap["languageCode"])
	if len(req.UserLoginId) != 0 {
	}
	var result []*pb.GetPostsReplyItem
	postsList, err := s.posts.GetPostsByIds(ctx, req.PostsIds)
	if err != nil {
		return nil, err
	}
	var postscodes []string
	if len(postsList) > 0 {
		postscodes = SelectTNoWhere(postsList, func(m *models.Post) string { return m.PostsCode })
		var repetions []remote.GetRepetitionRequestItem
		for _, value := range postsList {
			repetions = append(repetions, remote.GetRepetitionRequestItem{
				Code:    value.PostsCode,
				Title:   value.Title,
				Content: value.Content,
			})
		}
		//查询标题和内容是否一致或者重复率 重复率大于90%不显示标题
		resRepet, repeatErr := remote.GetRepetition(remote.GetRepetitionRequest{
			Mode:    2,
			PostCol: repetions,
		})
		attachs, err := s.posts.GetAttachments(ctx, postscodes)
		if err != nil {
			return nil, err
		}
		for _, value := range postsList {
			model := pb.GetPostsReplyItem{}
			attach := Where(attachs, func(attachment *models.Attachment) bool { return attachment.RelatedID == value.PostsCode })
			for _, img := range attach {
				list, detail := utilstool.GetPath(img.Content, "8")
				if img.Width <= 0 {
					img.Width = utilstool.IMGWIDTH
				}
				if img.Height <= 0 {
					img.Height = utilstool.IMGHEIGHT
				}
				model.Images = append(model.Images, &pb.Image{
					Width:  img.Width,
					Height: img.Height,
					List:   list,
					Detail: detail,
				})
			}
			if len(value.JSONContentExcept) > 0 {
				value.Content = JsonContentConvertLan(value.JSONContentExcept, value.ObjectID, languageCode) + value.Content
			}
			model.PostsId = value.PostsID
			model.Title = utilstool.MyHtmlDecode(value.Title, true)
			model.TitleNew = utilstool.MyHtmlDecode(value.Title, true)
			model.ContentNew = utilstool.MyHtmlDecode(value.Content, false)
			if repeatErr == nil {
				if len(resRepet.RepetitionCol) > 0 {
					singleRepeat := FirstT(resRepet.RepetitionCol, func(item *remote.GetRepetitionResponseItem) bool { return value.PostsCode == item.Code })
					if singleRepeat != nil {
						if singleRepeat.IncludeFlag || singleRepeat.Rate > 0.9 {
							model.Title = ""
						}
					}
				}
			}
			model.Content = value.Content
			model.ThemeCode = ""
			model.ThemeColor = ""
			model.Theme = ""
			model.ShareUrl = ""
			model.PublicTime = utilstool.ConvertToUnixMilli(value.PublicTime)
			result = append(result, &model)
		}
	}

	return &pb.GetPostsReply{
		Items: result,
	}, nil

}

// GetPostsApplaud 获取帖子点赞
func (s *WikiCommunityService) GetPostsApplaud(ctx context.Context, req *pb.GetPostsApplaudRequest) (*pb.GetPostsApplaudReply, error) {
	var result []string

	if len(req.UserLoginId) == 0 {

	} else {
		applauds, err := s.posts.GetUserApplaudPosts(ctx, req.PostsIds, req.UserLoginId)
		if err == nil {
			result = SelectTNoWhere(applauds, func(m *models.ApplaudRecordModel) string { return m.PostsId })
		}
	}
	return &pb.GetPostsApplaudReply{
		PostsId: result,
	}, nil
}
func (s *WikiCommunityService) GetSinglePosts(ctx context.Context, req *pb.GetSinglePostsRequest) (*pb.GetPostsReplyItem, error) {
	var result pb.GetPostsReplyItem
	postsinfo, err := s.posts.GetSinglePostsById(ctx, req.PostsId)
	if err != nil {
		return nil, err
	}
	attachs, err := s.posts.GetAttachments(ctx, []string{postsinfo.PostsCode})
	if err != nil {
		return nil, err
	}
	model := pb.GetPostsReplyItem{}
	for _, img := range attachs {
		list, detail := utilstool.GetPath(img.Content, "8")
		if img.Width <= 0 {
			img.Width = utilstool.IMGWIDTH
		}
		if img.Height <= 0 {
			img.Height = utilstool.IMGHEIGHT
		}
		model.Images = append(model.Images, &pb.Image{
			Width:  img.Width,
			Height: img.Height,
			List:   list,
			Detail: detail,
		})
	}
	model.PostsId = postsinfo.PostsID
	model.Title = utilstool.MyHtmlDecode(postsinfo.Title, true)
	model.TitleNew = utilstool.MyHtmlDecode(postsinfo.Title, true)
	model.ContentNew = utilstool.MyHtmlDecode(postsinfo.Content, false)
	model.Content = postsinfo.Content
	model.ThemeCode = ""
	model.ThemeColor = ""
	model.Theme = ""
	model.ShareUrl = ""
	model.PublicTime = utilstool.ConvertToUnixMilli(postsinfo.PublicTime)
	model.VodFileId = postsinfo.VodFileId
	if postsinfo.VodFileId != "" {
		model.HasVideo = true
	}
	return &result, nil
}

func JsonContentConvertLan(jsonContent string, categoryid string, languageCode string) string {
	var result = ""
	if len(jsonContent) > 0 && jsonContent != "null" {
		switch categoryid {
		case "40124359": //系统搭建
			jsonContent = strings.ReplaceAll(jsonContent, "PlatformProduct", i18n.GetWithChineseValueDefaultEnglish(languageCode, "商品说明"))
			jsonContent = strings.ReplaceAll(jsonContent, "PlatformUrl", i18n.GetWithChineseValueDefaultEnglish(languageCode, "官网地址"))
			jsonContent = strings.ReplaceAll(jsonContent, "PlatformIntroduction", i18n.GetWithChineseValueDefaultEnglish(languageCode, "公司简介"))
			break
		case "45474348": //支付
			jsonContent = strings.ReplaceAll(jsonContent, "PlatformProduct", i18n.GetWithChineseValueDefaultEnglish(languageCode, "商品说明"))
			jsonContent = strings.ReplaceAll(jsonContent, "PlatformUrl", i18n.GetWithChineseValueDefaultEnglish(languageCode, "官网地址"))
			jsonContent = strings.ReplaceAll(jsonContent, "PlatformIntroduction", i18n.GetWithChineseValueDefaultEnglish(languageCode, "公司简介"))
			jsonContent = strings.ReplaceAll(jsonContent, "PlatformProduct", i18n.GetWithChineseValueDefaultEnglish(languageCode, "商品说明"))
			jsonContent = strings.ReplaceAll(jsonContent, "PlatformUrl", i18n.GetWithChineseValueDefaultEnglish(languageCode, "官网地址"))
			jsonContent = strings.ReplaceAll(jsonContent, "PlatformIntroduction", i18n.GetWithChineseValueDefaultEnglish(languageCode, "公司简介"))
			break
		case "94201212": //牌照
			jsonContent = strings.ReplaceAll(jsonContent, "TransferProduct", i18n.GetWithChineseValueDefaultEnglish(languageCode, "商品说明"))
			jsonContent = strings.ReplaceAll(jsonContent, "TransferPrice", i18n.GetWithChineseValueDefaultEnglish(languageCode, "出售价格"))
			jsonContent = strings.ReplaceAll(jsonContent, "TransferDate", i18n.GetWithChineseValueDefaultEnglish(languageCode, "注册时间"))
			jsonContent = strings.ReplaceAll(jsonContent, "CommissionPrice", i18n.GetWithChineseValueDefaultEnglish(languageCode, "申请价格"))
			jsonContent = strings.ReplaceAll(jsonContent, "CommissionArea", i18n.GetWithChineseValueDefaultEnglish(languageCode, "监管地区"))
			jsonContent = strings.ReplaceAll(jsonContent, "CommissionLicense", i18n.GetWithChineseValueDefaultEnglish(languageCode, "牌照类型"))
			jsonContent = strings.ReplaceAll(jsonContent, "CommissionPeriod", i18n.GetWithChineseValueDefaultEnglish(languageCode, "申请周期"))
			jsonContent = strings.ReplaceAll(jsonContent, "CommissionMaintenance", i18n.GetWithChineseValueDefaultEnglish(languageCode, "维护费用"))
			jsonContent = strings.ReplaceAll(jsonContent, "CommissionIntroduction", i18n.GetWithChineseValueDefaultEnglish(languageCode, "公司简介"))
			jsonContent = strings.ReplaceAll(jsonContent, "CommissionProduct", i18n.GetWithChineseValueDefaultEnglish(languageCode, ""))
			break
		case "22747104": //ib
			jsonContent = strings.ReplaceAll(jsonContent, "IBProduct", i18n.GetWithChineseValueDefaultEnglish(languageCode, "商品说明"))
			break
		case "27116715": //招聘
			jsonContent = strings.ReplaceAll(jsonContent, "PersonalProduct", i18n.GetWithChineseValueDefaultEnglish(languageCode, "商品说明"))
			jsonContent = strings.ReplaceAll(jsonContent, "PersonalIntroduction", i18n.GetWithChineseValueDefaultEnglish(languageCode, "职位描述"))
			jsonContent = strings.ReplaceAll(jsonContent, "PersonalAddress", i18n.GetWithChineseValueDefaultEnglish(languageCode, "详细地址"))
			jsonContent = strings.ReplaceAll(jsonContent, "PersonalWelfare", i18n.GetWithChineseValueDefaultEnglish(languageCode, "公司福利"))
			jsonContent = strings.ReplaceAll(jsonContent, "PersonalJob", i18n.GetWithChineseValueDefaultEnglish(languageCode, "职位"))
			jsonContent = strings.ReplaceAll(jsonContent, "TeamAddress", i18n.GetWithChineseValueDefaultEnglish(languageCode, "详细地址"))
			jsonContent = strings.ReplaceAll(jsonContent, "TeamMember", i18n.GetWithChineseValueDefaultEnglish(languageCode, "团队人数"))
			jsonContent = strings.ReplaceAll(jsonContent, "PersonalSalary", i18n.GetWithChineseValueDefaultEnglish(languageCode, "薪酬水平"))
			jsonContent = strings.ReplaceAll(jsonContent, "PersonalRequirement", i18n.GetWithChineseValueDefaultEnglish(languageCode, "职位要求"))
			jsonContent = strings.ReplaceAll(jsonContent, "PersonalArea", i18n.GetWithChineseValueDefaultEnglish(languageCode, "工作国家或地区"))
			jsonContent = strings.ReplaceAll(jsonContent, "TeamArea", i18n.GetWithChineseValueDefaultEnglish(languageCode, "工作国家或地区"))
			jsonContent = strings.ReplaceAll(jsonContent, "TeamSalary", i18n.GetWithChineseValueDefaultEnglish(languageCode, "薪酬水平"))
			jsonContent = strings.ReplaceAll(jsonContent, "TeamProduct", i18n.GetWithChineseValueDefaultEnglish(languageCode, "商品说明"))
			jsonContent = strings.ReplaceAll(jsonContent, "TeamRequirement", i18n.GetWithChineseValueDefaultEnglish(languageCode, "职位要求"))
			jsonContent = strings.ReplaceAll(jsonContent, "PersonalProvince", i18n.GetWithChineseValueDefaultEnglish(languageCode, "工作国家或地区"))
			break
		case "91556172": //91556172
			jsonContent = strings.ReplaceAll(jsonContent, "ExhibitionMember", i18n.GetWithChineseValueDefaultEnglish(languageCode, "预计人数"))
			jsonContent = strings.ReplaceAll(jsonContent, "ExhibitionPrice", i18n.GetWithChineseValueDefaultEnglish(languageCode, "门票价格"))
			jsonContent = strings.ReplaceAll(jsonContent, "ExhibitionUrl", i18n.GetWithChineseValueDefaultEnglish(languageCode, "官网地址"))
			jsonContent = strings.ReplaceAll(jsonContent, "ExhibitionDetails", i18n.GetWithChineseValueDefaultEnglish(languageCode, "详细信息"))
			break
		case "39459757": //
			jsonContent = strings.ReplaceAll(jsonContent, "PersonalSalary", i18n.GetWithChineseValueDefaultEnglish(languageCode, "薪酬水平"))
			jsonContent = strings.ReplaceAll(jsonContent, "TeamSalary", i18n.GetWithChineseValueDefaultEnglish(languageCode, "薪酬水平"))
			jsonContent = strings.ReplaceAll(jsonContent, "TeamRequirement", i18n.GetWithChineseValueDefaultEnglish(languageCode, "职位要求"))
			jsonContent = strings.ReplaceAll(jsonContent, "PersonalProduct", i18n.GetWithChineseValueDefaultEnglish(languageCode, "商品说明"))
			jsonContent = strings.ReplaceAll(jsonContent, "PersonalIntroduction", i18n.GetWithChineseValueDefaultEnglish(languageCode, "职位描述"))
			jsonContent = strings.ReplaceAll(jsonContent, "PersonalAddress", i18n.GetWithChineseValueDefaultEnglish(languageCode, "详细地址"))
			jsonContent = strings.ReplaceAll(jsonContent, "PersonalWelfare", i18n.GetWithChineseValueDefaultEnglish(languageCode, "公司福利"))
			jsonContent = strings.ReplaceAll(jsonContent, "PersonalJob", i18n.GetWithChineseValueDefaultEnglish(languageCode, "职位"))
			jsonContent = strings.ReplaceAll(jsonContent, "TeamAddress", i18n.GetWithChineseValueDefaultEnglish(languageCode, "详细地址"))
			jsonContent = strings.ReplaceAll(jsonContent, "TeamProduct", i18n.GetWithChineseValueDefaultEnglish(languageCode, ""))
			jsonContent = strings.ReplaceAll(jsonContent, "TeamMember", i18n.GetWithChineseValueDefaultEnglish(languageCode, "团队人数"))
			break
		}
		var jsonc = make(map[string]string)

		err := json.Unmarshal([]byte(jsonContent), &jsonc)
		if err == nil {
			var keys = []string{"MyBigdataRecruitType", "PersonalCityCode", "PersonalCountyCode", "PersonalId", "PersonalCity", "PersonalProvinceCode", "PersonalCounty", "TeamCityCode", "TeamCountyCode", "TeamId", "TeamCity", "TeamProvince", "TeamProvinceCode", "TeamCounty", "PersonalArea", "TeamProduct"}
			for key, value := range jsonc {
				if ExistString(keys, key) {
					delete(jsonc, key)
					continue
				}
				if len(key) > 0 {
					result = result + key + ":" + value + "\n"
				} else {
					result = result + key + value
				}
			}
		}
	}
	return result
}

// newsjon转
func NewJonsContentConvertLan(newJsonContent, categoryid, languageCode string, TemlateDynamicFields []*models.Templatedynamicfield, TemlateDynamicFieldsValue []*models.Templatedynamicvalue) string {
	var newJsonContentJson []*NewJsonContentModel
	languageCode = strings.ReplaceAll(languageCode, "cn", "CN")
	languageCode = strings.ReplaceAll(languageCode, "hk", "HK")
	err := json.Unmarshal([]byte(newJsonContent), &newJsonContentJson)
	if err != nil {
		return ""
	}
	newJsonContentJson = Where(newJsonContentJson, func(m *NewJsonContentModel) bool { return m.DynamicId != "10" && len(m.DynamicValue) > 0 })
	if len(newJsonContentJson) == 0 {
		return ""
	}

	//获取当前服务类型动态字段
	currentTypes := Where(TemlateDynamicFields, func(m *models.Templatedynamicfield) bool {
		return m.Barinfoid == categoryid
	})
	var result = ""
	for _, value := range currentTypes {
		if len(value.Languagelist) == 0 {
			continue
		}
		var mapLanguage map[string]string
		err := json.Unmarshal([]byte(value.Languagelist), &mapLanguage)
		if err != nil {
			return ""
		}
		if _, ok := mapLanguage[languageCode]; ok {
			value.Name = mapLanguage[languageCode]
		}
		s := FirstT(newJsonContentJson, func(n *NewJsonContentModel) bool {
			return n.DynamicId == value.ID
		})
		if s != nil {
			if len(s.DynamicValue) > 0 {
				if (value.Type == 2 || value.Type == 3) && len(value.Dynamicvalueids) > 0 {
					existIds := Intersection[string](strings.Split(s.DynamicValue, ","), strings.Split(value.Dynamicvalueids, ","))
					if len(existIds) > 0 {
						dyvalue := Where(TemlateDynamicFieldsValue, func(m *models.Templatedynamicvalue) bool {
							return ExistString(existIds, m.ID)
						})
						if dyvalue != nil {
							orderdyvalue := OrderByIntDesc(dyvalue, func(t *models.Templatedynamicvalue) int32 {
								return t.Ordernum
							})
							var values []string
							for _, value := range orderdyvalue {
								var mapLanguagevalue map[string]string
								err := json.Unmarshal([]byte(value.Languagelist), &mapLanguagevalue)
								if err == nil {
									if _, ok := mapLanguagevalue[languageCode]; ok {
										values = append(values, mapLanguagevalue[languageCode])
									} else {
										values = append(values, value.Fieldvalue)
									}

								}
							}
							result = result + value.Name + ":" + strings.Join(values, ",") + "\n"

						}
					}
				} else if value.Type == 4 { //日期格式
					t, err := time.Parse("2006-01-02", s.DynamicValue)
					if err == nil {
						result = result + value.Name + ":" + t.Format("2006-01-02") + "\n"
					}
				}
				//} else if value.Type == 5 {
				////} else {
				////   jsonMap[value.Name] = s.DynamicValue
				////}
			}
		}

	}
	return result
}

func CreateImageModel(attachments []*models.Attachment, languageCode string, isWaterMark bool, appType string) []*pb.Image {
	var result = make([]*pb.Image, 0)
	var langs []string
	if strings.ToLower(languageCode) == "zh-cn" {
		langs = append(langs, "zh-cn")
		langs = append(langs, "zh")
	} else if strings.ToLower(languageCode) == "zh-hk" {
		langs = append(langs, "zh-hk")
		langs = append(langs, "zh-tw")
	} else {
		langs = append(langs, languageCode)
	}
	iswordpic := len(Where(attachments, func(m *models.Attachment) bool {
		return len(m.ContentLang) > 0 && m.ContentLang != "{}"
	})) > 0
	var isbreak = false
	for _, img := range attachments {
		if len(img.ContentLang) > 0 && img.ContentLang != "{}" { //是否有文生图
			var wordImg = make(map[string]string)
			err := json.Unmarshal([]byte(img.ContentLang), &wordImg)
			currentLang := []string{languageCode, "en"}
			if err == nil {
				for _, v := range currentLang {
					if value, ok := wordImg[v]; ok {
						img.Content = value
						isbreak = true
						break
					}
				}
			}
		} else {
			if iswordpic == false {
				if len(img.Translation) > 0 && img.Translation != "[]" { //是否有图片翻译
					var imgtran = make([]*ImageTrans, 20)
					err := json.Unmarshal([]byte(img.Translation), &imgtran)
					if err == nil {
						transingSingle := FirstT(imgtran, func(i *ImageTrans) bool {
							return ExistString(langs, i.Lang)
						})
						if transingSingle != nil {
							if len(transingSingle.Url) > 0 {
								img.Content = transingSingle.Url
							}
						}
					}
				}
			} else {
				continue
			}
		}

		list, _ := utilstool.GetPath(img.Content, appType)
		if img.Width <= 0 {
			img.Width = utilstool.IMGWIDTH
		}
		if img.Height <= 0 {
			img.Height = utilstool.IMGHEIGHT
		}
		//if isWaterMark {
		//	if appType == "600" { //是否是Leomongx
		//		list = strings.ReplaceAll(list, "list600", "lemonxlist600")
		//	} else {
		//		list = strings.ReplaceAll(list, "list600", "wiki600")
		//	}
		//}
		result = append(result, &pb.Image{
			Width:  img.Width,
			Height: img.Height,
			List:   list,
			Detail: list,
		})
		if isbreak {
			return result
		}
	}
	return result

}

// GetPostsOrCommentGradesInfo 获取动态或者评价的数据
func (s *WikiCommunityService) GetPostsOrCommentGradesInfo(ctx context.Context, req *pb.GetPostsOrCommentGradesInfoRequest) (*pb.GetPostsReply, error) {
	l := log.Context(ctx)
	defer func(start time.Time) {
		l.Infof("接口耗时[%v] ", time.Since(start))
	}(time.Now())
	files, _ := s.posts.GetTemplatedynamicfields(ctx)
	values, _ := s.posts.GetTemplatedynamicValues(ctx)
	var specialServiceTypes = []string{"96713708", "51692449", "73566309", "64104355"}
	var specialServiceType = "40124359"
	datamap := GetmateData(ctx)
	languageCode := strings.ToLower(datamap["languageCode"])
	fmt.Println(languageCode)
	fmt.Println("列表语言" + strings.Join(req.CommentGradeIds, ","))
	//appType := datamap["AppType"]
	countryNames, errcountry := remote.GetCountryCodeByLanguageCode(languageCode, "156")
	var result []*pb.GetPostsReplyItem
	postsList, _ := s.posts.GetPostsByIds(ctx, req.PostsIds)
	commentgradeList, _ := s.posts.GetCommentGradesByIds(ctx, req.CommentGradeIds)
	var postscodes []string
	var postsIds []string
	var repetionsposts []remote.GetRepetitionRequestItem
	if len(postsList) > 0 {

		serviceTypes, serviceErr := s.posts.GetServiceTypelist(ctx)
		postsIds = SelectTNoWhere(postsList, func(m *models.Post) string { return m.PostsID })
		postscodes = SelectTNoWhere(postsList, func(m *models.Post) string { return m.PostsCode })

		poststopics, _ := s.posts.GetPostsTopicByPostsIds(ctx, postsIds)
		postsRelationUsers, _ := s.posts.GetPostsUserRelationByPostsIds(ctx, postsIds)
		fmt.Println(len(poststopics))
		fmt.Println(len(postsRelationUsers))

		attachs, _ := s.posts.GetAttachments(ctx, postscodes)

		for _, value := range postsList {

			model := pb.GetPostsReplyItem{}
			model.ThemeCode = value.ObjectID
			if serviceErr == nil {
				if len(value.ObjectID) > 0 {
					if ExistString(specialServiceTypes, specialServiceType) {
						value.ObjectID = specialServiceType
					}

					serviceTypeInfo := FirstT(serviceTypes, func(m *models.ServiceProviderName) bool {
						return strings.ToLower(m.LanguageCode) == languageCode && strings.Trim(m.Code, " ") == strings.Trim(value.ObjectID, " ")
					})

					if serviceTypeInfo != nil {
						model.ThemeColor = ""
						model.Theme = serviceTypeInfo.Name
					}
				}
			}

			attach := Where(attachs, func(attachment *models.Attachment) bool { return attachment.RelatedID == value.PostsCode })
			attachorder := OrderByInt(attach, func(m *models.Attachment) int32 {
				return m.Sequence
			})
			for _, img := range attachorder {
				list, detail := utilstool.GetPath(img.Content, "8")
				if img.Width <= 0 {
					img.Width = utilstool.IMGWIDTH
				}
				if img.Height <= 0 {
					img.Height = utilstool.IMGHEIGHT
				}
				model.Images = append(model.Images, &pb.Image{
					Width:  img.Width,
					Height: img.Height,
					List:   list,
					Detail: detail,
				})
			} //1 2 3 4 5 6 7 8
			if errcountry == nil {
				countryName := FirstTNoPointer(countryNames, func(t remote.GetCountryCodeByLanguageCodeResponse) bool {
					return t.CountryCode == value.CountryCode || strings.ToLower(t.TwoCharCode) == value.CountryCode
				})
				if countryName != nil {
					model.CountryName = countryName.Name
				}
			}
			model.PostsCode = value.PostsCode
			model.ContentLanguage = value.LanguageCode
			model.Grade = value.CommentGrade
			model.PostsId = value.PostsID
			model.Title = utilstool.MyHtmlDecode(value.Title, true)
			model.TitleNew = utilstool.MyHtmlDecode(value.Title, false)
			model.ContentNew = utilstool.MyHtmlDecode(value.Content, false)
			model.EnterpriseCode = value.RelationEnterpriseCode
			model.ReleaseType = value.ReleaseType
			model.VodFileId = value.VodFileId
			if value.VodFileId != "" {
				model.HasVideo = true
			}
			if len(value.JSONContentExcept) > 0 {
				value.Content = JsonContentConvertLan(value.JSONContentExcept, value.ObjectID, languageCode) + value.Content
			}
			if len(value.NewJSONContent) > 0 && value.ReleaseType == 1 {

				value.Content = NewJonsContentConvertLan(value.NewJSONContent, value.ObjectID, languageCode, files, values) + value.Content
			}

			var sign PostsSignSetting
			if value.ObjectType > 0 {
				if value.CommentGrade == 3 {
					sign = PostsSigns[0]
				} else {
					sign = PostsSigns[1]
				}
				model.Sign = &pb.PostsSign{
					IsShow:  2,
					BgColor: sign.BgColor,
					Word:    sign.Word,
					Icon:    "",
					Color:   sign.Color,
				}
			}
			//0-列表、详情页都不显示，1-列表显示，2-详情页显示，3-列表、详情页都显示
			model.Content = utilstool.MyHtmlDecode(value.Content, false)
			repetionsposts = append(repetionsposts, remote.GetRepetitionRequestItem{
				Code:    value.PostsCode,
				Title:   model.Title,
				Content: model.Content,
			})

			model.ShareUrl = ""
			fmt.Println(value.PublicTime)
			model.PublicTime = utilstool.ConvertToUnixMilli(value.PublicTime)
			if len(value.AffEnterpriseCode) > 0 {
				model.UserId = value.AffEnterpriseCode
			} else {
				model.UserId = value.UserID
			}
			singlepostsTopics := Where(poststopics, func(m *models.PostsTopicRelation) bool {
				return m.PostsID == value.PostsID
			})
			singlepostsUserRelations := Where(postsRelationUsers, func(m *models.PostsUserRelation) bool {
				return m.PostsID == value.PostsID
			})

			var postsTopics []*models.PostsTopicView
			if singlepostsTopics != nil {
				for _, v := range singlepostsTopics {
					postsTopics = append(postsTopics, &models.PostsTopicView{
						Id:     v.TopicID,
						Name:   "#" + v.TopicName,
						Sort:   v.Sort,
						Type:   2,
						Enable: true,
					})
				}
			}
			if singlepostsUserRelations != nil {
				for _, v := range singlepostsUserRelations {
					postsTopics = append(postsTopics, &models.PostsTopicView{
						Id:     v.UserID,
						Name:   "@" + v.NickName,
						Sort:   v.Sort,
						Type:   1,
						Enable: true,
					})
				}
			}
			var ordertopicinfo = OrderByInt(postsTopics, func(p *models.PostsTopicView) int32 {
				return p.Sort
			})

			for _, v := range ordertopicinfo {
				model.PostsTopicItems = append(model.PostsTopicItems, &pb.PostsTopicItems{
					Id:     v.Id,
					Name:   v.Name,
					Enable: v.Enable,
					Type:   v.Type,
				})
			}
			result = append(result, &model)
		}
	}
	if len(repetionsposts) > 0 && req.IsDoTitle {
		resRepet, repeatErr := remote.GetRepetition(remote.GetRepetitionRequest{
			Mode:    2,
			PostCol: repetionsposts,
		})
		for _, z := range result {
			//查询标题和内容是否一致或者重复率 重复率大于90%不显示标题
			if repeatErr == nil {
				if len(resRepet.RepetitionCol) > 0 {
					singleRepeat := FirstT(resRepet.RepetitionCol, func(item *remote.GetRepetitionResponseItem) bool { return z.PostsCode == item.Code })
					if singleRepeat != nil {
						if singleRepeat.IncludeFlag || singleRepeat.Rate > 0.9 {
							z.Title = ""
						}
					}
				}
			}
		}
	}

	if len(commentgradeList) > 0 {
		commentgradeIds := SelectTNoWhere(commentgradeList, func(m *models.CommentGrade) string { return m.CommentGradeID })
		var repetions []remote.GetRepetitionRequestItem
		var transIds []string
		var languageCodes = []string{languageCode}
		for _, value := range commentgradeList {
			lange := strings.ToLower(value.LanguageCode)
			repetions = append(repetions, remote.GetRepetitionRequestItem{
				Code:    value.CommentGradeID,
				Title:   value.Title,
				Content: value.Content,
			})
			if lange != languageCode {
				transIds = append(transIds, value.CommentGradeID)
			}
		}
		languageCodes = append(languageCodes, languageCode)
		if languageCode != "en" {
			languageCodes = append(languageCodes, "en")
		}
		var trans []*models.CommentGradeTranslate
		if len(transIds) > 0 {
			trans, _ = s.posts.GetCommentGradesTrans(ctx, transIds, languageCodes)
		}

		//查询标题和内容是否一致或者重复率 重复率大于90%不显示标题
		resRepet, repeatErr := remote.GetRepetition(remote.GetRepetitionRequest{
			Mode:    2,
			PostCol: repetions,
		})
		attachs, _ := s.posts.GetAttachments(ctx, commentgradeIds)
		for _, value := range commentgradeList {
			model := pb.GetPostsReplyItem{}
			attach := Where(attachs, func(attachment *models.Attachment) bool { return attachment.RelatedID == value.CommentGradeID })
			attachorder := OrderByInt(attach, func(m *models.Attachment) int32 {
				return m.Sequence
			})
			model.Images = CreateImageModel(attachorder, languageCode, false, "8")
			model.Grade = value.Grade
			model.PostsCode = value.CommentGradeID
			model.PostsId = value.CommentGradeID
			model.Title = utilstool.MyHtmlDecode(value.Title, true)
			model.TitleNew = utilstool.MyHtmlDecode(value.Title, true)
			model.ContentNew = utilstool.MyHtmlDecode(value.Content, false)
			model.Content = utilstool.MyHtmlDecode(value.Content, false)
			model.EnterpriseCode = value.ObjectID

			if errcountry == nil {
				countryName := FirstTNoPointer(countryNames, func(t remote.GetCountryCodeByLanguageCodeResponse) bool {
					return t.CountryCode == value.CountryCode || strings.ToLower(t.TwoCharCode) == value.CountryCode
				})
				if countryName != nil {
					model.CountryName = countryName.Name
				}
			}

			if strings.ToLower(value.LanguageCode) != languageCode {
				if ExistString(transIds, value.CommentGradeID) {

					language := FirstT(trans, func(tran *models.CommentGradeTranslate) bool {
						return strings.ToLower(tran.LanguageCode) == languageCode && value.CommentGradeID == tran.CommentGradeID
					})
					if language != nil {
						model.Title = utilstool.MyHtmlDecode(language.Title, true)
						model.TitleNew = utilstool.MyHtmlDecode(language.Title, true)
						model.ContentNew = utilstool.MyHtmlDecode(language.Content, false)
						model.Content = utilstool.MyHtmlDecode(language.Content, false)
						model.ContentLanguage = languageCode
					} else {
						languageen := FirstT(trans, func(tran *models.CommentGradeTranslate) bool {
							return strings.ToLower(tran.LanguageCode) == "en" && value.CommentGradeID == tran.CommentGradeID
						})
						if languageen != nil {
							model.Title = utilstool.MyHtmlDecode(languageen.Title, true)
							model.TitleNew = utilstool.MyHtmlDecode(languageen.Title, true)
							model.ContentNew = utilstool.MyHtmlDecode(languageen.Content, false)
							model.Content = utilstool.MyHtmlDecode(languageen.Content, false)
							model.ContentLanguage = "en"
						}
					}
				}
			}
			if repeatErr == nil && req.IsDoTitle {
				if len(resRepet.RepetitionCol) > 0 {
					singleRepeat := FirstT(resRepet.RepetitionCol, func(item *remote.GetRepetitionResponseItem) bool { return value.CommentGradeID == item.Code })
					if singleRepeat != nil {
						if singleRepeat.IncludeFlag || singleRepeat.Rate > 0.9 {
							model.Title = ""
						}
					}
				}
			}
			var sign PostsSignSetting
			if value.Grade == 3 {
				sign = PostsSigns[0]
			} else if value.Grade == 1 {
				sign = PostsSigns[1]
			} else {
				sign = PostsSigns[2]
			}
			//0-列表、详情页都不显示，1-列表显示，2-详情页显示，3-列表、详情页都显示
			model.Sign = &pb.PostsSign{
				IsShow:  2,
				BgColor: sign.BgColor,
				Word:    i18n.GetWithChineseValueDefaultEnglish(languageCode, sign.Word),
				Icon:    "",
				Color:   sign.Color,
			}
			//model.Content = value.Content
			model.ThemeCode = value.ObjectID
			model.ThemeColor = ""
			model.Theme = ""
			model.ShareUrl = ""
			model.UserId = value.UserID
			model.PublicTime = utilstool.ConvertToUnixMilli(value.CreateTime)
			result = append(result, &model)
		}
	}
	return &pb.GetPostsReply{
		Items: result,
	}, nil

}

func (s *WikiCommunityService) GetPostsApplaudAndCollect(ctx context.Context, req *pb.GetPostsApplaudRequest) (*pb.GetPostsApplaudAndCollectReply, error) {
	var applaudIds []string
	var collectIds []string
	if len(req.UserLoginId) == 0 {

	} else {
		applauds, err := s.posts.GetUserApplaudPosts(ctx, req.PostsIds, req.UserLoginId)
		if err == nil {
			applaudIds = SelectTNoWhere(applauds, func(m *models.ApplaudRecordModel) string { return m.PostsId })
		}
		collects, err := s.posts.GetUserCollectPosts(ctx, req.PostsIds, req.UserLoginId)
		if err == nil {
			collectIds = SelectTNoWhere(collects, func(m *models.ApplaudRecordModel) string { return m.PostsId })
		}
	}
	var applaudsCounts []*pb.GetPostsApplaudAndCollectReplyCount
	applauds, err := s.posts.GetPostsUserApplaud(ctx, req.PostsIds)
	if err == nil {
		for _, v := range applauds {
			applaudsCounts = append(applaudsCounts, &pb.GetPostsApplaudAndCollectReplyCount{
				PostsId:             v.PostsID,
				ShowApplaudNumber:   utilstool.ShowNumber(v.ApplaudCount),
				ShowCommentNumber:   utilstool.ShowNumber(v.ReplyCount),
				ApplaudNumber:       v.ApplaudCount,
				CommentNumber:       v.ReplyCount,
				CollectNumber:       v.CollectCount,
				ShowCollectNumber:   utilstool.ShowNumber(v.CollectCount),
				IsShowApplaudNumber: true, //v.ApplaudCount >= 100,
				IsShowCommentNumber: true, //v.ReplyCount >= 100,
				IsShowCollectNumber: true, //v.CollectCount >= 100,
				IsShowPlayTimes:     true,
				PlayTimes:           v.ViewCount,
				ShowPlayTimes:       utilstool.ShowNumber(v.ViewCount),
			})
		}
	}
	return &pb.GetPostsApplaudAndCollectReply{
		Applauds:       applaudIds,
		Collects:       collectIds,
		ApplaudsCounts: applaudsCounts,
	}, nil
}

// GetUserCollectPosts 用户帖子收藏
func (s *WikiCommunityService) GetUserCollectPosts(ctx context.Context, req *pb.GetUserCollectPostsRequest) (*pb.GetUserCollectPostsReply, error) {
	var result []*pb.GetUserCollectPostsReplyItem
	collects, err := s.posts.GetSingleUserPostsCollect(ctx, req.UserLoginId, int(req.PageIndex), int(req.PageSize), req.ReleaseType)
	if err == nil {
		for _, value := range collects {
			result = append(result, &pb.GetUserCollectPostsReplyItem{
				PostsId:      value.PostsID,
				DataType:     value.ObjectType,
				CollectTime:  utilstool.ConvertToUnixMilli(value.CreateTime),
				PublicUserId: value.PostsUserId,
			})
		}
	}
	return &pb.GetUserCollectPostsReply{
		List: result,
	}, nil
}

//rpc GetPersonHomePostsPageList (GetPersonHomePostsPageListRequest) returns (GetPersonHomePostsPageListReply){

func (s *WikiCommunityService) GetPersonHomePostsPageList(ctx context.Context, req *pb.GetPersonHomePostsPageListRequest) (*pb.GetPersonHomePostsPageListReply, error) {
	var result []*pb.GetPostsReplyItem
	files, _ := s.posts.GetTemplatedynamicfields(ctx)
	values, _ := s.posts.GetTemplatedynamicValues(ctx)
	var specialServiceTypes = []string{"96713708", "51692449", "73566309", "64104355"}
	var specialServiceType = "40124359"
	datamap := GetmateData(ctx)
	preLanguageCode := datamap["preLanguageCode"]
	preLanguageCodes := strings.Split(preLanguageCode, ",")
	//appType := datamap["AppType"]
	languageCode := strings.ToLower(datamap["languageCode"])
	//var areacode = country.GetAreaCodeByCode(datamap["countryCode"])
	if len(req.UserId) == 0 {
		return &pb.GetPersonHomePostsPageListReply{
			List: result,
		}, nil
	}
	countryNames, errcountry := remote.GetCountryCodeByLanguageCode("zh-CN", "156")
	var isActivityCode = ExistString(OfficalCodes, req.UserId)
	isLemonx := false
	if isActivityCode {
		isLemonx = req.UserId == LemonXCode
		req.UserId = req.UserId + "2"
	}
	postsTops, _ := s.posts.GetPostsTop(ctx, req.UserId, req.UserLoginId, req.ReleaseType)
	postsList, _ := s.posts.GetUserPostsPageList(ctx, req.UserLoginId, req.UserId, req.PageIndex, req.PageSize, req.ReleaseType, isActivityCode, preLanguageCodes, isLemonx, postsTops)
	var postscodes []string
	var postsIds []string
	if len(postsList) > 0 {
		postscodes = SelectTNoWhere(postsList, func(m *models.Post) string { return m.PostsCode })
		postsIds = SelectTNoWhere(postsList, func(m *models.Post) string { return m.PostsID })
		poststopics, _ := s.posts.GetPostsTopicByPostsIds(ctx, postsIds)
		postsRelationUsers, _ := s.posts.GetPostsUserRelationByPostsIds(ctx, postsIds)
		var transIds []string
		var languageCodes = []string{languageCode}
		languageCodes = append(languageCodes, languageCode)
		var trans []*models.CommentGradeTranslate
		if languageCode != "en" {
			languageCodes = append(languageCodes, "en")
		}
		for _, value := range postsList {
			if value.ReleaseType == 2 && value.CommentGrade > 0 {
				lange := strings.ToLower(value.LanguageCode)
				if lange != languageCode {
					transIds = append(transIds, value.PostsID)
				}

			}
		}
		if len(transIds) > 0 {
			trans, _ = s.posts.GetCommentGradesTrans(ctx, transIds, languageCodes)
		}
		var postsStatistics []*models.PostsStatistic
		serviceTypes, serviceErr := s.posts.GetServiceTypelist(ctx)

		var repetions []remote.GetRepetitionRequestItem
		for _, value := range postsList {
			repetions = append(repetions, remote.GetRepetitionRequestItem{
				Code:    value.PostsCode,
				Title:   value.Title,
				Content: value.Content,
			})
		}
		//查询标题和内容是否一致或者重复率 重复率大于90%不显示标题
		resRepet, repeatErr := remote.GetRepetition(remote.GetRepetitionRequest{
			Mode:    2,
			PostCol: repetions,
		})

		var postsStatisticserr error
		attachs, _ := s.posts.GetAttachments(ctx, postscodes)
		if req.UserId == req.UserLoginId {
			postsStatistics, postsStatisticserr = s.posts.GetPostsStatisticsByPostsIds(ctx, postsIds)
		}
		for _, value := range postsList {
			model := pb.GetPostsReplyItem{}
			model.ThemeCode = value.ObjectID
			if serviceErr == nil {
				if len(value.ObjectID) > 0 {
					if ExistString(specialServiceTypes, specialServiceType) {
						value.ObjectID = specialServiceType
					}
					serviceTypeInfo := FirstT(serviceTypes, func(m *models.ServiceProviderName) bool {
						return strings.ToLower(m.LanguageCode) == languageCode && strings.Trim(m.Code, " ") == strings.Trim(value.ObjectID, " ")
					})
					if serviceTypeInfo != nil {
						model.ThemeColor = ""
						model.Theme = serviceTypeInfo.Name
					}
				}
			}

			attach := Where(attachs, func(attachment *models.Attachment) bool { return attachment.RelatedID == value.PostsCode })
			attachorder := OrderByInt(attach, func(m *models.Attachment) int32 {
				return m.Sequence
			})
			model.Images = CreateImageModel(attachorder, languageCode, false, "8")
			if errcountry == nil {
				countryName := FirstTNoPointer(countryNames, func(t remote.GetCountryCodeByLanguageCodeResponse) bool {
					return t.CountryCode == value.CountryCode
				})
				if countryName != nil {
					model.CountryName = countryName.Name
				}
			}
			if len(postsStatistics) > 0 && postsStatisticserr == nil {
				model.IsShowViewCount = true
				var postsStatisticsingle = FirstT(postsStatistics, func(m *models.PostsStatistic) bool {
					return m.PostsID == value.PostsID
				})
				var ViewCount int = 0
				if postsStatisticsingle != nil {
					ViewCount = int(postsStatisticsingle.ViewCount) + CalculateViewCount(value.PublicTime, value.Title)
					model.ViewCount = strconv.Itoa(ViewCount)
				} else {
					ViewCount = CalculateViewCount(value.PublicTime, value.Title)
					model.ViewCount = strconv.Itoa(CalculateViewCount(value.PublicTime, value.Title))
				}
				model.IsShowViewCount = ViewCount >= 100
				if value.Status != 200 {
					model.IsShowViewCount = false
				}
			}
			model.PostsId = value.PostsID
			model.Title = utilstool.MyHtmlDecode(value.Title, true)
			model.TitleNew = utilstool.MyHtmlDecode(value.Title, true)
			model.ContentNew = utilstool.MyHtmlDecode(value.Content, false)
			isTop := FirstT(postsTops, func(m *models.PostsTop) bool {
				return value.PostsID == m.PostsID
			})
			model.IsTop = false
			if isTop != nil {
				model.IsTop = true
				model.TopContent = i18n.GetWithChineseValueDefaultEnglish(languageCode, "置顶")
				model.TopColor = TopColor
				model.TopBgColor = TopBgColr
			}

			singlepostsTopics := Where(poststopics, func(m *models.PostsTopicRelation) bool {
				return m.PostsID == value.PostsID
			})
			singlepostsUserRelations := Where(postsRelationUsers, func(m *models.PostsUserRelation) bool {
				return m.PostsID == value.PostsID
			})

			var postsTopics []*models.PostsTopicView
			if singlepostsTopics != nil {
				for _, v := range singlepostsTopics {
					postsTopics = append(postsTopics, &models.PostsTopicView{
						Id:     v.TopicID,
						Name:   "#" + v.TopicName,
						Sort:   v.Sort,
						Type:   2,
						Enable: true,
					})
				}
			}
			if singlepostsUserRelations != nil {
				for _, v := range singlepostsUserRelations {
					postsTopics = append(postsTopics, &models.PostsTopicView{
						Id:     v.UserID,
						Name:   "@" + v.NickName,
						Sort:   v.Sort,
						Type:   1,
						Enable: true,
					})
				}
			}
			var ordertopicinfo = OrderByInt(postsTopics, func(p *models.PostsTopicView) int32 {
				return p.Sort
			})

			for _, v := range ordertopicinfo {

				model.PostsTopicItems = append(model.PostsTopicItems, &pb.PostsTopicItems{
					Id:     v.Id,
					Name:   v.Name,
					Enable: v.Enable,
					Type:   v.Type,
				})
			}

			if len(value.JSONContentExcept) > 0 {
				value.Content = JsonContentConvertLan(value.JSONContentExcept, value.ObjectID, languageCode) + value.Content
			}
			if len(value.NewJSONContent) > 0 && value.ReleaseType == 1 {

				value.Content = NewJonsContentConvertLan(value.NewJSONContent, value.ObjectID, languageCode, files, values) + value.Content
			}

			if repeatErr == nil {

				if len(resRepet.RepetitionCol) > 0 {
					singleRepeat := FirstT(resRepet.RepetitionCol, func(item *remote.GetRepetitionResponseItem) bool { return value.PostsCode == item.Code })
					if singleRepeat != nil {
						if singleRepeat.IncludeFlag || singleRepeat.Rate > 0.9 {
							model.Title = ""
						}
					}
				}
			}
			model.Content = value.Content
			if value.ReleaseType == 2 && value.CommentGrade > 0 {
				if strings.ToLower(value.LanguageCode) != languageCode {
					if ExistString(transIds, value.PostsID) {

						language := FirstT(trans, func(tran *models.CommentGradeTranslate) bool {
							return strings.ToLower(tran.LanguageCode) == languageCode && value.PostsID == tran.CommentGradeID
						})
						if language != nil {
							model.Title = utilstool.MyHtmlDecode(language.Title, true)
							model.TitleNew = utilstool.MyHtmlDecode(language.Title, true)
							model.ContentNew = utilstool.MyHtmlDecode(language.Content, false)
							model.Content = utilstool.MyHtmlDecode(language.Content, false)
							model.ContentLanguage = languageCode
						} else {
							languageen := FirstT(trans, func(tran *models.CommentGradeTranslate) bool {
								return strings.ToLower(tran.LanguageCode) == "en" && value.PostsID == tran.CommentGradeID
							})
							if languageen != nil {
								model.Title = utilstool.MyHtmlDecode(languageen.Title, true)
								model.TitleNew = utilstool.MyHtmlDecode(languageen.Title, true)
								model.ContentNew = utilstool.MyHtmlDecode(languageen.Content, false)
								model.Content = utilstool.MyHtmlDecode(languageen.Content, false)
								model.ContentLanguage = "en"
							}
						}
					}
				}
			}
			model.DataType = 3
			if value.ReleaseType == 2 && value.CommentGrade > 0 {
				var sign PostsSignSetting
				if value.CommentGrade == 3 {
					sign = PostsSigns[0]
					model.DataType = 2
				} else if value.CommentGrade == 1 {
					sign = PostsSigns[1]
					model.DataType = 9
				} else {
					sign = PostsSigns[2]
					model.DataType = 9
				}
				//0-列表、详情页都不显示，1-列表显示，2-详情页显示，3-列表、详情页都显示
				model.Sign = &pb.PostsSign{
					IsShow:  2,
					BgColor: sign.BgColor,
					Word:    i18n.GetWithChineseValueDefaultEnglish(languageCode, sign.Word),
					Icon:    "",
					Color:   sign.Color,
				}
			}

			model.ShareUrl = ""
			model.PublicTime = utilstool.ConvertToUnix(value.PublicTime)
			if len(value.AffEnterpriseCode) > 0 {
				model.UserId = value.AffEnterpriseCode
			} else {
				model.UserId = value.UserID
			}
			result = append(result, &model)
		}
	}

	return &pb.GetPersonHomePostsPageListReply{
		List: result,
	}, nil

}
func (s *WikiCommunityService) GetUserIdByPostsId(ctx context.Context, req *pb.GetUserIdByPostsIdRequest) (*pb.GetUserIdByPostsIdReply, error) {
	userid, _ := s.posts.GetUserIdByPostsId(ctx, req.PostsId)
	var struserid = *userid
	var removelastuserid = struserid[:len(struserid)-1]
	if ExistString(OfficalCodes, removelastuserid) {
		struserid = removelastuserid
	}
	return &pb.GetUserIdByPostsIdReply{
		UserId: struserid,
	}, nil
}

func (s *WikiCommunityService) GetPostsNumber(ctx context.Context, req *pb.GetPostsNumberRequest) (*pb.GetPostsNumberReply, error) {

	datamap := GetmateData(ctx)
	preLanguageCode := datamap["preLanguageCode"]
	preLanguageCodes := strings.Split(preLanguageCode, ",")
	//var areacode = country.GetAreaCodeByCode(datamap["countryCode"])
	var pcount int
	var isActivityCode = ExistString(OfficalCodes, req.UserId)
	isLemonx := false
	if isActivityCode {
		isLemonx = req.UserId == LemonXCode
		req.UserId = req.UserId + "2"
	}
	postsnumber, _ := s.posts.GetPersonPostsNumber(ctx, req.UserId, req.UserLoginId, isActivityCode, 1, preLanguageCodes, isLemonx)
	if postsnumber != nil {
		pcount = *postsnumber
	}
	var dcount int
	dailysnumber, _ := s.posts.GetPersonPostsNumber(ctx, req.UserId, req.UserLoginId, isActivityCode, 2, preLanguageCodes, isLemonx)
	if postsnumber != nil {
		dcount = *dailysnumber
	}
	return &pb.GetPostsNumberReply{
		BusinessNumber: strconv.Itoa(pcount),
		DailyNumber:    strconv.Itoa(dcount),
	}, nil

}

// GetUserViewHistoryPosts 浏览历史
func (s *WikiCommunityService) GetUserViewHistoryPosts(ctx context.Context, req *pb.GetUserViewHistoryPostsRequest) (*pb.GetUserViewHistoryPostsReply, error) {
	var result []*pb.GetUserViewHistoryPostsItem
	collects, err := s.posts.GetUserViewHistoryPosts(ctx, req.UserLoginId, int(req.PageIndex), int(req.PageSize), req.ReleaseType)
	if err == nil {
		for _, value := range collects {
			result = append(result, &pb.GetUserViewHistoryPostsItem{

				PostsId:     value.PostsID,
				DataType:    value.ObjectType,
				CollectTime: utilstool.ConvertToUnixMilli(value.CreateTime),
			})
		}
	}
	return &pb.GetUserViewHistoryPostsReply{
		List: result,
	}, nil
}

func (s *WikiCommunityService) GetPostsUserApplaud(ctx context.Context, req *pb.GetUserPostsApplaudRequest) (*pb.GetUserPostsApplaudReply, error) {
	var list []*PostsKeyValue
	var result []*pb.GetUserPostsApplaudReplyItem
	applauds, err := s.posts.GetPostsUserApplaud(ctx, req.PostsIds)
	if err == nil {
		for _, v := range applauds {
			list = append(list, &PostsKeyValue{
				Key:   v.PostsID,
				Value: strconv.Itoa(int(v.ApplaudCount)),
			})
		}
	}
	for _, v := range req.PostsIds {
		first := FirstT(list, func(t *PostsKeyValue) bool {
			return v == t.Key
		})
		if first == nil {
			result = append(result, &pb.GetUserPostsApplaudReplyItem{
				PostsId: v,
				Number:  "0",
			})
		} else {
			result = append(result, &pb.GetUserPostsApplaudReplyItem{
				PostsId: v,
				Number:  first.Value,
			})
		}
	}

	return &pb.GetUserPostsApplaudReply{
		List: result,
	}, nil
}

func (_ *WikiCommunityService) GetPostsReplyStatus(ctx context.Context, req *pb.GetPostsReplyStatusRequest) (*pb.GetPostsReplyStatusReply, error) {
	var result []*pb.GetPostsReplyStatusReplyItem
	var codes = SelectTNoWhere(DetailReplyConfigs, func(t *PostsDetailReplyConfig) string {
		return t.Code
	})
	for _, v := range req.DataUserId {
		if ExistString(codes, v) {
			single := FirstT(DetailReplyConfigs, func(t *PostsDetailReplyConfig) bool {
				return t.Code == v
			})
			result = append(result, &pb.GetPostsReplyStatusReplyItem{
				DataUserId: v,
				Status:     single.Status,
			})
		} else {
			result = append(result, &pb.GetPostsReplyStatusReplyItem{
				DataUserId: v,
				Status:     3,
			})
		}
	}

	return &pb.GetPostsReplyStatusReply{
		List: result,
	}, nil
}

func (s *WikiCommunityService) GetTopicRecommend(ctx context.Context,
	in *pb.GetTopicRecommendRequest) (out *pb.GetTopicRecommendReply, err error) {
	lang, ok := icontext.LanguageCodeFrom(ctx)
	if !ok {
		lang = "zh-cn"
	}
	country, ok := icontext.CountryCodeFrom(ctx)
	if !ok {
		country = "156"
	}
	area, err := remote.GetAreaCode(country)
	if err != nil {
		return
	}
	topics, err := s.posts.GetTopicRecommend(ctx, lang, area)
	// ids := make([]string, 0, len(topics))
	ids := SelectTNoWhere(topics, func(in *models.PostsTopic) string {
		return in.TopicID
	})
	statistics, err := s.posts.GetTopicStatisticsList(ctx, ids)
	if err != nil {
		return
	}
	items := make([]*pb.TopicRecommend, 0, len(topics))
	for _, v := range topics {
		var view int32 = 0
		s := FirstT(statistics, func(in *models.PostsTopicStatistic) bool {
			return in.TopicID == v.TopicID
		})
		if s != nil {
			view = s.TopicViewCount
		}
		items = append(items, &pb.TopicRecommend{
			Prefix:    "#",
			TopicId:   v.TopicID,
			Content:   v.Content,
			ViewCount: view + v.ViewCountSettings,
		})
	}
	out = &pb.GetTopicRecommendReply{
		RecommendCol: items,
	}
	return
}
func (s *WikiCommunityService) GetTopicDetail(ctx context.Context,
	in *pb.GetTopicDetailRequest) (out *pb.GetTopicDetailReply, err error) {
	// 限制首页才增加话题浏览量
	if in.Type == 1 && in.PageIndex == 1 {
		s.posts.UpdateTopicViewCount(ctx, in.TopicId)
	}
	topic, statistics, err := s.posts.GetTopicDetail(ctx, in.TopicId)
	if topic.IsDelete == 1 {
		err = errors.New("话题已经删除")
		return
	}
	if topic.TopicStatus != 1 {
		err = errors.New("话题待审核")
		return
	}
	if in.PageIndex <= 0 {
		in.PageIndex = 1
	}
	if in.PageSize <= 0 || in.PageSize >= 50 {
		in.PageSize = 20
	}

	collect, err := s.posts.GetTopicCollect(ctx, in.UserId, in.TopicId)
	if err != nil {
		return
	}
	c := false
	if collect.ID != "" {
		c = true
	}
	img := topic.BgImage
	imgurl := utilstool.GetImageFullPath(img, "8")
	out = &pb.GetTopicDetailReply{
		Prefix:           "#",
		TopicId:          topic.TopicID,
		Content:          topic.Content,
		Introduction:     topic.Intro,
		BackgroudImage:   imgurl,
		ViewCount:        statistics.TopicViewCount + topic.ViewCountSettings,
		ParticipantCount: statistics.ParticipantCount,
		Collected:        c,
	}
	var posts []*models.Post
	var total int64
	if in.Type == 1 {
		posts, total, _ = s.posts.GetTopicHotPosts(ctx, in)
	} else {
		posts, total, _ = s.posts.GetTopicLatesPosts(ctx, in)
	}
	items := s.BindDetail(ctx, posts)
	out.PostsCol = items
	out.PostsCount = int32(total)

	return
}

func (s *WikiCommunityService) BindDetail(ctx context.Context, in []*models.Post) (out []*pb.GetPostsReplyItem) {
	lang, ok := icontext.LanguageCodeFrom(ctx)
	if !ok {
		lang = "en"
	}
	var postscodes []string
	if len(in) > 0 {
		postscodes = SelectTNoWhere(in, func(m *models.Post) string { return m.PostsCode })
		var repetions []remote.GetRepetitionRequestItem
		for _, value := range in {
			repetions = append(repetions, remote.GetRepetitionRequestItem{
				Code:    value.PostsCode,
				Title:   value.Title,
				Content: value.Content,
			})
		}
		//查询标题和内容是否一致或者重复率 重复率大于90%不显示标题
		resRepet, repeatErr := remote.GetRepetition(remote.GetRepetitionRequest{
			Mode:    2,
			PostCol: repetions,
		})
		attachs, _ := s.posts.GetAttachments(ctx, postscodes)
		for _, value := range in {
			model := &pb.GetPostsReplyItem{}
			attach := Where(attachs, func(attachment *models.Attachment) bool { return attachment.RelatedID == value.PostsCode })
			for _, img := range attach {
				list, detail := utilstool.GetPath(img.Content, "8")
				if img.Width <= 0 {
					img.Width = utilstool.IMGWIDTH
				}
				if img.Height <= 0 {
					img.Height = utilstool.IMGHEIGHT
				}
				model.Images = append(model.Images, &pb.Image{
					Width:  img.Width,
					Height: img.Height,
					List:   list,
					Detail: detail,
				})
			}
			if len(value.JSONContentExcept) > 0 {
				value.Content = JsonContentConvertLan(value.JSONContentExcept, value.ObjectID, lang) + value.Content
			}
			if len(value.AffEnterpriseCode) > 0 {
				model.UserId = value.AffEnterpriseCode
			} else {
				model.UserId = value.UserID
			}
			model.PostsId = value.PostsID
			model.Title = utilstool.MyHtmlDecode(value.Title, true)
			model.TitleNew = utilstool.MyHtmlDecode(value.Title, true)
			model.ContentNew = utilstool.MyHtmlDecode(value.Content, false)
			if repeatErr == nil {
				if len(resRepet.RepetitionCol) > 0 {
					singleRepeat := FirstT(resRepet.RepetitionCol, func(item *remote.GetRepetitionResponseItem) bool { return value.PostsCode == item.Code })
					if singleRepeat != nil {
						if singleRepeat.IncludeFlag || singleRepeat.Rate > 0.9 {
							model.Title = ""
						}
					}
				}
			}
			model.Content = value.Content
			model.ThemeCode = ""
			model.ThemeColor = ""
			model.Theme = ""
			model.ShareUrl = ""
			model.PublicTime = utilstool.ConvertToUnixMilli(value.PublicTime)
			model.ReleaseType = value.ReleaseType
			out = append(out, model)
		}
	}
	return
}

func (s *WikiCommunityService) GetUserTopicCollectList(ctx context.Context, req *pb.GetUserTopicCollectListRequest) (*pb.GetUserTopicCollectListReply, error) {
	var result []*pb.GetUserTopicCollectListReplyItem
	collectList, err := s.posts.GetUserCollectTopicPageList(ctx, int(req.PageIndex), int(req.PageSize), req.UserId)
	if err == nil {
		var topicIds []string
		if len(collectList) > 0 {
			topicIds = SelectTNoWhere(collectList, func(t *models.PostsTopicCollectView) string {
				return t.TopicID
			})
		}
		var topicstatistics []*models.PostsTopicStatistic
		var topics []*models.PostsTopic
		fmt.Println(len(topicIds))
		if len(topicIds) > 0 {
			topicstatistics, err = s.posts.GetTopicStatisticsList(ctx, topicIds)
			topics, err = s.posts.GetTopicInfoByTopicIds(ctx, topicIds)
		}
		for _, v := range collectList {

			model := &pb.GetUserTopicCollectListReplyItem{
				TopicName: v.TopicName,
				TopicId:   v.TopicID,
			}
			if len(topicstatistics) > 0 {
				singlemodel := FirstT(topicstatistics, func(m *models.PostsTopicStatistic) bool {
					return v.TopicID == m.TopicID
				})
				var settingscount int32 = 0
				settingscountinfo := FirstT(topics, func(m *models.PostsTopic) bool {
					return v.TopicID == m.TopicID
				})
				if settingscountinfo != nil {
					settingscount = settingscountinfo.ViewCountSettings
				}
				if singlemodel != nil {

					model.ViewCount = utilstool.ShowNumber(singlemodel.TopicViewCount + settingscount)
					model.UserCount = utilstool.ShowNumber(singlemodel.ParticipantCount)
					model.IsShowUserCount = singlemodel.ParticipantCount >= 100
					model.IsShowViewCount = singlemodel.TopicViewCount+settingscount >= 100
				}
			}
			result = append(result, model)
		}
	}
	return &pb.GetUserTopicCollectListReply{
		List: result,
	}, nil
}

var (
	officalCount = map[string]models.Offical{
		"861472539": {Fans: "offical_news_fans", LastFans: "offical_news_fans_last", LastApplaud: "offical_news_applaud_last"},
		"394856217": {Fans: "offical_express_fans", LastFans: "offical_express_fans_last", LastApplaud: "offical_express_applaud_last"},
		"674159832": {Fans: "offical_survey_fans", LastFans: "offical_survey_fans_last", LastApplaud: "offical_survey_applaud_last"},
		"148293765": {Fans: "offical_mediation_fans", LastFans: "offical_mediation_fans_last", LastApplaud: "offical_mediation_applaud_last"},
		"582310937": {Fans: "offical_activity_fans", LastFans: "offical_activity_fans_last", LastApplaud: "offical_activity_applaud_last"},
		"218302325": {Fans: "offical_lemonx_fans", LastFans: "offical_lemonx_fans_last", LastApplaud: "offical_lemonx_applaud_last"},
		"753115278": {Fans: "offical_expo_fans", LastFans: "offical_expo_fans_last", LastApplaud: "offical_expo_applaud_last"},
		"933042519": {Fans: "offical_elite_fans", LastFans: "offical_elite_fans_last", LastApplaud: "offical_elite_applaud_last"},
		"243453620": {Fans: "offical_skyline_fans", LastFans: "offical_skyline_fans_last", LastApplaud: "offical_skyline_applaud_last"},
	}
)

func (s *WikiCommunityService) GetUserBusinessCountOrApplaudCount(ctx context.Context, req *pb.GetUserBusinessCountOrApplaudCountRequest) (*pb.GetUserBusinessCountOrApplaudCountReply, error) {
	var result []*pb.GetUserBusinessCountOrApplaudCountReplyItem
	var originuserids []string
	for _, v := range req.UserIds {
		originuserids = append(originuserids, v)
	}
	var generaloffice = []string{WikiFxCode, WikiFXEducationCode}
	for _, v := range generaloffice {
		if ExistString(originuserids, v) {
			req.UserIds = append(req.UserIds, v+"2")
		}
	}
	applaudCount, _ := s.posts.GetPostsUsersApplaudCount(ctx, req.UserIds)
	buinsessCount, _ := s.posts.GetPostsUsersPostsCount(ctx, req.UserIds)
	officalApplaudCount, err := s.GetOfficalApplaudCount(ctx, req.UserIds)
	if err != nil {
		return nil, err
	}
	for _, v := range originuserids {
		model := pb.GetUserBusinessCountOrApplaudCountReplyItem{
			UserId:       v,
			ApplaudCount: 0,
			PostsCount:   0,
		}
		applaud := FirstT(applaudCount, func(m *models.UserTopicStatisticView) bool {
			return v == m.UserId || m.UserId == v+"2"
		})
		if applaud != nil {
			model.ApplaudCount = int32(applaud.ApplaudCount)
		}
		if oc, ok := officalApplaudCount[v]; ok {
			model.ApplaudCount = int32(oc)
		}
		posts := FirstT(buinsessCount, func(m *models.PostsUserCount) bool {
			return v == m.UserId
		})
		if posts != nil {
			model.PostsCount = int32(posts.PostsCount)
		}
		result = append(result, &model)
	}

	return &pb.GetUserBusinessCountOrApplaudCountReply{
		List: result,
	}, nil
}

func (s *WikiCommunityService) GetOfficalApplaudCount(ctx context.Context, userIds []string) (out map[string]int, err error) {
	out = make(map[string]int)
	current := make(map[string]models.Offical)
	keys := make([]string, 0, 10)
	for _, v := range userIds {
		if oc, ok := officalCount[v]; ok {
			keys = append(keys, oc.Fans)
			keys = append(keys, oc.LastFans)
			keys = append(keys, oc.LastApplaud)
			current[v] = oc
		}
	}
	if len(keys) > 0 {
		configs, erri := s.posts.GetConfig(ctx, keys)
		if erri != nil {
			err = erri
			return
		}
		postsNumber, _ := s.posts.GetOfficalPostsNumber(ctx, []string{"9330425192", "2434536202"})
		for k, v := range current {
			config := FirstT(configs, func(m *models.Config) bool {
				return m.ConfigType == v.LastApplaud
			})
			var fans = ConvertConfigValue(FirstT(configs, func(m *models.Config) bool {
				return m.ConfigType == v.Fans
			}))
			var lastFans = ConvertConfigValue(FirstT(configs, func(m *models.Config) bool {
				return m.ConfigType == v.LastFans
			}))
			var modified time.Time
			if config != nil {
				modified = config.ModifiedAt
			}
			codes := []string{k}
			applauds, erri := s.posts.GetEnterpriseApplaudCount(ctx, codes, modified)
			if erri != nil {
				err = erri
				return
			}
			views, erri := s.posts.GetEnterpriseViewCount(ctx, codes, modified)
			if erri != nil {
				err = erri
				return
			}
			users, erri := remote.GetUserFansCount(ctx, codes, modified)
			if erri != nil {
				err = erri
				return
			}
			var user remote.GetUserFansOutPut
			if len(users) > 0 {
				user = users[0]
			}
			applaud := applauds[k]
			if (k == "933042519" || k == "243453620") && postsNumber[k+"2"] == 0 {
				dictapp, _ := s.posts.GetEnterpriseApplaudCount(ctx, codes, time.Time{})
				out[k] = dictapp[k]
				continue
			}
			view := views[k]
			number := Calculate(k, user.TotalCount+fans, ConvertConfigValue(config), user.Count+(fans-lastFans), applaud, view)
			out[k] = number
		}
	}
	return
}

func Calculate(no string, totalFans, last, fans, applaud, view int) (out int) {
	codes := []string{"582310937", "218302325", "753115278", "933042519"}
	if ExistString(codes, no) {
		return CalculateNew(no, totalFans, last, fans, applaud, view)
	}
	if no == "394856217" {
		return last + applaud + view
	}
	if totalFans < 50000 {
		return last + fans + applaud + view
	} else {
		return last + applaud
	}
}

func CalculateNew(no string, totalFans, last, fans, applaud, view int) (out int) {
	if totalFans < 3000 {
		return last + fans + applaud + view
	} else {
		return last + applaud
	}
}

func ConvertConfigValue(c *models.Config) (out int) {
	if c != nil {
		out, _ = strconv.Atoi(c.ConfigValue)
	}
	return
}

// ActivityPageList 活动
func (s *WikiCommunityService) ActivityPageList(ctx context.Context, request *pb.ActivityListRequest) (*pb.ActivityListReply, error) {
	var statusmap = map[int32]string{
		1: "进行中",
		2: "未开始",
		3: "已结束",
	}
	var statusColormap = map[int32]string{
		1: "#3D3D3D",
		2: "#FFFFFF",
		3: "#FFFFFF",
	}

	var statusBgColormap = map[int32]string{
		1: "#FFD733",
		2: "#FF6000",
		3: "#000000",
	}
	var result []*pb.ActivityListItemReply
	datamap := GetmateData(ctx)
	languageCode := datamap["languageCode"]
	areaCode := datamap["areaCode"]
	listpage, err := s.posts.GetActivityPageList(ctx, int(request.PageIndex), int(request.PageSize), areaCode, languageCode)
	if err != nil {
		return nil, err
	}
	for _, v := range listpage {
		var status = v.StartStatus
		var startTime = v.StartTime.Format("2006.01-02")
		var endTime = v.EndTime.Format("2006.01-02")
		if time.Now().Year() == v.StartTime.Year() {
			startTime = v.StartTime.Format("01-02")
		}
		if time.Now().Year() == v.StartTime.Year() {
			endTime = v.EndTime.Format("01-02")
		}
		result = append(result, &pb.ActivityListItemReply{
			ActivityId:    v.ID,
			Title:         v.ActivityTitle,
			Intro:         v.Intro,
			StartTime:     startTime,
			EndTime:       endTime,
			Status:        status,
			StatusContent: i18n.GetWithChineseValueDefaultEnglish(languageCode, statusmap[status]),
			StatusBgColor: statusBgColormap[status],
			StatusColor:   statusColormap[status],
			Image:         utilstool.GetSinglePath(v.ImageURL, "8"),
		})
	}
	return &pb.ActivityListReply{
		List: result,
	}, nil
}
func GetActivityStatus(time1, time2 time.Time) int32 {
	var now = time.Now().UTC().Add(time.Hour * 8)
	if now.After(time2) {
		return 3
	}
	if now.Before(time1) {
		return 2
	}
	return 1
}

// ActivityDetail 活动详情
func (s *WikiCommunityService) ActivityDetail(ctx context.Context, request *pb.ActivityDetailRequest) (*pb.ActivityDetailReply, error) {
	datamap := GetmateData(ctx)
	languageCode := datamap["languageCode"]
	activitysingle, err := s.posts.GetActivityDetail(ctx, request.ActivityId)
	if err != nil {
		return nil, err
	}
	if activitysingle == nil {
		return nil, errors.New("Error")
	}
	var isWonderful = false
	if activitysingle.IsWonderful == 1 {
		isWonderful = true
	}
	if request.PageIndex == 0 {
		request.PageIndex = 1
	}
	if request.PageIndex == 1 {
		var statusmap = map[int32]string{
			1: "进行中",
			2: "未开始",
			3: "已结束",
		}
		var statusColormap = map[int32]string{
			1: "#3D3D3D",
			2: "#FFFFFF",
			3: "#FFFFFF",
		}

		var statusBgColormap = map[int32]string{
			1: "#FFD733",
			2: "#FF6000",
			3: "#000000",
		}
		var topicName = ""
		if len(activitysingle.TopicID) > 0 {
			topicInfo, _, _ := s.posts.GetTopicDetail(ctx, activitysingle.TopicID)
			topicName = topicInfo.Content
		}

		userphotos, _ := s.posts.GetActivityJoinUsePhotos(ctx, request.ActivityId)
		var JoinUserPhoto []string
		for _, v := range userphotos {
			if strings.Contains(*v, "ico") {
				JoinUserPhoto = append(JoinUserPhoto, "https://icoimg.fx696.com/"+strings.TrimLeft(*v, "/")+"_wiki-template-global")
			} else {
				JoinUserPhoto = append(JoinUserPhoto, "https://img.fx696.com/"+strings.TrimLeft(*v, "/")+"_wiki200")
			}
		}
		var status = activitysingle.StartStatus
		var startTime = activitysingle.StartTime.Format("2006.01-02")
		var endTime = activitysingle.EndTime.Format("2006.01-02")
		if time.Now().Year() == activitysingle.StartTime.Year() {
			startTime = activitysingle.StartTime.Format("01-02")
		}
		if time.Now().Year() == activitysingle.StartTime.Year() {
			endTime = activitysingle.EndTime.Format("01-02")
		}
		var PostsCols []*pb.GetPostsReplyItem
		if isWonderful == true && len(activitysingle.WonderfulTopic) > 0 {
			var topicIds = strings.Split(activitysingle.WonderfulTopic, ",")
			posts, _ := s.posts.GetTopicsPosts(ctx, topicIds, request.PageIndex, request.PageSize)
			PostsCols = s.BindDetail(ctx, posts)
		}
		disabledtopicIds, _ := s.posts.GetActivityTopicDisabled(ctx, request.ActivityId)
		if disabledtopicIds != nil {
			totopicIds := PtrToString(disabledtopicIds)
			doc, _ := goquery.NewDocumentFromReader(strings.NewReader(activitysingle.Content))
			doc.Find(".hashtag").Each(
				func(i int, s *goquery.Selection) {
					topicId, _ := s.Attr("tid")
					if ExistString(totopicIds, topicId) {
						s.SetAttr("class", "has-not-htag")
					}

				})
			html, err := doc.Html()
			if err == nil {
				activitysingle.Content = html
			}
		}
		var result = &pb.ActivityDetailReply{
			ActivityId:      activitysingle.ID,
			Title:           activitysingle.ActivityTitle,
			Intro:           activitysingle.Intro,
			StartTime:       startTime,
			EndTime:         endTime,
			Status:          status,
			StatusContent:   i18n.GetWithChineseValueDefaultEnglish(languageCode, statusmap[status]),
			StatusBgColor:   statusBgColormap[status],
			StatusColor:     statusColormap[status],
			Image:           utilstool.GetSinglePath(activitysingle.ImageURL, "8"),
			JoinCount:       strconv.Itoa(int(activitysingle.JoinUserCountShow)),
			JoinUserPhoto:   JoinUserPhoto,
			Content:         activitysingle.Content,
			LinkType:        activitysingle.LinkType,
			LinkContent:     activitysingle.LinkContent,
			TopicId:         activitysingle.TopicID,
			TopicName:       topicName,
			IsWonderful:     isWonderful,
			LinkAddress:     activitysingle.LinkAddress,
			PostsCol:        PostsCols,
			LanguageCode:    activitysingle.LanguageCode,
			AreaCode:        activitysingle.AreaCode,
			LinkAddressType: activitysingle.LinkAddressType,
		}
		return result, nil
	} else {
		var PostsCols []*pb.GetPostsReplyItem
		if isWonderful == true && len(activitysingle.WonderfulTopic) > 0 {
			var topicIds = strings.Split(activitysingle.WonderfulTopic, ",")
			posts, _ := s.posts.GetTopicsPosts(ctx, topicIds, request.PageIndex, request.PageSize)
			PostsCols = s.BindDetail(ctx, posts)
			var result = &pb.ActivityDetailReply{
				ActivityId: activitysingle.ID,
				PostsCol:   PostsCols,
			}
			return result, nil
		} else {
			var result = &pb.ActivityDetailReply{}
			return result, nil
		}
	}
}

// ActivityPostsPageList 广场
func (s *WikiCommunityService) ActivityPostsPageList(ctx context.Context, req *pb.ActivityPostsPageListRequest) (*pb.ActivityPostsPageListReply, error) {
	var specialServiceTypes = []string{"96713708", "51692449", "73566309", "64104355"}
	var specialServiceType = "40124359"
	datamap := GetmateData(ctx)
	languageCode := datamap["languageCode"]
	areaCode := datamap["areaCode"]
	var result []*pb.GetPostsReplyItem
	countryNames, errcountry := remote.GetCountryCodeByLanguageCode("zh-CN", "156")
	var activityIds = s.posts.GetActivityIds(ctx, areaCode, languageCode)
	if len(activityIds) == 0 {
		return &pb.ActivityPostsPageListReply{
			Items: result,
		}, nil
	}

	postsList, _ := s.posts.GetActivityPostsPageList(ctx, utilstool.PointerToString(activityIds), int(req.PageIndex), int(req.PageSize))
	var postscodes []string
	var postsIds []string
	if len(postsList) > 0 {
		serviceTypes, serviceErr := s.posts.GetServiceTypelist(ctx)
		postsIds = SelectTNoWhere(postsList, func(m *models.Post) string { return m.PostsID })
		postscodes = SelectTNoWhere(postsList, func(m *models.Post) string { return m.PostsCode })
		poststopics, _ := s.posts.GetPostsTopicByPostsIds(ctx, postsIds)
		postsRelationUsers, _ := s.posts.GetPostsUserRelationByPostsIds(ctx, postsIds)
		fmt.Println(len(poststopics))
		fmt.Println(len(postsRelationUsers))
		var repetions []remote.GetRepetitionRequestItem
		for _, value := range postsList {
			repetions = append(repetions, remote.GetRepetitionRequestItem{
				Code:    value.PostsCode,
				Title:   value.Title,
				Content: value.Content,
			})
		}
		//查询标题和内容是否一致或者重复率 重复率大于90%不显示标题
		resRepet, repeatErr := remote.GetRepetition(remote.GetRepetitionRequest{
			Mode:    2,
			PostCol: repetions,
		})

		attachs, _ := s.posts.GetAttachments(ctx, postscodes)

		for _, value := range postsList {

			model := pb.GetPostsReplyItem{}
			model.ThemeCode = value.ObjectID
			if serviceErr == nil {
				if len(value.ObjectID) > 0 {
					if ExistString(specialServiceTypes, specialServiceType) {
						value.ObjectID = specialServiceType
					}

					serviceTypeInfo := FirstT(serviceTypes, func(m *models.ServiceProviderName) bool {
						return strings.ToLower(m.LanguageCode) == languageCode && strings.Trim(m.Code, " ") == strings.Trim(value.ObjectID, " ")
					})

					if serviceTypeInfo != nil {
						model.ThemeColor = ""
						model.Theme = serviceTypeInfo.Name
					}
				}
			}

			attach := Where(attachs, func(attachment *models.Attachment) bool { return attachment.RelatedID == value.PostsCode })
			attachorder := OrderByInt(attach, func(m *models.Attachment) int32 {
				return m.Sequence
			})
			for _, img := range attachorder {
				list, detail := utilstool.GetPath(img.Content, "8")
				if img.Width <= 0 {
					img.Width = utilstool.IMGWIDTH
				}
				if img.Height <= 0 {
					img.Height = utilstool.IMGHEIGHT
				}
				model.Images = append(model.Images, &pb.Image{
					Width:  img.Width,
					Height: img.Height,
					List:   list,
					Detail: detail,
				})
			} //1 2 3 4 5 6 7 8
			if errcountry == nil {
				countryName := FirstTNoPointer(countryNames, func(t remote.GetCountryCodeByLanguageCodeResponse) bool {
					return t.CountryCode == value.CountryCode
				})
				if countryName != nil {
					model.CountryName = countryName.Name
				}
			}
			model.ContentLanguage = value.LanguageCode
			model.Grade = value.CommentGrade
			model.PostsId = value.PostsID
			model.Title = utilstool.MyHtmlDecode(value.Title, true)
			model.TitleNew = utilstool.MyHtmlDecode(value.Title, true)
			model.ContentNew = utilstool.MyHtmlDecode(value.Content, false)
			model.EnterpriseCode = value.RelationEnterpriseCode
			model.ReleaseType = value.ReleaseType
			if len(value.JSONContentExcept) > 0 {
				value.Content = JsonContentConvertLan(value.JSONContentExcept, value.ObjectID, languageCode) + value.Content
			}
			if len(value.NewJSONContent) > 0 && value.ReleaseType == 1 {
				files, _ := s.posts.GetTemplatedynamicfields(ctx)
				values, _ := s.posts.GetTemplatedynamicValues(ctx)
				value.Content = NewJonsContentConvertLan(value.NewJSONContent, value.ObjectID, languageCode, files, values) + value.Content
			}
			if repeatErr == nil {
				if len(resRepet.RepetitionCol) > 0 {
					singleRepeat := FirstT(resRepet.RepetitionCol, func(item *remote.GetRepetitionResponseItem) bool { return value.PostsCode == item.Code })
					if singleRepeat != nil {
						if singleRepeat.IncludeFlag || singleRepeat.Rate > 0.9 {
							model.Title = ""
						}
					}
				}
			}
			var sign PostsSignSetting
			if value.ObjectType > 0 {
				if value.CommentGrade == 3 {
					sign = PostsSigns[0]
				} else {
					sign = PostsSigns[1]
				}
				model.Sign = &pb.PostsSign{
					IsShow:  2,
					BgColor: sign.BgColor,
					Word:    sign.Word,
					Icon:    "",
					Color:   sign.Color,
				}
			}
			//0-列表、详情页都不显示，1-列表显示，2-详情页显示，3-列表、详情页都显示
			model.Content = utilstool.MyHtmlDecode(value.Content, false)

			model.ShareUrl = ""
			fmt.Println(value.PublicTime)
			model.PublicTime = utilstool.ConvertToUnixMilli(value.PublicTime)
			if len(value.AffEnterpriseCode) > 0 {
				model.UserId = value.AffEnterpriseCode
			} else {
				model.UserId = value.UserID
			}
			singlepostsTopics := Where(poststopics, func(m *models.PostsTopicRelation) bool {
				return m.PostsID == value.PostsID
			})
			singlepostsUserRelations := Where(postsRelationUsers, func(m *models.PostsUserRelation) bool {
				return m.PostsID == value.PostsID
			})

			var postsTopics []*models.PostsTopicView
			if singlepostsTopics != nil {
				for _, v := range singlepostsTopics {
					postsTopics = append(postsTopics, &models.PostsTopicView{
						Id:     v.TopicID,
						Name:   "#" + v.TopicName,
						Sort:   v.Sort,
						Type:   2,
						Enable: true,
					})
				}
			}
			if singlepostsUserRelations != nil {
				for _, v := range singlepostsUserRelations {
					postsTopics = append(postsTopics, &models.PostsTopicView{
						Id:     v.UserID,
						Name:   "@" + v.NickName,
						Sort:   v.Sort,
						Type:   1,
						Enable: true,
					})
				}
			}
			var ordertopicinfo = OrderByInt(postsTopics, func(p *models.PostsTopicView) int32 {
				return p.Sort
			})

			for _, v := range ordertopicinfo {
				model.PostsTopicItems = append(model.PostsTopicItems, &pb.PostsTopicItems{
					Id:     v.Id,
					Name:   v.Name,
					Enable: v.Enable,
					Type:   v.Type,
				})
			}
			result = append(result, &model)
		}
	}

	return &pb.ActivityPostsPageListReply{
		Items: result,
	}, nil

}

// UserJoinActivity参与活动
func (s *WikiCommunityService) UserJoinActivity(ctx context.Context, req *pb.UserJoinActivityRequest) (*pb.EmptyResponse, error) {
	datamap := GetmateData(ctx)
	userid := datamap["userid"]
	relativeurl := utilstool.GetRelativeImageUrl(req.AvatarAddress)
	avataraddress := strings.ReplaceAll(relativeurl, "_wiki200", "")
	avataraddress = strings.ReplaceAll(avataraddress, "_wiki-template-global", "")
	s.posts.JoinActivity(ctx, userid, req.ActivityId, avataraddress)
	return &pb.EmptyResponse{}, nil
}
func (s *WikiCommunityService) GetTopicName(ctx context.Context, req *pb.UserTopicNameRequest) (*pb.UserTopicNameReply, error) {
	topicName, err := s.posts.GetTopicNameByTopicId(ctx, req.TopicId)
	if err != nil {
		return nil, err
	}
	return &pb.UserTopicNameReply{
		TopicName: topicName,
	}, nil
}

func (s *WikiCommunityService) GetSingleUserApplaudCount(ctx context.Context, req *pb.GetSingleUserApplaudCountRequest) (*pb.GetSingleUserApplaudCountReply, error) {
	applaudCount, _ := s.posts.GetPostsUsersApplaudCount(ctx, []string{req.UserId})
	if len(applaudCount) == 0 {
		return &pb.GetSingleUserApplaudCountReply{
			ApplaudNumber: 0,
		}, nil
	}
	return &pb.GetSingleUserApplaudCountReply{
		ApplaudNumber: int64(applaudCount[0].ApplaudCount),
	}, nil

}

// AnalyzePostsTraderInfo 通过大模型分析内容的交易商信息
func (s *WikiCommunityService) AnalyzePostsTraderInfo(ctx context.Context, req *pb.AnalyzeSinglePostsTraderInfoRequest) (*pb.AnalyzeSinglePostsTraderInfoReply, error) {
	// 验证请求参数
	if req.GetContent() == "" {
		return nil, innErr.ErrBadRequest
	}

	// 检查upstream客户端是否可用
	if s.upstreamClient == nil {
		return nil, fmt.Errorf("交易商分析服务不可用")
	}

	// 调用大模型分析内容，获取交易商关键词
	traderData, err := s.upstreamClient.ExtractTraderInfo(ctx, req.GetContent(), "hunyuan")
	if err != nil {
		// 记录详细的错误信息用于调试
		fmt.Printf("调用交易商分析API失败: %v, 请求内容: %s\n", err, req.GetContent())
		return nil, fmt.Errorf("分析交易商信息失败: %w", err)
	}

	// 记录API返回的数据用于调试
	fmt.Printf("交易商分析API返回数据: %+v\n", traderData)

	// 构造返回结果
	// 注意：这里需要根据实际的API响应结构来解析数据
	// 暂时使用第一个识别到的交易商信息，实际使用时可能需要调整
	var traderInfo *pb.TraderInfoItem

	// 从API响应中提取交易商信息
	if traderData != nil {
		// 优先尝试从traders字段提取
		if traders, ok := traderData["traders"]; ok {
			// 处理[]interface{}类型的traders
			if tradersList, ok := traders.([]interface{}); ok && len(tradersList) > 0 {
				// 取第一个交易商作为主要结果
				if traderName, ok := tradersList[0].(string); ok && traderName != "" {
					// 清理交易商名称中的引号和转义字符
					cleanName := cleanTraderName(traderName)
					traderInfo = &pb.TraderInfoItem{
						Code: "", // TODO: 需要通过交易商名称查询获取实际的code
						Name: cleanName,
						Logo: "", // TODO: 需要通过交易商名称查询获取实际的logo
					}
				}
			} else if tradersSlice, ok := traders.([]string); ok && len(tradersSlice) > 0 {
				// 处理[]string类型的traders
				cleanName := cleanTraderName(tradersSlice[0])
				traderInfo = &pb.TraderInfoItem{
					Code: "",
					Name: cleanName,
					Logo: "",
				}
			}
		}
		
		// 如果从traders字段没有提取到，尝试从raw_response提取
		if traderInfo == nil {
			if rawResponse, ok := traderData["raw_response"]; ok {
				if responseStr, ok := rawResponse.(string); ok && responseStr != "" {
					// 从原始响应中尝试提取交易商名称
					// 这里可以根据实际API响应格式进行更复杂的解析
					cleanName := cleanTraderName(responseStr)
					traderInfo = &pb.TraderInfoItem{
						Code: "",
						Name: cleanName, // 清理后的响应作为交易商名称
						Logo: "",
					}
				}
			}
		}
	}

	// 如果识别到了交易商名称，通过fx企业搜索接口获取详细信息
	if traderInfo != nil && traderInfo.Name != "" {
		// 调用fx企业搜索接口获取交易商详细信息
		searchReq := &upstream.FxEnterpriseSearchRequest{
			Keyword: traderInfo.Name,
			Type:    2, // 2表示搜索交易商
		}

		searchResp, err := s.upstreamClient.FxEnterpriseSearch(ctx, searchReq)
		if err != nil {
			// 记录错误但不影响主流程，使用已有的交易商名称
			fmt.Printf("调用fx企业搜索API失败: %v, 关键词: %s\n", err, traderInfo.Name)
		} else if searchResp != nil && len(searchResp.Result) > 0 {
			// 使用搜索结果中的第一个匹配项更新交易商信息
			firstResult := searchResp.Result[0]
			traderInfo.Code = firstResult.Code
			traderInfo.Logo = firstResult.Logo
			// 如果搜索结果有更准确的名称，可以选择更新
			if firstResult.ShowName != "" {
				traderInfo.Name = firstResult.ShowName
			}

			// 记录成功获取的信息用于调试
			fmt.Printf("成功获取交易商详细信息: Code=%s, Name=%s, Logo=%s\n",
				traderInfo.Code, traderInfo.Name, traderInfo.Logo)
		}
	}

	// 如果没有识别到交易商信息，返回空结果
	if traderInfo == nil {
		traderInfo = &pb.TraderInfoItem{
			Code: "",
			Name: "",
			Logo: "",
		}
	}

	return &pb.AnalyzeSinglePostsTraderInfoReply{
		TraderInfo: traderInfo,
	}, nil
}

// cleanTraderName 清理交易商名称中的引号和转义字符
func cleanTraderName(name string) string {
	// 去除首尾空格
	cleaned := strings.TrimSpace(name)
	
	// 移除双引号
	cleaned = strings.Trim(cleaned, `"`)
	
	// 移除转义的双引号
	cleaned = strings.ReplaceAll(cleaned, `\"`, ``)
	
	// 移除其他可能的转义字符
	cleaned = strings.ReplaceAll(cleaned, `\\`, ``)
	
	return cleaned
}
