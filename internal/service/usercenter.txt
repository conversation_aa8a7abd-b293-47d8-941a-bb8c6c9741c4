package service

import (
	pb "api-usercenter/api/community/v1"
	"api-usercenter/internal/dao"
	"api-usercenter/internal/models"
	utilstool "api-usercenter/internal/utils"
	"context"
	"encoding/json"
	"fmt"
	"github.com/go-kratos/kratos/v2/log"
	"reflect"
	"sort"
	"strings"
	"time"
)

// EnterpriseInfo 企业信息
type EnterpriseInfo struct {
	EnterpriseCode      string
	EnterpriseType      string
	LocalShortName      string
	EnterpriseName      string
	EnterpriseShortName string
	Logo                string
	Ico                 string
	IconV               string
}

// ServiceProviderView 服务商信息
type ProviderView struct {
	LanguageCode        string
	Ico                 string
	ServiceProviderCode string
	ShortName           string
	Logo                string
}

type TraderLogView struct {
	LanguageCode string
	Ico          string
	Logo         string
}

type TraderNameView struct {
	LanguageCode string
	TraderCode   string
	ShortName    string
}

type UserCenterService struct {
	pb.UnimplementedUserCenterServer
	user *dao.User
}

func NewUserCenterService(user *dao.User) *UserCenterService {
	return &UserCenterService{
		user: user,
	}
}

type Stu struct {
	Id   int    //编号
	Name string //姓名
	Age  int    //年龄
}

func SelectEnterpriseCode(list []*models.UserData, fnc func(*models.UserData) bool) []string {
	var result []string
	for _, ptr := range list {
		if fnc(ptr) {
			result = append(result, ptr.EnterpriseCode)
		}
	}
	return result
}
func SelectT[T comparable, K any](s []*T, fnc func(*T) bool, fnc2 func(*T) K) []K {
	var result []K
	for _, ptr := range s {
		if fnc(ptr) {
			result = append(result, fnc2(ptr))
		}
	}
	return result
}

func FirstT[T comparable](s []*T, fnc func(*T) bool) *T {
	for _, ptr := range s {
		if fnc(ptr) {
			return ptr
		}
	}
	return nil
}
func Exist[T comparable](s []*T, item *T) bool {
	for _, ptr := range s {
		if ptr == item {
			return true
		}
	}
	return false
}

// 降序
func OrderByIntDesc[T comparable](s []*T, fnc func(*T) int32) []*T {
	sort.Slice(s, func(i, j int) bool {
		return fnc(s[i]) > fnc(s[j])
	})
	return s
}
func OrderByInt[T comparable](s []*T, fnc func(*T) int32) []*T {
	sort.Slice(s, func(i, j int) bool {
		return fnc(s[i]) < fnc(s[j])
	})
	return s
}

//func OrderByString[T comparable](s []*T, fnc func(*T) string) []*T {
//	 sort.Strings()
//	return s
//}

func CreateEnterprise(providerBasicColumn string, traderLogoColumn string, traderNameColumn string, languageCode string) EnterpriseInfo {
	var result = EnterpriseInfo{}
	//服务商
	languageCode = strings.ToLower(languageCode)
	if len(providerBasicColumn) > 0 && providerBasicColumn != "[]" {
		var provider []*ProviderView
		err := json.Unmarshal([]byte(providerBasicColumn), &provider)
		if err == nil {
			//当前语言
			currentlanguage := FirstT(provider, func(m *ProviderView) bool { return strings.ToLower(m.LanguageCode) == languageCode })
			if currentlanguage == nil {
				enlanguage := FirstT(provider, func(m *ProviderView) bool { return strings.ToLower(m.LanguageCode) == "en" })
				if enlanguage != nil {
					result.Logo = utilstool.EnterpirseLogoFullPath(enlanguage.Logo)
					result.Ico = utilstool.EnterpirseLogoFullPath(enlanguage.Ico)
					result.EnterpriseName = enlanguage.ShortName
					result.EnterpriseCode = enlanguage.ServiceProviderCode
					result.EnterpriseShortName = enlanguage.ShortName
					result.LocalShortName = enlanguage.ShortName
				}
				alllanguage := FirstT(provider, func(m *ProviderView) bool { return strings.ToLower(m.LanguageCode) == "all" })
				if alllanguage != nil {
					if len(alllanguage.Logo) > 0 {
						result.Logo = utilstool.EnterpirseLogoFullPath(alllanguage.Logo)
					}
					if len(alllanguage.Ico) > 0 {
						result.Ico = utilstool.EnterpirseLogoFullPath(alllanguage.Ico)
					}
					result.LocalShortName = alllanguage.ShortName
					result.EnterpriseShortName = alllanguage.ShortName
					result.EnterpriseName = alllanguage.ShortName
					result.EnterpriseCode = alllanguage.ServiceProviderCode
				}
			} else {
				result.EnterpriseShortName = currentlanguage.ShortName
				result.EnterpriseName = currentlanguage.ShortName
				result.LocalShortName = currentlanguage.ShortName
				languageen := FirstT(provider, func(m *ProviderView) bool { return strings.ToLower(m.LanguageCode) == "en" })
				if languageen != nil {
					if languageen.ShortName != currentlanguage.ShortName {
						result.EnterpriseName = languageen.ShortName + "" + currentlanguage.ShortName
					}
					result.Logo = utilstool.EnterpirseLogoFullPath(languageen.Logo)
					result.Ico = utilstool.EnterpirseLogoFullPath(languageen.Ico)
				}
				if len(currentlanguage.Logo) > 0 {
					result.Logo = utilstool.EnterpirseLogoFullPath(currentlanguage.Logo)
				}
				if len(currentlanguage.Ico) > 0 {
					result.Ico = utilstool.EnterpirseLogoFullPath(currentlanguage.Ico)
				}
			}
		}
	} else {
		//交易商
		if len(traderLogoColumn) > 0 && traderLogoColumn != "[]" {
			var trader []*TraderLogView
			err := json.Unmarshal([]byte(traderLogoColumn), &trader)
			if err == nil {
				currentlanguagetrader := FirstT(trader, func(m *TraderLogView) bool { return strings.ToLower(m.LanguageCode) == languageCode })
				if currentlanguagetrader == nil {
					enlanguagetrader := FirstT(trader, func(m *TraderLogView) bool { return strings.ToLower(m.LanguageCode) == "en" })
					if enlanguagetrader != nil {
						result.Logo = utilstool.EnterpirseLogoFullPath(enlanguagetrader.Logo)
						result.Ico = utilstool.EnterpirseLogoFullPath(enlanguagetrader.Ico)
					}
				} else {
					enlanguagetrader := FirstT(trader, func(m *TraderLogView) bool { return strings.ToLower(m.LanguageCode) == "en" })
					if enlanguagetrader != nil {
						result.Logo = utilstool.EnterpirseLogoFullPath(enlanguagetrader.Logo)
						result.Ico = utilstool.EnterpirseLogoFullPath(enlanguagetrader.Ico)
					}
					if len(currentlanguagetrader.Logo) > 0 {
						result.Logo = utilstool.EnterpirseLogoFullPath(currentlanguagetrader.Logo)
					}
					if len(currentlanguagetrader.Ico) > 0 {
						result.IconV = utilstool.EnterpirseLogoFullPath(currentlanguagetrader.Logo)
					}
				}
			}
			if len(traderNameColumn) > 0 {
				var tradernames []*TraderNameView
				err := json.Unmarshal([]byte(traderNameColumn), &tradernames)
				if err == nil {
					tradernamecurrent := FirstT(tradernames, func(m *TraderNameView) bool { return strings.ToLower(m.LanguageCode) == languageCode })
					if tradernamecurrent == nil {
						tradernamecurrenten := FirstT(tradernames, func(m *TraderNameView) bool { return strings.ToLower(m.LanguageCode) == "en" })
						if tradernamecurrenten != nil {
							result.EnterpriseName = tradernamecurrenten.ShortName
							result.EnterpriseShortName = tradernamecurrenten.ShortName
							result.LocalShortName = tradernamecurrenten.ShortName
						}
					} else {
						result.EnterpriseName = tradernamecurrent.ShortName
						result.EnterpriseShortName = tradernamecurrent.ShortName
						result.LocalShortName = tradernamecurrent.ShortName
						tradernamecurrenten := FirstT(tradernames, func(m *TraderNameView) bool { return strings.ToLower(m.LanguageCode) == "en" })
						if tradernamecurrenten != nil {
							if tradernamecurrenten.ShortName != tradernamecurrent.ShortName {
								result.EnterpriseName = tradernamecurrenten.ShortName + "" + tradernamecurrent.ShortName
							}
						}
					}
				}
			}
		}
	}
	return result
}

func GetSingleUserDetail(userinfo *models.UserData, enterprises []*models.UserEnterpriseData, userLoginId string, languageCode string, userAttentions []*models.UserWikBitAttention, isSingleUser bool) pb.UserInfo {
	var result pb.UserInfo
	//是否是单个用户
	if isSingleUser {
		result.FollowCount = 0
		result.FansCount = 0
	}
	result.AttentionStauts = 1
	if userLoginId == userinfo.UserId {
		result.AttentionStauts = 4
	}
	result.UserId = userinfo.UserId
	result.NickName = userinfo.NickName
	result.OriginNickName = userinfo.NickName
	result.RegisterLong = utilstool.GetRegisterTimeYear(userinfo.RegistrationTime, languageCode)
	result.UserStatus = 3
	result.WikiFxNumber = userinfo.WikiFXNumber
	result.EnterpriseUserLevel = userinfo.Level
	result.RegistrationTime = userinfo.RegistrationTime.Format("2006-01-02 15:04:05")
	result.CountryCode = userinfo.CountryCode
	result.PhoneNumber = userinfo.PhoneNumber
	result.Email = userinfo.Email
	result.AreaCode = userinfo.AreaCode
	result.IdentityType = 1
	result.UserIdentityNew = 1
	result.IsAuth = false
	if len(userinfo.EnterpriseCode) > 0 {
		result.IdentityType = 2
		result.EnterpriseType = userinfo.EnterpriseType
		result.EnterpriseUserLevel = userinfo.Level
	}
	if len(userAttentions) > 0 {
		attention := FirstT(userAttentions, func(m *models.UserWikBitAttention) bool { return m.AttentionedUserId == userinfo.UserId })
		if attention != nil {
			result.IsFollow = true
			if attention.IsBothAtten == 1 {
				result.AttentionStauts = 3
			} else {
				result.AttentionStauts = 2
			}
		}
	}
	if len(userinfo.AvatarAddress) == 0 || strings.Contains(userinfo.AvatarAddress, "0000000000") || strings.Contains(userinfo.AvatarAddress, "Init/900") {
		result.AvatarAddress = utilstool.EstablishFullPath(utilstool.PERSONPH)
	} else {
		result.AvatarAddress = utilstool.UserAvatarAddressFullPath(result.AvatarAddress)
	}
	result.OriginAvatarAddress = result.AvatarAddress
	result.EnterpriseCode = userinfo.EnterpriseCode
	var positonname string
	if len(userinfo.EnterpriseCode) > 0 {
		if userinfo.Level == 2 {
			result.UserStatus = 1
		} else {
			result.UserStatus = 2
		}
		enterprisesingle := FirstT(enterprises, func(m *models.UserEnterpriseData) bool { return m.EnterpriseCode == userinfo.EnterpriseCode })
		if enterprisesingle != nil {

			enterprise := CreateEnterprise(enterprisesingle.ProviderBasicColumn, enterprisesingle.TraderLogoColumn, enterprisesingle.TraderNameColumn, languageCode)
			result.EnterpriseIco = enterprise.Ico
			result.EnterpriseLogo = enterprise.Logo
			result.EnterpriseName = strings.Trim(enterprise.EnterpriseName, " ")
			if len(userinfo.PositionCode) > 0 {
				positonname = "职位"
			}
			result.Position = positonname
			result.StaffTag = enterprise.LocalShortName + "·" + positonname
			if enterprisesingle.IsAuthenticated == 1 {
				result.IsAuth = true
			}
			if len(enterprise.Ico) > 0 {
				result.AvatarAddress = enterprise.Ico
			} else {
				result.AvatarAddress = utilstool.EstablishFullPath(utilstool.ENTERPRISEPH)
				result.EnterpriseIco = utilstool.EstablishFullPath(utilstool.ENTERPRISEPH)
			}
			//服务商
			if enterprisesingle.EnterpriseType == 1 {
				result.TagWords = "服务商"
				result.DetailBgImg = utilstool.EstablishFullPath(utilstool.ENTERPRISEDETAILBG)

				if enterprisesingle.IsAuthenticated == 1 {
					result.TagIcon = utilstool.EstablishFullPath(utilstool.ENTERPRISETAGICON)
					result.EnterpriseVIcon = utilstool.EstablishFullPath(utilstool.ENTERPRISEICON)
					result.EnterpriseVIcon2 = utilstool.EstablishFullPath(utilstool.AUTHSERVICEICON)
				} else {
					result.TagIcon = utilstool.EstablishFullPath(utilstool.GRAYTAGICON)
					result.EnterpriseVIcon = utilstool.EstablishFullPath(utilstool.GRAYICON)
					result.EnterpriseVIcon2 = utilstool.EstablishFullPath(utilstool.NOAUTHVICON)
				}
			} else {
				//交易商
				result.TagWords = "交易商"
				result.TagIcon = utilstool.EstablishFullPath(utilstool.TRADERTAGICON)
				result.EnterpriseVIcon = utilstool.EstablishFullPath(utilstool.TRADERICON)
				result.EnterpriseVIcon2 = utilstool.EstablishFullPath(utilstool.AUTHTRADERVICON)
				result.IsAuth = true
			}
		}
	}
	return result
}

func (s *UserCenterService) GetUsersInfo(ctx context.Context, req *pb.GetUsersRequest) (*pb.GetUsersReply, error) {
	//获取多个用户信息
	l := log.Context(ctx)
	defer func(start time.Time) {
		l.Infof("text")
	}(time.Now())
	var result []*pb.UserInfo
	userinfos, err := s.user.GetUserDataByUserIds(ctx, req.UserIds)
	if err != nil {
		return nil, err
	}
	if len(userinfos) == 0 {
		return &pb.GetUsersReply{}, nil
	}
	var enterpriseCodes = SelectT(userinfos, func(m *models.UserData) bool { return len(m.UserId) > 0 }, func(m *models.UserData) string { return m.UserId })
	fmt.Print(enterpriseCodes)

	var ss int32 = 0
	fmt.Print(reflect.TypeOf(ss))
	print(FirstT(userinfos, func(m *models.UserData) bool { return len(m.UserId) > 0 }).UserId)
	print(OrderByInt(userinfos, func(m *models.UserData) int32 { return m.Level })[0].UserId)
	//获取企业信息
	enterpriseInfos, err := s.user.GetEnterpriseInfobyIds(ctx, enterpriseCodes)
	if err != nil {
		return nil, err
	}
	//获取用户关注信息
	var userloginIdAttentionuser []*models.UserWikBitAttention
	if len(req.UserLoginId) == 10 {
		userloginIdAttentionuser, err = s.user.GetUserLoginAttentionUserIds(ctx, req.UserLoginId, req.UserIds)
	}
	for _, value := range userinfos {
		singleuser := GetSingleUserDetail(value, enterpriseInfos, req.UserLoginId, req.LanguageCode, userloginIdAttentionuser, len(req.UserIds) == 1)
		result = append(result, &singleuser)
	}
	return &pb.GetUsersReply{
		Message: result,
	}, nil
}
