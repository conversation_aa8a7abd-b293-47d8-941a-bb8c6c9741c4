package service

import (
	"encoding/json"
	"fmt"
	"os"
)

type PostsDetailReplyConfig struct {
	Name   string
	Code   string
	Status int32
}

var DetailReplyConfigs []*PostsDetailReplyConfig

func init() {
	DetailReplyConfigs, _ = readJson("./data/officialpostsreplystatus.json")
}

func readJson(filename string) ([]*PostsDetailReplyConfig, error) {
	file, err := os.Open(filename)
	if err != nil {
		fmt.Println("Error opening JSON file:", err)
		return nil, err
	}
	defer file.Close()
	// 读取文件内容到字节数组
	byteValue, _ := os.ReadFile(filename)
	// 解析JSON到map
	if err := json.Unmarshal(byteValue, &DetailReplyConfigs); err != nil {
		fmt.Println("Error decoding JSON:", err)
	}
	return DetailReplyConfigs, nil

}
