package service

import (
	"api-community/internal/models"
	"math"
	"sort"
	"time"
)

func SelectEnterpriseCode(list []*models.UserData, fnc func(*models.UserData) bool) []string {
	var result []string
	for _, ptr := range list {
		if fnc(ptr) {
			result = append(result, ptr.EnterpriseCode)
		}
	}
	return result
}
func SelectT[T comparable, K any](s []*T, fnc func(*T) bool, fnc2 func(*T) K) []K {
	var result []K
	for _, ptr := range s {
		if fnc(ptr) {
			result = append(result, fnc2(ptr))
		}
	}
	return result
}
func SelectTNoWhere[T comparable, K any](s []*T, fnc2 func(*T) K) []K {
	var result []K
	for _, ptr := range s {
		result = append(result, fnc2(ptr))

	}
	return result
}

func FirstT[T comparable](s []*T, fnc func(*T) bool) *T {
	for _, ptr := range s {
		if fnc(ptr) {
			return ptr
		}
	}
	return nil
}
func FirstTNoPointer[T comparable](s []T, fnc func(T) bool) *T {
	for _, ptr := range s {
		if fnc(ptr) {
			return &ptr
		}
	}
	return nil
}

func Where[T comparable](s []*T, fnc func(*T) bool) []*T {
	var result []*T
	for _, ptr := range s {
		if fnc(ptr) {
			result = append(result, ptr)
		}

	}
	if len(result) > 0 {
		return result
	} else {
		return nil
	}
}
func Exist[T any](s []*T, item *T) bool {
	for _, ptr := range s {
		if ptr == item {
			return true
		}
	}
	return false
}
func ExistString(s []string, item string) bool {
	for _, ptr := range s {
		if ptr == item {
			return true
		}
	}
	return false
}

// 降序
func OrderByIntDesc[T comparable](s []*T, fnc func(*T) int32) []*T {
	sort.Slice(s, func(i, j int) bool {
		return fnc(s[i]) > fnc(s[j])
	})
	return s
}
func OrderByInt[T comparable](s []*T, fnc func(*T) int32) []*T {
	sort.Slice(s, func(i, j int) bool {
		return fnc(s[i]) < fnc(s[j])
	})
	return s
}
func Intersection[T comparable](arr1, arr2 []T) []T {
	m := make(map[T]bool)
	var result []T

	// 将第一个数组的元素添加到映射中
	for _, num := range arr1 {
		m[num] = true
	}

	// 检查第二个数组的元素是否在映射中
	for _, num := range arr2 {
		if m[num] {
			result = append(result, num)
			m[num] = false // 确保结果中不包含重复元素
		}
	}

	return result
}

// CalculateViewCount 计算查看数量
// 24小时取整*156
// 如果时差小于12小时，则增长值为(1+X)*X/2   如果时差大于12小时，则增长值为78+(1+(X−12))*(X−12)/2
// 最终数值=(2结果+3结果)*n；n建议为标题字符数*0.01
func CalculateViewCount(datetime time.Time, title string) int {
	var daycount = 156
	var number1 = 0
	var number2 = 0
	now := time.Now().Sub(datetime).Hours()
	if now >= 5*24 {
		now = 5 * 24
	}
	if now < 24 {
		if now == 0 {
			return 0
		} else {
			number := int(now)
			if now < 12 {
				number1 = lessThan12(number)
			} else {
				number2 = greaterThan12(number)
			}
		}
	} else {
		number := int(math.Floor(now / float64(24)))
		number1 = daycount * number
		mod := number % 24
		if mod != 0 {
			if mod < 12 {
				number2 = lessThan12(mod)
			} else {
				number2 = greaterThan12(mod)
			}
		}

	}

	return int((float64(number1) + float64(number2)) * (0.01 * float64(len(title))))

}

func lessThan12(number int) int {
	return (1 + number) * number / 2
}
func greaterThan12(number int) int {
	return 78 + (1+(number-12))*(number-12)/2
}

func PtrToString(s []*string) []string {
	var result []string
	for _, v := range s {
		result = append(result, *v)
	}
	return result

}
