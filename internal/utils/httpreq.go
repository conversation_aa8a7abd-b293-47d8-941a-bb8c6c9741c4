package utilstool

import (
	"errors"
	"fmt"
	"github.com/imroc/req/v3"
	"time"
)

func HttpGet(url string, headers map[string]string) (interface{}, error) {
	client := req.C().SetTimeout(5 * time.Second).DevMode()
	resp, err := client.R().
		SetHeaders(headers).
		Get(url)
	if err != nil {
		panic(err)
		return nil, err
	}
	return resp.SuccessResult(), nil

}

func HttpPost(url string, headers map[string]string, body interface{}) (interface{}, error) {
	client := req.C().SetTimeout(5 * time.Second) //.DevMode()
	resp, err := client.R().SetBody(body).SetHeaders(headers).
		SetContentType("application/json").
		Post(url)
	if err != nil {
		//panic(err)
		return nil, err
	}
	return resp.SuccessResult(), err
}

type RemoteResultModel[T any] struct {
	Result T
}

func HttpPostT[T any](url string, headers map[string]string, body interface{}) (T, error) {
	var result RemoteResultModel[T]
	client := req.C().SetTimeout(5 * time.Second) //.DevMode()
	resp, err := client.R().SetBody(body).SetHeaders(headers).
		SetContentType("application/json").
		SetSuccessResult(&result).
		Post(url)
	if err != nil {
		//fmt.Println("远程调用失败" + url + err.Error())
		return result.Result, err
	}
	//fmt.Println("远程调用" + url + resp.Dump())
	if resp.IsSuccessState() {
		return result.Result, nil
	}
	if err == nil {
		err = errors.New("请求失败")
	}
	return result.Result, err

}

func HttpGetT[T any](url string, headers map[string]string, body interface{}) (T, error) {
	var result RemoteResultModel[T]
	client := req.C().SetTimeout(5 * time.Second) //.DevMode()
	resp, err := client.R().SetBody(body).SetHeaders(headers).
		SetSuccessResult(&result).
		Get(url)
	if err != nil {
		fmt.Println("远程调用失败" + url + err.Error())
		return result.Result, err
	}
	fmt.Println("远程调用" + url + resp.Dump())
	if resp.IsSuccessState() {
		return result.Result, nil
	}
	if err == nil {
		err = errors.New("请求失败")
	}
	return result.Result, err

}
func HttpGetListT[T any](url string, headers map[string]string) ([]T, error) {
	var result RemoteResultModel[[]T]
	client := req.C().SetTimeout(5 * time.Second) //.DevMode()
	resp, err := client.R().SetHeaders(headers).
		SetSuccessResult(&result).
		Get(url)

	if err != nil {
		//fmt.Println("远程调用失败" + url + err.Error())
		return result.Result, err
	}
	//fmt.Println("远程调用" + url)
	if resp.IsSuccessState() {
		return result.Result, nil
	}
	return result.Result, err

}
