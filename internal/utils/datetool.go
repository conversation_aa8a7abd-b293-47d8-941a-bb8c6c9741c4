package utilstool

import (
	"fmt"
	"time"
)

func GetRegisterTimeYear(registertime time.Time, languageCode string) string {
	var diff = time.Now().Sub(registertime).Hours() / 24
	if diff < 365 {
		return "1年内"
	} else if diff >= 365 && diff < 365*2 {
		return fmt.Sprintf("{%d}-{%d}年", 1, 2)
	} else if diff >= 365*2 && diff < 365*5 {
		return fmt.Sprintf("{%d}-{%d}年", 3, 5)
	} else if diff >= 365*5 && diff < 365*10 {
		return fmt.Sprintf("{%d}-{%d}年", 6, 10)
	} else if diff >= 365*10 && diff < 365*15 {
		return fmt.Sprintf("{%d}-{%d}年", 11, 15)
	} else if diff >= 365*15 && diff < 365*20 {
		return fmt.Sprintf("{%d}-{%d}年", 16, 20)
	} else {
		return "20年以上"

	}

}
