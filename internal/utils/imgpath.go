package utilstool

import (
	"fmt"
	"regexp"
	"strings"
)

const (
	LogoDomain         = "https://eimgjys.fx696.com/"
	DefaultDomain      = "https://img.fx696.com/"
	IconDomain         = "https://icoimg.fx696.com/"
	GlobalTemplate     = "_wiki-template-global"
	AttachmentTemplate = "_wikilogo"
	PERSONPH           = "WikiEnterprise/sign/personph.png"
	TEMPLATEEMPLATE    = "_wiki200"
	ENTERPRISEDETAILBG = "WikiEnterprise/sign/enterprisedetailbg.png"
	BCBGENTERPRISE     = "WikiEnterprise/sign/bcbgenterprise.png"
	ENTERPRISEPH       = "WikiEnterprise/sign/enterpirseph.png"
	ENTERPRISETAGICON  = "WikiEnterprise/sign/enterprisetagicon.png"
	ENTERPRISEBGCOLOR  = "#FFF7EE"
	ENTERPRISECOLOR    = "#FB8E16"
	ENTERPRISEICON     = "WikiEnterprise/sign/enterrpiseicon.png"
	AUTHSERVICEICON    = "WikiEnterprise/identity/service.png"
	GRAYTAGICON        = "WikiEnterprise/sign/graytagicon.png"
	NOAUTHVICON        = "WikiEnterprise/identity/noauth.png"
	GRAYICON           = "WikiEnterprise/sign/grayicon.png"
	TRADERTAGICON      = "WikiEnterprise/sign/tradertagicon.png"
	TRADERICON         = "WikiEnterprise/sign/tradericon.png"
	AUTHTRADERVICON    = "WikiEnterprise/identity/trader.png"
	MARKETURL          = "https://eimgmarket.zy223.com"
	BARURL             = "https://h8imgs.zy223.com"
	IMGWIDTH           = 3000
	IMGHEIGHT          = 4000
	LEMONXURL          = "https://h8imgs.tech010.com"
)

func EnterpirseLogoFullPath(path string) string {
	domain := LogoDomain
	if len(path) == 0 {
		return path
	}
	if strings.Contains(path, "ico") {
		domain = IconDomain
	}
	return fmt.Sprintf("%s%s%s", domain, strings.TrimLeft(path, "/"), GlobalTemplate)
}

func EstablishFullPath(path string) string {
	domain := DefaultDomain
	if len(path) == 0 {
		return path
	}
	return fmt.Sprintf("%s%s%s", domain, strings.TrimLeft(path, "/"), GlobalTemplate)

}
func UserAvatarAddressFullPath(path string) string {
	domain := DefaultDomain
	if len(path) == 0 {
		return path
	}
	return fmt.Sprintf("%s%s%s", domain, strings.TrimLeft(path, "/"), TEMPLATEEMPLATE)
}
func GetPath(path string, appType string) (list string, detail string) {
	if len(path) == 0 {
		return "", ""
	}
	path = strings.ReplaceAll(path, "_png", "")
	path = GetRelativeImageUrl(path)
	var url = ""
	if strings.Index(path, "market") >= 0 {
		url = MARKETURL
	} else {
		if appType == "600" {
			url = LEMONXURL
		} else {
			url = BARURL
		}
	}
	list = fmt.Sprintf("%s%s%s", url, path, "-list600")
	detail = fmt.Sprintf("%s%s%s", url, path, "-list600")
	return list, detail
}
func GetSinglePath(path string, appType string) string {
	if len(path) == 0 {
		return ""
	}
	path = strings.ReplaceAll(path, "_png", "")
	path = GetRelativeImageUrl(path)
	var url = ""
	if strings.Index(path, "market") >= 0 {
		url = MARKETURL
	} else {
		if appType == "600" {
			url = LEMONXURL
		} else {
			url = BARURL
		}
	}
	return fmt.Sprintf("%s%s%s", url, path, "-list600")

}
func GetImageFullPath(path string, appType string) string {
	if len(path) == 0 {
		return ""
	}
	path = strings.ReplaceAll(path, "_png", "")
	path = GetRelativeImageUrl(path)
	var url = ""
	if strings.Index(path, "market") >= 0 {
		url = MARKETURL
	} else {
		if appType == "600" {
			url = LEMONXURL
		} else {
			url = BARURL
		}
	}
	var detail = ""
	if strings.Contains(path, "-detail") {
		detail = fmt.Sprintf("%s%s", url, path)

	} else {
		detail = fmt.Sprintf("%s%s%s", url, path, "-detail")

	}
	return detail
}

func GetRelativeImageUrl(path string) string {
	var domain = ""
	var p = `(http|https)://(?<domain>[^(:|/]*)`
	m, err := regexp.Compile(p)
	if err == nil {
		domain = m.FindString(path)
	}
	if domain == "" {
		path = strings.TrimLeft(path, "/")
		return "/" + path
	}
	httpurl := strings.ReplaceAll(path, "_wiki-template-global", "")
	var url = SubStringLength(httpurl, len(domain), len(httpurl)-len(domain))
	url = strings.TrimLeft(url, "/")
	return "/" + url
}
