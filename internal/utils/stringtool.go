package utilstool

import (
	"fmt"
	"html"
	"math"
	"math/rand"
	"strconv"
	"strings"
	"time"
)

func SubStringLength(source string, start int, length int) string {
	var r = []rune(source)
	//if start < 0 || end > length || start > end {
	//	return ""
	//}
	//
	//if start == 0 && end == length {
	//	return source
	//}

	return string(r[start : start+length])

}
func SubString(source string, start int, end int) string {
	var r = []rune(source)
	length := len(r)

	if start < 0 || end > length || start > end {
		return ""
	}

	if start == 0 && end == length {
		return source
	}

	return string(r[start:end])

}
func MyHtmlDecode(content string, isReplace bool) string {
	if isReplace {
		content = strings.ReplaceAll(content, "\r\n", "")
		content = strings.ReplaceAll(content, "\n", "")
		return html.UnescapeString(content)
	} else {
		return html.UnescapeString(content)
	}
}

func ShowNumber(number int32) string {
	if number < 1000 {
		return strconv.Itoa(int(number))
	}
	if number > 999 && number < 1000000 {
		result := math.Floor(float64(number*10)/1000) / float64(10)
		fmt.Println(result)
		return strconv.FormatFloat(result, 'f', -1, 64) + "K"
	} else {
		result := math.Floor(float64(number*10)/1000000) / 10
		return strconv.FormatFloat(result, 'f', -1, 64) + "M"
	}
}
func TimeNowStr() string {
	now := time.Now().UTC()
	return now.Format("20060102150405")
}
func GetNumber(perfix string) string {
	rand.NewSource(time.Now().UnixNano())
	randomNumber := rand.Intn(9000000) + 1000000
	return perfix + TimeNowStr() + strconv.Itoa(randomNumber)
}
func PointerToString(arrs []*string) []string {
	var result []string
	for _, v := range arrs {
		if v != nil {
			result = append(result, *v)
		}
	}
	return result
}

func Random(min, max int) int {
	rand.NewSource(time.Now().UnixNano())
	randomNumber := rand.Intn(max-min+1) + min
	return randomNumber
}
