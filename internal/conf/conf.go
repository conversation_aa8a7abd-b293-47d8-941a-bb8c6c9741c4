package conf

import (
	common "api-community/api/common"
)

type Bootstrap struct {
	Server   *common.ServerConfig `json:"server"`
	Data     *common.DataConfig   `json:"data"`
	Business *Business            `json:"business"`
}

type RemoteUrl struct {
	DatahubApi string `json:"datahubapi"`
}
type GrpcUrl struct {
	Usercenter string `json:"usercenter"`
}
type Business struct {
	RemoteUrl RemoteUrl `json:"remote_url"`
	GrpcUrl   GrpcUrl   `json:"grpc_url"`
}
