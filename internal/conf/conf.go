package conf

import (
	common "api-community/api/common"
)

type Bootstrap struct {
	Server   *common.ServerConfig `json:"server"`
	Data     *common.DataConfig   `json:"data"`
	Business *Business            `json:"business"`
}

type RemoteUrl struct {
	DatahubApi    string `json:"datahubapi"`
	Address2GeoApi string `json:"address2geoapi"` // 新增：交易商分析API地址
}
type GrpcUrl struct {
	Usercenter string `json:"usercenter"`
}
type Business struct {
	RemoteUrl RemoteUrl `json:"remote_url"`
	GrpcUrl   GrpcUrl   `json:"grpc_url"`
}
