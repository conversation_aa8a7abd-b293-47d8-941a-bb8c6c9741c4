package server

import (
	"api-community/api/common"
	v1 "api-community/api/community/v1"
	"api-community/internal/service"
	"api-community/internal/tool/env"
	"api-community/internal/tool/ilog"
	mmd "api-community/internal/tool/metadata"
	"api-community/internal/tool/metrics"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/middleware/recovery"
	"github.com/go-kratos/kratos/v2/middleware/tracing"
	"github.com/go-kratos/kratos/v2/transport/grpc"
	"time"
)

func NewGRPCServer(c *common.ServerConfig, svc *service.WikiCommunityService, logger log.Logger) *grpc.Server {
	var opts = []grpc.ServerOption{
		grpc.Middleware(
			mmd.Server(),
			tracing.Server(),
			recovery.Recovery(),
			ilog.LoggingGRPC(),
			metrics.ServerMetricsMiddleware(env.GetServiceName()),
		),
	}
	if c.Grpc.Network != "" {
		opts = append(opts, grpc.Network(c.Grpc.Network))
	}
	if c.Grpc.Addr != "" {
		opts = append(opts, grpc.Address(c.Grpc.Addr))
	}
	if c.Grpc.TimeoutSeconds > 0 {
		opts = append(opts, grpc.Timeout(time.Duration(c.Grpc.TimeoutSeconds)*time.Second))
	}
	srv := grpc.NewServer(opts...)

	v1.RegisterWikiCommunityServer(srv, svc)
	return srv
}
