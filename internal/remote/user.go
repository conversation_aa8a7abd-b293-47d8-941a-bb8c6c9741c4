package remote

import (
	utilstool "api-community/internal/utils"
	"context"
	"strings"
	"time"

	"github.com/go-kratos/kratos/v2/metadata"
)

const (
	UserApi = "http://wikiuapi.fxeyeinterface.com/" //用户地址
)

type GetUserFansOutPut struct {
	UserId     string `json:"UserId"`
	Count      int    `json:"Count"`
	TotalCount int    `json:"TotalCount"`
}

func GetUserFansCount(ctx context.Context, userIds []string, start time.Time) (out []GetUserFansOutPut, err error) {
	body := make(map[string]any)
	body["UserIds"] = userIds
	if start.After(time.Time{}) {
		body["StartTime"] = start.UTC().Format(time.DateTime)
	}
	header := make(map[string]string)
	if md, ok := metadata.FromServerContext(ctx); ok {
		var languageCode = md.Get("languagecode")
		var countryCode = md.Get("countrycode")
		var basicData = md.Get("BasicData")
		header["LanguageCode"] = strings.ToLower(languageCode)
		header["CountryCode"] = countryCode
		header["BasicData"] = basicData
	}
	out, err = utilstool.HttpPostT[[]GetUserFansOutPut](UserApi+"user/getusersfanscountbytime", header, body)
	return
}
