package remote

import (
	utilstool "api-community/internal/utils"
	"errors"
	"strings"
)

const (
	GETREPETITIONURL = "http://datahubapi.fxeyeinterface.com"
	WIKIBITSEARCHURL = "http://wikibitsearch.fxeyeinterface.com" //搜索地址
)

type GetRepetitionRequest struct {
	Mode    int32
	PostCol []GetRepetitionRequestItem
}
type GetRepetitionRequestItem struct {
	Code    string
	Content string
	Title   string
}
type GetRepetitionResponse struct {
	RepetitionCol []*GetRepetitionResponseItem
}

type GetRepetitionResponseItem struct {
	Code           string
	Rate           float32 //相似度
	RepetitionCode string
	IncludeFlag    bool //是否包含
}

type GetCountryCodeByLanguageCodeResponse struct {
	Name        string
	CountryCode string
	TwoCharCode string
}

type Area struct {
	Code        string
	CountryCode string
	TwoCharCode string
}

// GetRepetition 计算相似度
func GetRepetition(req GetRepetitionRequest) (*GetRepetitionResponse, error) {
	res, err := utilstool.HttpPostT[*GetRepetitionResponse](GETREPETITIONURL+"/v1/repetition", map[string]string{}, &req)
	return res, err
}

// GetCountryCodeByLanguageCode 根据语言获取国家翻译
func GetCountryCodeByLanguageCode(languageCode, countryCode string) ([]GetCountryCodeByLanguageCodeResponse, error) {
	//var rediskey = "country_" + languageCode + "_" + countryCode
	res, err := utilstool.HttpGetListT[GetCountryCodeByLanguageCodeResponse](WIKIBITSEARCHURL+"/country/areaCode?languageCode="+languageCode,
		map[string]string{"CountryCode": countryCode})
	return res, err

}

func GetAreaCode(countryCode string) (string, error) {
	res, err := utilstool.HttpGetListT[Area](WIKIBITSEARCHURL+"/area", nil)
	out := ""
	for _, v := range res {
		if strings.EqualFold(v.CountryCode, countryCode) {
			out = v.Code
		}
	}
	if out == "" {
		err = errors.New("没有找到国家区域")
	}
	return out, err
}
