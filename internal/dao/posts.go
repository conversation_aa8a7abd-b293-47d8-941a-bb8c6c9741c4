package dao

import (
	"api-community/internal/models"
	"api-community/internal/tool/igorm"
	"api-community/internal/tool/ormhelper"
	utilstool "api-community/internal/utils"
	"context"
	"fmt"
	"math"
	"strconv"
	"strings"
	"time"

	pb "api-community/api/community/v1"

	"github.com/go-kratos/kratos/v2/log"
	redis "github.com/go-redis/redis/v8"
	"gorm.io/gorm"
)

type Posts struct {
	Mysql       *igorm.DB
	RedisClient *redis.Client
}

func NewUser(mysql *igorm.DB, redisClient *redis.Client) *Posts {
	return &Posts{
		Mysql:       mysql,
		RedisClient: redisClient,
	}
}

func (s *Posts) Session(ctx context.Context, opts ...igorm.Option) *gorm.DB {
	return s.Mysql.NewOptions(opts...).Session().WithContext(ctx)
}

func (s *Posts) Create(ctx context.Context, in *models.User, opts ...igorm.Option) (int64, error) {
	err := s.Session(ctx, opts...).Create(in).Error
	if err != nil {
		return 0, ormhelper.WrapErr(err)
	}

	return in.ID, nil
}

func (s *Posts) Delete(ctx context.Context, id int64, opts ...igorm.Option) error {
	err := s.Session(ctx, opts...).Model(&models.User{}).Delete("id = ?", id).Error
	if err != nil {
		return ormhelper.WrapErr(err)
	}
	return nil
}

func (s *Posts) Update(ctx context.Context, in *models.User, opts ...igorm.Option) error {
	err := s.Session(ctx, opts...).Updates(in).Error
	if err != nil {
		return ormhelper.WrapErr(err)
	}
	return nil
}

func (s *Posts) Get(ctx context.Context, id int64, opts ...igorm.Option) (*models.User, error) {
	var out *models.User

	err := s.Session(ctx, opts...).
		Where("id = ?", id).
		First(&out).Error
	if err != nil {
		return nil, ormhelper.WrapErr(err)
	}
	return out, nil
}

func (s *Posts) FindByPage(ctx context.Context, page, size int, opts ...igorm.Option) ([]*models.User, error) {
	var out []*models.User
	err := s.Session(ctx, opts...).
		Order("id desc").
		Offset((page - 1) * size).
		Limit(size).
		Find(&out).Error
	if err != nil {
		return nil, ormhelper.WrapErr(err)
	}
	return out, nil
}

func (s *Posts) FindByOffset(ctx context.Context, offset int64, size int, opts ...igorm.Option) ([]*models.User, int64, error) {
	if offset <= 0 {
		offset = math.MaxInt64
	}

	var out []*models.User
	err := s.Session(ctx, opts...).
		Where("id < ?", offset). // 从offset处开始取
		Order("id desc").        // 如果要使用offset，则必须要按照ID进行排序
		Limit(size + 1).         // 这里多取一条
		Find(&out).Error
	if err != nil {
		return nil, 0, ormhelper.WrapErr(err)
	}

	var (
		count      = len(out)
		nextOffset int64
	)

	if count > 1 && count > size {
		nextOffset = out[count-2].ID
	}

	if count > size {
		out = out[:count-1]
	}

	return out, nextOffset, nil
}

// GetPostsByIds 根据多个商业code或者Id获取多个数据
func (s *Posts) GetPostsByIds(ctx context.Context, ids []string, opts ...igorm.Option) ([]*models.Post, error) {
	var out []*models.Post
	if len(ids) == 0 {
		return nil, nil
	}
	db := s.Session(ctx, opts...)
	tx := db.Where("PostsId in ?", ids)
	err := tx.Find(&out).Error
	if err != nil {
		return nil, ormhelper.WrapErr(err)
	}
	return out, nil
}

// GetSinglePostsById 获取单个商业数据
func (s *Posts) GetSinglePostsById(ctx context.Context, id string, opts ...igorm.Option) (*models.Post, error) {
	var out *models.Post
	err := s.Session(ctx, opts...).Where("PostsId= ?", id).Find(&out).Error
	if err != nil {
		return nil, ormhelper.WrapErr(err)
	}
	return out, nil
}

// GetUserApplaudPosts 用户是否点赞
func (s *Posts) GetUserApplaudPosts(ctx context.Context, ids []string, userLoginId string, opts ...igorm.Option) ([]*models.ApplaudRecordModel, error) {
	var out []*models.ApplaudRecordModel
	err := s.Session(ctx, opts...).Raw("select PostsId from posts_applaudrecord where  PostsId in ? and UserId= ? ", ids, userLoginId).Scan(&out).Error
	if err != nil {
		return nil, ormhelper.WrapErr(err)
	}
	return out, nil
}

// GetUserCollectPosts 用户是否收藏
func (s *Posts) GetUserCollectPosts(ctx context.Context, ids []string, userLoginId string, opts ...igorm.Option) ([]*models.ApplaudRecordModel, error) {
	var out []*models.ApplaudRecordModel
	err := s.Session(ctx, opts...).Raw("select PostsId from posts_collect where  PostsId in ? and UserId= ? ", ids, userLoginId).Scan(&out).Error
	if err != nil {
		return nil, ormhelper.WrapErr(err)
	}
	return out, nil
}

// GetAttachments 获取图片数据
func (s *Posts) GetAttachments(ctx context.Context, ids []string, opts ...igorm.Option) ([]*models.Attachment, error) {
	var out []*models.Attachment
	err := s.Session(ctx, opts...).Where("RelatedId in  (?) ", ids).Find(&out).Error
	if err != nil {
		return nil, ormhelper.WrapErr(err)
	}
	return out, nil
}

// GetCommentGradesByIds 获取评价数据
func (s *Posts) GetCommentGradesByIds(ctx context.Context, ids []string, opts ...igorm.Option) ([]*models.CommentGrade, error) {
	var out []*models.CommentGrade
	if len(ids) == 0 {
		return nil, nil
	}
	db := s.Session(ctx, opts...)
	tx := db.Where("CommentGradeId in ?", ids)
	err := tx.Find(&out).Error
	if err != nil {
		return nil, ormhelper.WrapErr(err)
	}
	return out, nil
}

// 获取评价翻译
func (s *Posts) GetCommentGradesTrans(ctx context.Context, ids []string, languages []string, opts ...igorm.Option) ([]*models.CommentGradeTranslate, error) {
	var out []*models.CommentGradeTranslate
	if len(ids) == 0 {
		return nil, nil
	}
	err := s.Session(ctx, opts...).Where("CommentGradeId in ? and LanguageCode in ?", ids, languages).Find(&out).Error
	if err != nil {
		return nil, ormhelper.WrapErr(err)
	}
	return out, nil
}

// 获取动态字段
func (s *Posts) GetTemplatedynamicfields(ctx context.Context, opts ...igorm.Option) ([]*models.Templatedynamicfield, error) {
	var out []*models.Templatedynamicfield
	err := s.Session(ctx, opts...).Where(" Status=1 order by  ordernum desc").Find(&out).Error
	if err != nil {
		return nil, ormhelper.WrapErr(err)
	}
	return out, nil
}

// 动态字段的值
func (s *Posts) GetTemplatedynamicValues(ctx context.Context, opts ...igorm.Option) ([]*models.Templatedynamicvalue, error) {
	var out []*models.Templatedynamicvalue
	err := s.Session(ctx, opts...).Where(" Status=1 ").Find(&out).Error
	if err != nil {
		return nil, ormhelper.WrapErr(err)
	}
	return out, nil
}

// 商业类型
func (s *Posts) GetServiceTypelist(ctx context.Context, opts ...igorm.Option) ([]*models.ServiceProviderName, error) {
	var out []*models.ServiceProviderName
	err := s.Session(ctx, opts...).Raw("select a.ServiceTypeCode Code,b.`Name`,b.LanguageCode from serviceproviderservicetype a INNER JOIN serviceproviderservicetypelang b  on a.ServiceTypeCode=b.ServiceTypeCode where a.IsEnabled=1").Scan(&out).Error

	if err != nil {
		return nil, ormhelper.WrapErr(err)
	}
	return out, nil
}

// GetSingleUserPostsCollect 获取用户收藏
func (s *Posts) GetSingleUserPostsCollect(ctx context.Context, userId string, pageIndex int, pageSize int, releaseType int32, opts ...igorm.Option) ([]*models.PostsCollect, error) {
	var out []*models.PostsCollect
	err := s.Session(ctx, opts...).Where("userid= ?  and ReleaseType=?  ", userId, releaseType).Order(" CreateTime desc ").Limit(pageSize).Offset((pageIndex - 1) * pageSize).Find(&out).Error

	if err != nil {
		return nil, ormhelper.WrapErr(err)
	}
	return out, nil
}

// GetUserPostsPageList 获取用户帖子
func (s *Posts) GetUserPostsPageList(ctx context.Context, userLoginId string, userId string, pageIndex int32, pageSize int32, releaseType int32, isWikiFXActivity bool, languagecodes []string, isLemonx bool, tops []*models.PostsTop, opts ...igorm.Option) ([]*models.Post, error) {
	var out []*models.Post
	stwhere := "ReleaseType= ? "
	if userId == userLoginId {
		stwhere = stwhere + " and userid= ? and `Status`!=401 and  Status!=501 "
	} else {

		stwhere = stwhere + " and userid= ?   and `Status`=200 "
	}
	var postsdis []string
	if len(tops) > 0 {
		for _, v := range tops {
			postsdis = append(postsdis, v.PostsID)
		}
		stwhere = stwhere + " and postsid not in ('" + strings.Join(postsdis, "','") + "') "
	}
	if isWikiFXActivity == false {
		stwhere = stwhere + " and (AffEnterpriseCode='' or AffEnterpriseCode Is null) "
		err := s.Session(ctx, opts...).Raw(" select * from posts x where "+stwhere+" order by  PublicTime desc limit ?,?", releaseType, userId, (int(pageIndex)-1)*int(pageSize), pageSize).Scan(&out).Error
		if err != nil {
			return nil, ormhelper.WrapErr(err)
		}
	} else {
		if isLemonx {
			stwhere = stwhere + " and languagecode in ?"
			err := s.Session(ctx, opts...).Raw(" select * from posts x where "+stwhere+" order by  PublicTime desc limit ?,?", releaseType, userId, (int(pageIndex)-1)*int(pageSize), pageSize).Scan(&out).Error
			if err != nil {
				return nil, ormhelper.WrapErr(err)
			}
		} else {
			stwhere = stwhere + " and languagecode in ?"
			err := s.Session(ctx, opts...).Raw(" select * from posts x where "+stwhere+" order by  PublicTime desc limit ?,?", releaseType, userId, languagecodes, (int(pageIndex)-1)*int(pageSize), pageSize).Scan(&out).Error
			if err != nil {
				return nil, ormhelper.WrapErr(err)
			}
		}

	}
	if len(tops) > 0 && pageIndex == 1 {
		var postsTops []*models.Post
		err := s.Session(ctx, opts...).Raw(" select * from posts where postsid in ?", postsdis).Scan(&postsTops).Error
		if err != nil {
			return nil, ormhelper.WrapErr(err)
		}

		newSlice := make([]*models.Post, len(out)+len(tops))
		// 将原始切片的元素拷贝到新切片的其他位置
		index := 0
		for _, v := range tops {
			for _, m := range postsTops {
				if m.PostsID == v.PostsID {
					newSlice[index] = m
					index++
					break
				}
			}
		}
		copy(newSlice[len(tops):], out)
		return newSlice, nil
	}

	return out, nil

}

// GetPostsStatisticsByPostsIds 根据postsid获取统计数据
func (s *Posts) GetPostsStatisticsByPostsIds(ctx context.Context, postsIds []string, opts ...igorm.Option) ([]*models.PostsStatistic, error) {
	var out []*models.PostsStatistic
	err := s.Session(ctx, opts...).Where("PostsId in ? ", postsIds).Find(&out).Error
	if err != nil {
		return nil, ormhelper.WrapErr(err)
	}
	return out, nil
}

func (s *Posts) GetUserIdByPostsId(ctx context.Context, postsId string, opts ...igorm.Option) (*string, error) {
	var out []*string
	err := s.Session(ctx, opts...).Raw("select userid from posts where postsId= ? ", postsId).Scan(&out).Error
	if err != nil {
		return nil, ormhelper.WrapErr(err)
	} else {
		if len(out) > 0 {
			return out[0], nil
		} else {
			err = s.Session(ctx, opts...).Raw("select userid from comment_grade where CommentGradeId= ? ", postsId).Scan(&out).Error
			if len(out) > 0 {
				return out[0], nil
			}
		}
	}
	return nil, nil
}

type GetPostsNumberView struct {
	ReleaseType int32
	Number      int32
}

func (s *Posts) GetPersonPostsNumber(ctx context.Context, userId string, userLoginId string, isWikiActivity bool, releaseType int, languageCodes []string, isLemonx bool, opts ...igorm.Option) (*int, error) {
	var out *int
	stwhere := "ReleaseType= ? "
	if userId == userLoginId {
		stwhere = stwhere + " and userid= ? and `Status`!=401  and `Status`!=501"
	} else {
		stwhere = stwhere + " and userid= ?   and `Status`=200"
	}
	//if releaseType == 2 {
	//	stwhere = stwhere + "  and NOT EXISTS ( SELECT * FROM `whitelist` `s` WHERE (( `AreaCode` = '999' ) OR ( `AreaCode` = '" + areaCode + "' )) AND ( `x`.`RelationEnterpriseCode` = `TraderCode` ) AND ( `x`.`CommentGrade` = 3 )) "
	//}
	if isWikiActivity == false {
		stwhere = stwhere + " and (AffEnterpriseCode='' or AffEnterpriseCode Is null)"
		err := s.Session(ctx, opts...).Raw(" select count(*) from posts x where "+stwhere, releaseType, userId).Scan(&out).Error
		if err == nil {
			return out, nil
		}
	} else {
		if isLemonx {
			err := s.Session(ctx, opts...).Raw(" select count(*) from posts x where "+stwhere, releaseType, userId).Scan(&out).Error
			if err == nil {
				return out, nil
			}

		} else {
			stwhere = stwhere + " and languageCode in ?"
			err := s.Session(ctx, opts...).Raw(" select count(*) from posts x where "+stwhere, releaseType, userId, languageCodes).Scan(&out).Error
			if err == nil {
				return out, nil
			}

		}

	}

	return nil, nil
}

func (s *Posts) GetEnterprisePostsNumber(ctx context.Context, userId string, releaseType int, opts ...igorm.Option) (*int, error) {
	var out *int
	err := s.Session(ctx, opts...).Raw(" select count(*) from posts  where ReleaseType= ? and  AffEnterpriseCode=? ", releaseType, userId).Scan(&out).Error
	if err == nil {
		return out, nil
	}
	return nil, nil
}

func (s *Posts) GetOfficalPostsNumber(ctx context.Context, userIds []string, opts ...igorm.Option) (map[string]int, error) {
	var out []map[string]any
	sql := "select userid,count(1) as cnt from posts where userid in ? and `status`=200"
	err := s.Session(ctx, opts...).Raw(sql, userIds).Scan(&out).Error
	if err != nil {
		return nil, err
	}
	res := make(map[string]int, 10)
	for _, v := range out {
		u := fmt.Sprintf("%v", v["userid"])
		cnt, _ := strconv.Atoi(fmt.Sprintf("%v", v["cnt"]))
		res[u] = cnt
	}
	return res, nil
}

// 用户点赞
func (s *Posts) GetUserApplaudByPosts(ctx context.Context, postsIds []string, userLoginId string, opts ...igorm.Option) ([]*models.PostsApplaudrecord, error) {
	var out []*models.PostsApplaudrecord
	err := s.Session(ctx, opts...).Where(" UserId=? and postsId in ? ", userLoginId, postsIds).Find(&out).Error
	if err != nil {
		return nil, ormhelper.WrapErr(err)
	}
	return out, nil
}

// GetSingleUserPostsCollect 获取用户记录
func (s *Posts) GetUserViewHistoryPosts(ctx context.Context, userId string, pageIndex int, pageSize int, releaseType int32, opts ...igorm.Option) ([]*models.PostsViewHistory, error) {
	var out []*models.PostsViewHistory
	//var date = time.Now().UTC().AddDate(0, 0, -7)
	err := s.Session(ctx, opts...).Where("userid= ?  and PostsType=? ", userId, releaseType).Order(" CreateTime desc ").Limit(pageSize).Offset((pageIndex - 1) * pageSize).Find(&out).Error

	if err != nil {
		return nil, ormhelper.WrapErr(err)
	}
	return out, nil
}

// GetPostsStatisticsByPostsIds 根据postsid获取统计数据
func (s *Posts) GetPostsUserApplaud(ctx context.Context, postsIds []string, opts ...igorm.Option) ([]*models.PostsStatistic, error) {
	var out []*models.PostsStatistic
	err := s.Session(ctx, opts...).Where(" postsid in ? ", postsIds).Find(&out).Error
	if err != nil {
		return nil, ormhelper.WrapErr(err)
	}
	return out, nil
}

func (s *Posts) GetTopicRecommend(ctx context.Context,
	language, area string) (out []*models.PostsTopic, err error) {
	err = s.Mysql.Session().WithContext(ctx).
		Table("posts_topic_recommend pr ").
		Select([]string{"tp.TopicID", "tp.`Content`", "tp.ViewCountSettings"}).
		Joins(" join posts_topic tp on tp.TopicId=pr.TopicId").
		Order("pr.LastUpdateTime desc").
		Limit(5).
		Where(" AreaCode =? and IsDelete=0 and TopicStatus =1", area).
		Find(&out).Error
	if err != nil {
		log.Errorf("GetTopicRecommend Db err:%v", err)
	}
	return
}

func (s *Posts) GetTopicDetail(ctx context.Context,
	topicId string) (detail *models.PostsTopic, statistics *models.PostsTopicStatistic, err error) {
	db := s.Mysql.Session().WithContext(ctx)
	err = db.Limit(1).
		Where(" TopicId = ? ", topicId).
		Find(&detail).Error
	if err != nil {
		log.Errorf("GetTopicDetail Db err:%v", err)
		return
	}
	err = db.Limit(1).
		Where(" TopicId = ? ", topicId).
		Find(&statistics).Error
	if err != nil {
		log.Errorf("GetTopicDetail statistics Db err:%v", err)
	}
	return
}

func (s *Posts) GetTopicLatesPosts(ctx context.Context,
	in *pb.GetTopicDetailRequest) (out []*models.Post, total int64, err error) {
	offset := (in.PageIndex - 1) * in.PageSize
	db := s.Mysql.Session().WithContext(ctx).
		Table("posts_topic_relation pr").
		Joins(" join posts ps on pr.PostsId=ps.PostsId").
		Where("ps.`Status` = 200 and pr.TopicId=?", in.TopicId)
	err = db.Count(&total).Error
	if err != nil {
		log.Errorf("GetTopicLatesPosts count Db err:%v", err)
	}
	err = db.
		Select("ps.*").
		Order("ps.PublicTime desc").
		Offset(int(offset)).
		Limit(int(in.PageSize)).
		Find(&out).
		Error
	if err != nil {
		log.Errorf("GetTopicLatesPosts Db err:%v", err)
	}
	return
}

func (s *Posts) GetTopicHotPosts(ctx context.Context,
	in *pb.GetTopicDetailRequest) (out []*models.Post, total int64, err error) {
	offset := (in.PageIndex - 1) * in.PageSize
	db := s.Mysql.Session().WithContext(ctx).
		Table("posts_topic_relation pr").
		Joins(" join posts ps on pr.PostsId=ps.PostsId").
		Joins(" left join dwd_posts_topic_relation_score sta on ps.PostsId=sta.PostsId").
		Where("ps.`Status` = 200 and pr.TopicId=?", in.TopicId)
	err = db.Count(&total).Error
	if err != nil {
		log.Errorf("GetTopicHotPosts count Db err:%v", err)
	}
	err = db.
		Select("ps.*,(sta.score) cnt").
		Order("cnt desc,ps.PublicTime desc").
		Offset(int(offset)).
		Limit(int(in.PageSize)).
		Find(&out).
		Error
	if err != nil {
		log.Errorf("GetTopicHotPosts Db err:%v", err)
	}
	return
}

// GetPostsTopicByPostsIds 根据帖子Id获取帖子话题
func (s *Posts) GetPostsTopicByPostsIds(ctx context.Context, postsIds []string, opts ...igorm.Option) ([]*models.PostsTopicRelation, error) {
	var out []*models.PostsTopicRelation
	err := s.Session(ctx, opts...).Raw("select b.* from posts_topic a INNER JOIN posts_topic_relation b on a.TopicId=b.TopicId where a.IsDelete=0 and a.TopicStatus=1 and b.postsId in ?", postsIds).Scan(&out).Error
	if err != nil {
		return nil, ormhelper.WrapErr(err)
	}
	return out, nil
}

// GetPostsUserRelationByPostsIds 根据帖子Id获取帖子@
func (s *Posts) GetPostsUserRelationByPostsIds(ctx context.Context, postsIds []string, opts ...igorm.Option) ([]*models.PostsUserRelation, error) {
	var out []*models.PostsUserRelation
	err := s.Session(ctx, opts...).Where(" postsId in ?", postsIds).Find(&out).Error
	if err != nil {
		return nil, ormhelper.WrapErr(err)
	}
	return out, nil
}

// 获取用户收藏帖
func (s *Posts) GetUserCollectTopicPageList(ctx context.Context, pageIndex, pageSize int, userId string, opts ...igorm.Option) ([]*models.PostsTopicCollectView, error) {
	var out []*models.PostsTopicCollectView
	err := s.Session(ctx, opts...).
		Raw("select a.TopicId,a.Content TopicName from  posts_topic a INNER JOIN posts_topic_collect b on a.TopicId=b.TopicId where b.userid=? and a.TopicStatus=1 and a.IsDelete=0 order by  b.CreatedAt desc limit ?,?", userId, pageSize*(pageIndex-1), pageSize).
		Scan(&out).Error
	if err != nil {
		return nil, ormhelper.WrapErr(err)
	}
	return out, nil
}

// GetTopicStatisticsList 获取帖子统计
func (s *Posts) GetTopicStatisticsList(ctx context.Context, topicIds []string, opts ...igorm.Option) ([]*models.PostsTopicStatistic, error) {
	var out []*models.PostsTopicStatistic
	err := s.Session(ctx, opts...).Where(" TopicId in ? ", topicIds).Find(&out).Error
	if err != nil {
		return nil, ormhelper.WrapErr(err)
	}
	return out, nil
}

func (s *Posts) GetTopicCollect(ctx context.Context,
	userId, topicId string) (out *models.PostsTopicCollect, err error) {
	err = s.Mysql.Session().WithContext(ctx).
		Limit(1).
		Where("TopicId = ? and UserId = ?", topicId, userId).
		Find(&out).Error
	if err != nil {
		log.Errorf("GetTopicCollect Db err:%v", err)
	}
	return
}

func (s *Posts) UpdateTopicViewCount(ctx context.Context, topicId string) (err error) {
	sql := `update posts_topic_statistics set  TopicViewCount=TopicViewCount+1,ViewCount=ViewCount+1,LastUpdateTime=? where topicId=?`
	now := time.Now().UTC()
	err = s.Mysql.Session().WithContext(ctx).
		Exec(sql, now, topicId).
		Error
	if err != nil {
		log.Errorf("UpdateTopicViewCount Db err:%v", err)
	}
	return
}

// GetTopicInfoByTopicIds 根据帖子Id获取帖子信息
func (s *Posts) GetTopicInfoByTopicIds(ctx context.Context, topicIds []string, opts ...igorm.Option) ([]*models.PostsTopic, error) {
	var out []*models.PostsTopic
	err := s.Session(ctx, opts...).Where(" TopicId in ? ", topicIds).Find(&out).Error
	if err != nil {
		return nil, ormhelper.WrapErr(err)
	}
	return out, nil
}

// GetPostsUsersApplaudCount 获取用户获赞
func (s *Posts) GetPostsUsersApplaudCount(ctx context.Context, userIds []string, opts ...igorm.Option) ([]*models.UserTopicStatisticView, error) {
	var out []*models.UserTopicStatisticView
	err := s.Session(ctx, opts...).
		Raw("select sum(a.ApplaudCount) ApplaudCount,b.userid UserId from posts_statistics a inner join posts b on a.postsid=b.postsid where b.userid in ? GROUP BY  b.userid ", userIds).
		Scan(&out).Error
	if err != nil {
		return nil, ormhelper.WrapErr(err)
	}
	return out, nil
}

// GetPostsUsersPostsCount 获取用户
func (s *Posts) GetPostsUsersPostsCount(ctx context.Context, userIds []string, opts ...igorm.Option) ([]*models.PostsUserCount, error) {
	var out []*models.PostsUserCount
	err := s.Session(ctx, opts...).
		Raw("select count(userid) PostsCount,UserId  from posts where userid in ? and status=200 GROUP BY userid", userIds).
		Scan(&out).Error
	if err != nil {
		return nil, ormhelper.WrapErr(err)
	}
	return out, nil
}

func (s *Posts) GetEnterpriseViewCount(ctx context.Context,
	codes []string, dt time.Time) (out map[string]int, err error) {
	para := []any{codes}
	sql := "select PostsUserId,count(Id) as total from posts_view_history where PostsUserId in ?"
	if dt.After(time.Time{}) {
		sql += " and CreateTime > ?"
		para = append(para, dt)
	}
	var res []*models.Result
	sql += " group by PostsUserId"
	err = s.Mysql.Session().
		Raw(sql, para...).
		Find(&res).
		Error
	if err != nil {
		log.Errorf("GetEnterpriseViewCount Db err:%v", err)
	}
	out = make(map[string]int)
	for _, v := range res {
		out[v.PostsUserId] = v.Total
	}
	return
}

func (s *Posts) GetEnterpriseApplaudCount(ctx context.Context,
	codes []string, dt time.Time) (out map[string]int, err error) {
	para := []any{codes}
	sql := "select PostsUserId,count(PostsId) as total from posts_applaudrecord where PostsUserId in ?"
	if dt.After(time.Time{}) {
		sql += " and CreateTime > ?"
		para = append(para, dt)
	}
	var res []*models.Result
	sql += " group by PostsUserId"
	err = s.Mysql.Session().
		Raw(sql, para...).
		Find(&res).
		Error
	if err != nil {
		log.Errorf("GetEnterpriseApplaudCount Db err:%v", err)
	}
	out = make(map[string]int)
	for _, v := range res {
		out[v.PostsUserId] = v.Total
	}
	return
}

func (s *Posts) GetConfig(ctx context.Context,
	keys []string) (out []*models.Config, err error) {
	err = s.Mysql.Session().WithContext(ctx).
		Where("config_type in ?", keys).
		Find(&out).Error
	if err != nil {
		log.Errorf("GetConfig Db err:%v", err)
	}
	return
}

// GetPostsTop //帖子置顶
func (s *Posts) GetPostsTop(ctx context.Context, userid, userloginId string, releaseType int32) ([]*models.PostsTop, error) {
	var out []*models.PostsTop
	if userid == userloginId { //如果查看自己的主页
		err := s.Mysql.Session().WithContext(ctx).Where(" UserId=?  and Status=1 and ReleaseType=? order by CreateTime desc", userid, releaseType).Find(&out).Error
		return out, err
	} else { //查看其他用户排除未审核的
		err := s.Mysql.Session().Raw("select a.* from posts_top a INNER JOIN posts b on a.PostsId=b.PostsId where a.userid=? and a.`Status`=1 and a.ReleaseType=? and b.`Status`=200 order by CreateTime desc", userid, releaseType).Scan(&out).Error
		return out, err
	}

}

// GetActivityPageList 活动分页     1. 进行中>未开始>已结束，状态相同取结束时间正序，结束时间相同，按开始时间正序排列；
func (s *Posts) GetActivityPageList(ctx context.Context, pageIndex, pageSize int, areaCode, languageCode string, opts ...igorm.Option) ([]*models.PostsActivity, error) {
	var out []*models.PostsActivity
	err := s.Session(ctx, opts...).
		Raw("select *, case WHEN StartTime>NOW() THEN 2 WHEN EndTime<NOW()  THEN 3 ELSE 1  end as StartStatus from posts_activity where status=1 and  LanguageCode=? and AreaCode=?   order by StartStatus, EndTime, StartTime limit ?,?", languageCode, areaCode, pageSize*(pageIndex-1), pageSize).Scan(&out).Error
	if err != nil {
		return nil, ormhelper.WrapErr(err)
	}
	return out, nil
}

// GetActivityIds 获取当期区域活动Id
func (s *Posts) GetActivityIds(ctx context.Context, areaCode, languageCode string, opts ...igorm.Option) []*string {
	var out []*string
	err := s.Session(ctx, opts...).
		Raw("select Id  from posts_activity where  status=1 and  LanguageCode=? and AreaCode=?", languageCode, areaCode).Scan(&out).Error
	if err != nil {
		return nil
	}
	return out
}

// GetActivityDetail 活动详情
func (s *Posts) GetActivityDetail(ctx context.Context, activityId string, opts ...igorm.Option) (*models.PostsActivity, error) {
	var out *models.PostsActivity
	err := s.Session(ctx, opts...).
		Raw("select *,case WHEN StartTime>NOW() THEN 2 WHEN EndTime<NOW()  THEN 3 ELSE 1  end as StartStatus from posts_activity where Id=? ", activityId).Scan(&out).Error
	if err != nil {
		return nil, ormhelper.WrapErr(err)
	}
	return out, nil
}

// GetActivityJoinUserIds 参与活动userid
func (s *Posts) GetActivityJoinUserIds(ctx context.Context, activityId string, opts ...igorm.Option) ([]string, error) {
	var out []string
	err := s.Session(ctx, opts...).
		Raw("select UserId from posts_activity_join where ActivityId=? order by CreateTime desc limit 6  ", activityId).Scan(out).Error
	if err != nil {
		return nil, ormhelper.WrapErr(err)
	}
	return out, nil
}

func (s *Posts) GetActivityJoinUsePhotos(ctx context.Context, activityId string, opts ...igorm.Option) ([]*string, error) {
	var out []*string
	err := s.Session(ctx, opts...).
		Raw("select AvatarAddress from posts_activity_join where ActivityId=? order by CreateTime desc limit 5  ", activityId).Scan(&out).Error
	if err != nil {
		return nil, ormhelper.WrapErr(err)
	}
	return out, nil
}

func (s *Posts) GetActivityPostsPageList(ctx context.Context, activityIds []string, pageIndex, pageSize int, opts ...igorm.Option) ([]*models.Post, error) {
	var out []*models.Post
	err := s.Session(ctx, opts...).
		Raw("select b.* from  posts_activity_posts a inner join posts b on a.postsId=b.postsid where a.ActivityId in ? and b.status=200 and b.ReleaseType=2 order by PublicTime desc limit ?,?", activityIds, pageSize*(pageIndex-1), pageSize).Scan(&out).Error
	if err != nil {
		return nil, ormhelper.WrapErr(err)
	}
	return out, nil
}

// GetActivityJoinUserExist用户是否参数
func (s *Posts) GetActivityJoinUserExist(ctx context.Context, activityId, userid string, opts ...igorm.Option) (bool, error) {
	var out *[]string
	err := s.Session(ctx, opts...).
		Raw("select UserId from posts_activity_join where ActivityId=? and userid=?   ", activityId, userid).Scan(&out).Error
	if err != nil {
		return false, ormhelper.WrapErr(err)
	}
	return out != nil, nil
}

// JoinActivity 参与用户
func (s *Posts) JoinActivity(ctx context.Context, userid, activityId string, avatarAddress string, opts ...igorm.Option) error {

	isjoin, _ := s.GetActivityJoinUserExist(ctx, activityId, userid)
	var num = strconv.Itoa(utilstool.Random(3, 10))
	if len(userid) > 0 {
		if !isjoin {
			s.Session(ctx, opts...).Exec("update posts_activity set JoinUserCount=JoinUserCount+1,JoinUserCountShow=JoinUserCountShow+"+num+" where id=?", activityId)
			userjoin := models.PostsActivityJoin{
				UserID:        userid,
				ActivityID:    activityId,
				CreateTime:    time.Now().UTC(),
				ID:            utilstool.GetNumber("AJU"),
				AvatarAddress: avatarAddress,
			}
			s.Session(ctx, opts...).Create(userjoin)
		} else {
			s.Session(ctx, opts...).Exec("update posts_activity set JoinUserCount=JoinUserCount+1 where id=?", activityId)
		}
	} else {
		s.Session(ctx, opts...).Exec("update posts_activity set JoinUserCount=JoinUserCount+1,JoinUserCountShow=JoinUserCountShow+"+num+" where id=?", activityId)
	}
	return nil

}

func (s *Posts) GetTopicsPosts(ctx context.Context,
	topicIds []string, pageIndex, pageSize int32) (out []*models.Post, err error) {
	offset := (pageIndex - 1) * pageSize
	db := s.Mysql.Session().WithContext(ctx).
		Table("posts_topic_relation pr").
		Joins(" join posts ps on pr.PostsId=ps.PostsId").
		Joins(" left join dwd_posts_topic_relation_score sta on ps.PostsId=sta.PostsId").
		Where("ps.`Status` = 200 and pr.TopicId in ?", topicIds)
	err = db.
		Select("ps.*,(sta.score) cnt").
		Order("cnt desc,ps.PublicTime desc").
		Offset(int(offset)).
		Limit(int(pageSize)).
		Find(&out).
		Error
	if err != nil {
		log.Errorf("GetTopicsPosts Db err:%v", err)
	}
	return
}

// 获取活动内容话题失效话题
func (s *Posts) GetActivityTopicDisabled(ctx context.Context, activityId string, opts ...igorm.Option) (out []*string, err error) {
	var result []*string
	err = s.Session(ctx, opts...).Raw("select a.TopicId from posts_activity_topic a INNER JOIN  posts_topic b on a.TopicId=b.TopicId  where a.ActivityId=? and (b.IsDelete=1 or  b.TopicStatus=0)", activityId).Scan(&result).Error
	return result, err
}

func (s *Posts) GetTopicNameByTopicId(ctx context.Context, topicId string, opts ...igorm.Option) (out string, err error) {
	var result *models.PostsTopic
	err = s.Session(ctx, opts...).Where("TopicId=?", topicId).First(&result).Error
	if err != nil {
		return "", err
	}
	if result == nil {
		return "", err
	}
	return result.Content, err
}
